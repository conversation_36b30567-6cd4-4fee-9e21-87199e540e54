'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/hooks/useAuth'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import { 
  Check, 
  Crown, 
  Zap, 
  Shield, 
  Sparkles,
  Users,
  Clock,
  HeadphonesIcon,
  Code,
  Download
} from 'lucide-react'

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { user } = useAuth()
  const router = useRouter()

  const features = {
    free: [
      { icon: Zap, text: "50 compressions per month", highlight: true },
      { icon: Download, text: "10MB max file size" },
      { icon: Shield, text: "PNG, JPEG, WebP support" },
      { icon: Clock, text: "Basic compression speed" },
    ],
    pro: [
      { icon: Sparkles, text: "Unlimited compressions", highlight: true },
      { icon: Download, text: "200MB max file size", highlight: true },
      { icon: Shield, text: "All formats (PNG, JPEG, WebP, GIF, Video)" },
      { icon: Zap, text: "Advanced compression algorithms" },
      { icon: Users, text: "Batch processing up to 100 files" },
      { icon: Code, text: "API access with 10,000 calls/month" },
      { icon: HeadphonesIcon, text: "Priority email support" },
      { icon: Crown, text: "Format conversion (PNG↔JPEG↔WebP)" },
    ]
  }

  const proPrice = isAnnual ? 7 : 9
  const proPriceOriginal = isAnnual ? 10 : 12

  // Handle free trial start
  const handleStartTrial = async () => {
    if (!user) {
      // Redirect to login/signup
      router.push('/?auth=signup')
      return
    }

    // Check if user has already used trial
    if (user.trial_used) {
      toast.error('You have already used your free trial. Please subscribe to continue.')
      return
    }

    // Check if user is already on trial or pro
    if (user.membership_level === 'pro') {
      toast('You already have Pro access!', { icon: 'ℹ️' })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/subscription/create-trial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        toast.success('🎉 Your 14-day free trial has started!')
        // Refresh user data
        window.location.reload()
      } else {
        toast.error(data.error || 'Failed to start trial')
      }
    } catch (error) {
      console.error('Trial start error:', error)
      toast.error('Failed to start trial. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle subscription checkout
  const handleSubscribe = async () => {
    if (!user) {
      // Redirect to login/signup
      router.push('/?auth=signup')
      return
    }

    setIsLoading(true)
    try {
      const priceId = isAnnual
        ? process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_YEARLY
        : process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY

      const response = await fetch('/api/subscription/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          isAnnual,
        }),
      })

      const data = await response.json()

      if (data.success && data.checkout_url) {
        // Redirect to Stripe checkout
        window.location.href = data.checkout_url
      } else {
        toast.error(data.error || 'Failed to create checkout session')
      }
    } catch (error) {
      console.error('Checkout error:', error)
      toast.error('Failed to start checkout. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Determine what to show on the Pro button
  const getProButtonConfig = () => {
    if (!user) {
      return {
        text: 'Start 14-Day Free Trial',
        onClick: handleStartTrial,
        disabled: false
      }
    }

    if (user.membership_level === 'pro') {
      if (user.membership_status === 'trial') {
        return {
          text: 'Subscribe Now',
          onClick: handleSubscribe,
          disabled: false
        }
      } else {
        return {
          text: 'Current Plan',
          onClick: () => {},
          disabled: true
        }
      }
    }

    if (user.trial_used) {
      return {
        text: 'Subscribe Now',
        onClick: handleSubscribe,
        disabled: false
      }
    }

    return {
      text: 'Start 14-Day Free Trial',
      onClick: handleStartTrial,
      disabled: false
    }
  }

  const proButtonConfig = getProButtonConfig()

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">

      <main className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Simple, <span className="text-blue-600 dark:text-blue-400">Affordable</span> Pricing
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Get more for less. Our Pro plan offers better value than competitors with
            more free compressions and lower prices.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-8">
            <span className={`mr-3 ${!isAnnual ? 'text-gray-900 dark:text-gray-100 font-semibold' : 'text-gray-500 dark:text-gray-400'}`}>
              Monthly
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isAnnual ? 'bg-blue-600 dark:bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 ${isAnnual ? 'text-gray-900 dark:text-gray-100 font-semibold' : 'text-gray-500 dark:text-gray-400'}`}>
              Annual
            </span>
            {isAnnual && (
              <span className="ml-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs font-semibold px-2 py-1 rounded-full">
                Save 22%
              </span>
            )}
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            
            {/* Free Plan */}
            <motion.div
              className="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-8 border border-gray-200 dark:border-gray-700 relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-8 h-8 text-gray-600 dark:text-gray-300" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Free</h3>
                <div className="text-5xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  $0
                  <span className="text-lg font-normal text-gray-600 dark:text-gray-400">/month</span>
                </div>
                <p className="text-gray-600 dark:text-gray-300">Perfect for trying out our service</p>
              </div>

              <ul className="space-y-4 mb-8">
                {features.free.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-0.5 ${
                      feature.highlight ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      <Check className={`w-4 h-4 ${feature.highlight ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}`} />
                    </div>
                    <span className={`text-gray-700 dark:text-gray-300 ${feature.highlight ? 'font-semibold' : ''}`}>
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>

              <button className="w-full bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105">
                Get Started Free
              </button>

              <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">
                No credit card required
              </p>
            </motion.div>

            {/* Pro Plan */}
            <motion.div
              className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl p-8 border-2 border-blue-500 dark:border-blue-400 relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {/* Popular Badge */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-500 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center">
                  <Crown className="w-4 h-4 mr-2" />
                  Most Popular
                </div>
              </div>

              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Crown className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Pro</h3>
                <div className="flex items-center justify-center mb-2">
                  <span className="text-2xl text-gray-400 dark:text-gray-500 line-through mr-2">
                    ${proPriceOriginal}
                  </span>
                  <span className="text-5xl font-bold text-gray-900 dark:text-gray-100">
                    ${proPrice}
                  </span>
                  <span className="text-lg font-normal text-gray-600 dark:text-gray-400 ml-1">
                    /{isAnnual ? 'month' : 'month'}
                  </span>
                </div>
                {isAnnual && (
                  <p className="text-sm text-green-600 dark:text-green-400 font-semibold mb-2">
                    Billed annually - Save $36/year
                  </p>
                )}
                <p className="text-gray-600 dark:text-gray-300">For professionals and businesses</p>
              </div>

              <ul className="space-y-4 mb-8">
                {features.pro.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-0.5 ${
                      feature.highlight ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      <Check className={`w-4 h-4 ${feature.highlight ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}`} />
                    </div>
                    <span className={`text-gray-700 dark:text-gray-300 ${feature.highlight ? 'font-semibold' : ''}`}>
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>

              <button
                onClick={proButtonConfig.onClick}
                disabled={proButtonConfig.disabled || isLoading}
                className={`w-full font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg ${
                  proButtonConfig.disabled
                    ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-500 dark:to-purple-500 hover:from-blue-700 hover:to-purple-700 dark:hover:from-blue-600 dark:hover:to-purple-600'
                } text-white`}
              >
                {isLoading ? 'Loading...' : proButtonConfig.text}
              </button>

              <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">
                No credit card required • Cancel anytime
              </p>
            </motion.div>
          </div>
        </div>

        {/* Comparison with Competitors */}
        <motion.div
          className="max-w-4xl mx-auto mt-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Why Choose Us Over Competitors?
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              Compare our Pro plan with leading competitors
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Feature</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-blue-600 dark:text-blue-400">Our Pro</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-600 dark:text-gray-400">Tinify Pro</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-600 dark:text-gray-400">Others</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">Monthly Price</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600 dark:text-blue-400">$7-9</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">$25</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">$15-30</td>
                  </tr>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">Free Monthly Compressions</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600 dark:text-blue-400">50</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">20</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">10-20</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">Max File Size</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600 dark:text-blue-400">200MB</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">75MB</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">50-100MB</td>
                  </tr>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">Video Support</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600 dark:text-blue-400">✓</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">✗</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">Limited</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">API Calls/Month</td>
                    <td className="px-6 py-4 text-center text-sm font-semibold text-blue-600 dark:text-blue-400">10,000</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">500</td>
                    <td className="px-6 py-4 text-center text-sm text-gray-600 dark:text-gray-400">1,000-5,000</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          className="max-w-4xl mx-auto mt-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-gray-100 mb-12">
            Frequently Asked Questions
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers. All payments are processed securely.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                Can I cancel my subscription anytime?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Yes, you can cancel anytime with no questions asked. You&apos;ll keep access to Pro features until your current billing period ends.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                Is there really a free trial?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Yes! Get 14 days of full Pro access with no credit card required. Experience unlimited compressions and all premium features.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                How does the API work?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Our RESTful API lets you integrate compression into your apps. Pro users get 10,000 API calls per month with detailed documentation.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                Do you offer team discounts?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Yes! Teams of 5+ users get 15% off, and enterprise customers get custom pricing. Contact us for volume discounts.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">
                What&apos;s your refund policy?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                We offer a 30-day money-back guarantee. If you&apos;re not satisfied, we&apos;ll refund your payment in full, no questions asked.
              </p>
            </div>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white">
            <h2 className="text-3xl font-bold mb-4">
              Ready to compress like a pro?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of users who trust us with their compression needs
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={proButtonConfig.onClick}
                disabled={proButtonConfig.disabled || isLoading}
                className={`font-semibold py-3 px-8 rounded-xl transition-colors ${
                  proButtonConfig.disabled
                    ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                    : 'bg-white text-blue-600 hover:bg-gray-100'
                }`}
              >
                {isLoading ? 'Loading...' : proButtonConfig.text}
              </button>
              <button
                onClick={() => router.push('/')}
                className="border-2 border-white text-white font-semibold py-3 px-8 rounded-xl hover:bg-white hover:text-blue-600 transition-colors"
              >
                View Demo
              </button>
            </div>
          </div>
        </motion.div>
      </main>

    </div>
  )
}
