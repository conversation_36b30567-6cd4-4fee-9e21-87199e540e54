'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { TrendingUp, Users, FileImage, Zap } from 'lucide-react'

const stats = [
  {
    icon: FileImage,
    value: 1000000,
    suffix: '+',
    label: 'Files Compressed',
    description: 'Images and videos processed',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: TrendingUp,
    value: 90,
    suffix: '%',
    label: 'Size Reduction',
    description: 'Average compression ratio',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: Users,
    value: 50000,
    suffix: '+',
    label: 'Happy Users',
    description: 'Developers and creators',
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: Zap,
    value: 3,
    suffix: 's',
    label: 'Average Speed',
    description: 'Processing time per file',
    color: 'from-yellow-500 to-orange-500'
  }
]

// Counter animation hook
function useCounter(end: number, duration: number = 2000, start: number = 0) {
  const [count, setCount] = useState(end) // Start with end value for SSR
  const [isVisible, setIsVisible] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    setCount(start) // Reset to start value after mount
  }, [start])

  useEffect(() => {
    if (!isVisible || !isMounted) return

    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)

      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentCount = Math.floor(easeOutQuart * (end - start) + start)

      setCount(currentCount)

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [end, duration, start, isVisible, isMounted])

  return { count, setIsVisible }
}

function StatCard({ stat, index }: { stat: typeof stats[0]; index: number }) {
  const { count, setIsVisible } = useCounter(stat.value, 2000)
  const Icon = stat.icon

  return (
    <motion.div
      className="relative group"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ 
        opacity: 1, 
        y: 0,
        transition: { 
          duration: 0.6, 
          delay: index * 0.1,
          onComplete: () => setIsVisible(true)
        }
      }}
      viewport={{ once: true }}
    >
      <div className="relative p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
        {/* Background gradient on hover */}
        <div className={`
          absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 
          transition-opacity duration-300 rounded-2xl
        `} />
        
        {/* Icon */}
        <div className={`
          w-16 h-16 rounded-xl bg-gradient-to-br ${stat.color} 
          flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300
        `}>
          <Icon className="w-8 h-8 text-white" />
        </div>

        {/* Stats */}
        <div className="relative">
          <div className="flex items-baseline gap-1 mb-2">
            <motion.span 
              className="text-4xl font-bold text-gray-900 dark:text-gray-100"
              key={count}
              initial={{ scale: 1.1 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.2 }}
            >
              {count.toLocaleString()}
            </motion.span>
            <span className={`text-2xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}>
              {stat.suffix}
            </span>
          </div>
          
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">
            {stat.label}
          </h3>

          <p className="text-sm text-gray-600 dark:text-gray-400">
            {stat.description}
          </p>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-4 right-4 w-2 h-2 rounded-full bg-gray-200 dark:bg-gray-600 group-hover:bg-gray-300 dark:group-hover:bg-gray-500 transition-colors duration-300" />
        <div className="absolute bottom-4 right-4 w-1 h-1 rounded-full bg-gray-200 dark:bg-gray-600 group-hover:bg-gray-300 dark:group-hover:bg-gray-500 transition-colors duration-300" />
      </div>
    </motion.div>
  )
}

export function Stats() {
  return (
    <section className="py-20 px-4 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Subtle geometric shapes */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-100 dark:bg-blue-900 rounded-full opacity-20 blur-xl" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-100 dark:bg-purple-900 rounded-full opacity-20 blur-xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full opacity-30 blur-2xl" />
      </div>

      <div className="max-w-7xl mx-auto relative">
        {/* Section header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2 
            className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Trusted by <span className="text-blue-600 dark:text-blue-400">Thousands</span>
          </motion.h2>
          <motion.p 
            className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Join the growing community of developers, designers, and content creators who rely on CompressHub for their optimization needs.
          </motion.p>
        </motion.div>

        {/* Stats grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <StatCard key={index} stat={stat} index={index} />
          ))}
        </div>

        {/* Bottom section */}
        <motion.div 
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-100">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium text-gray-700">
              Live stats updated in real-time
            </span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
