'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import {
  RotateCw,
  RotateCcw,
  FlipHorizontal,
  FlipVertical,
  Crop,
  Download,
  Undo,
  Redo,
  Sliders,
  Type,
  Square,
  Circle,
  Palette,
  Save,
  Droplets
} from 'lucide-react'
import toast from 'react-hot-toast'
import { WatermarkSettings, WatermarkConfig, defaultWatermarkConfig, applyWatermarkToCanvas } from './WatermarkSettings'
import { memoryManager, PerformanceMonitor, createManagedURL, createManagedBlob } from '@/utils/memoryManager'

interface ImageEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

interface FilterSettings {
  brightness: number
  contrast: number
  saturation: number
  blur: number
  sepia: number
  grayscale: number
}

export function ImageEditor({ file, onEditComplete }: ImageEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isMounted, setIsMounted] = useState(false)
  const [fabricLoaded, setFabricLoaded] = useState(false)
  const [activeTab, setActiveTab] = useState<'basic' | 'filters' | 'text' | 'shapes' | 'watermark'>('basic')
  const [filters, setFilters] = useState<FilterSettings>({
    brightness: 0,
    contrast: 0,
    saturation: 0,
    blur: 0,
    sepia: 0,
    grayscale: 0
  })
  const [history, setHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  const [originalImage, setOriginalImage] = useState<any>(null)
  const [watermarkConfig, setWatermarkConfig] = useState<WatermarkConfig>(defaultWatermarkConfig)
  const [managedResources, setManagedResources] = useState<string[]>([])

  // Ensure component is mounted on client side
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Load Fabric.js dynamically
  useEffect(() => {
    if (isMounted) {
      import('fabric').then((fabricModule) => {
        console.log('Fabric.js loaded successfully')
        setFabricLoaded(true)
      }).catch((error) => {
        console.error('Failed to load Fabric.js:', error)
        toast.error('Failed to load image editor')
      })
    }
  }, [isMounted])

  // Initialize canvas with image
  useEffect(() => {
    console.log('ImageEditor useEffect triggered with file:', file)
    if (!isMounted || !fabricLoaded || !canvasRef.current) {
      console.log('Not ready:', { isMounted, fabricLoaded, canvasRef: !!canvasRef.current })
      return
    }

    const initializeCanvas = async () => {
      try {
        const { fabric } = await import('fabric')
        console.log('Creating fabric canvas')

        const canvas = new fabric.Canvas(canvasRef.current!, {
          width: 800,
          height: 600,
          backgroundColor: '#ffffff'
        })

        fabricCanvasRef.current = canvas
        console.log('Fabric canvas created')

        // Load image
        const reader = new FileReader()
        reader.onload = (e) => {
          console.log('FileReader onload triggered')
          const imgUrl = e.target?.result as string
          console.log('Image URL created, length:', imgUrl.length)

          fabric.Image.fromURL(imgUrl, (img) => {
            console.log('fabric.Image.fromURL callback triggered', img)
            try {
              // Check if image loaded successfully
              if (!img || !img.width || !img.height) {
                throw new Error('Invalid image data')
              }

              console.log('Image dimensions:', img.width, 'x', img.height)

              // Scale image to fit canvas
              const canvasWidth = 800
              const canvasHeight = 600
              const imgWidth = img.width!
              const imgHeight = img.height!

              const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight)
              console.log('Calculated scale:', scale)

              img.scale(scale)
              img.set({
                left: (canvasWidth - imgWidth * scale) / 2,
                top: (canvasHeight - imgHeight * scale) / 2,
                selectable: false
              })

              canvas.add(img)
              canvas.renderAll()
              setOriginalImage(img)
              setIsLoading(false)
              console.log('Image loaded successfully, loading set to false')

              // Save initial state
              saveToHistory()
            } catch (error) {
              console.error('Error loading image:', error)
              setIsLoading(false)
              toast.error('Failed to load image')
            }
          }, {
            crossOrigin: 'anonymous'
          })
        }

        reader.onerror = () => {
          console.error('Error reading file')
          setIsLoading(false)
          toast.error('Failed to read image file')
        }

        console.log('Starting to read file as data URL')
        reader.readAsDataURL(file)

      } catch (error) {
        console.error('Error initializing canvas:', error)
        setIsLoading(false)
        toast.error('Failed to initialize image editor')
      }
    }

    initializeCanvas()

    return () => {
      console.log('Cleaning up canvas')
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose()
      }
      // Clean up managed resources
      managedResources.forEach(id => memoryManager.release(id))
    }
  }, [file, isMounted, fabricLoaded])

  const saveToHistory = useCallback(() => {
    if (!fabricCanvasRef.current) return
    
    const state = JSON.stringify(fabricCanvasRef.current.toJSON())
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1)
      newHistory.push(state)
      return newHistory
    })
    setHistoryIndex(prev => prev + 1)
  }, [historyIndex])

  const undo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      const state = history[newIndex]
      fabricCanvasRef.current?.loadFromJSON(state, () => {
        fabricCanvasRef.current?.renderAll()
        setHistoryIndex(newIndex)
      })
    }
  }

  const redo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      const state = history[newIndex]
      fabricCanvasRef.current?.loadFromJSON(state, () => {
        fabricCanvasRef.current?.renderAll()
        setHistoryIndex(newIndex)
      })
    }
  }

  const rotateImage = (angle: number) => {
    if (!originalImage || !fabricCanvasRef.current) return
    
    originalImage.rotate(originalImage.angle! + angle)
    fabricCanvasRef.current.renderAll()
    saveToHistory()
  }

  const flipImage = (direction: 'horizontal' | 'vertical') => {
    if (!originalImage || !fabricCanvasRef.current) return
    
    if (direction === 'horizontal') {
      originalImage.set('flipX', !originalImage.flipX)
    } else {
      originalImage.set('flipY', !originalImage.flipY)
    }
    
    fabricCanvasRef.current.renderAll()
    saveToHistory()
  }

  const applyFilters = useCallback(() => {
    if (!originalImage || !fabricCanvasRef.current) return

    const filterArray: fabric.IBaseFilter[] = []

    if (filters.brightness !== 0) {
      filterArray.push(new fabric.Image.filters.Brightness({ brightness: filters.brightness / 100 }))
    }
    
    if (filters.contrast !== 0) {
      filterArray.push(new fabric.Image.filters.Contrast({ contrast: filters.contrast / 100 }))
    }
    
    if (filters.saturation !== 0) {
      filterArray.push(new fabric.Image.filters.Saturation({ saturation: filters.saturation / 100 }))
    }
    
    if (filters.blur > 0) {
      filterArray.push(new fabric.Image.filters.Blur({ blur: filters.blur / 100 }))
    }
    
    if (filters.sepia > 0) {
      filterArray.push(new fabric.Image.filters.Sepia())
    }
    
    if (filters.grayscale > 0) {
      filterArray.push(new fabric.Image.filters.Grayscale())
    }

    originalImage.filters = filterArray
    originalImage.applyFilters()
    fabricCanvasRef.current.renderAll()
  }, [filters, originalImage])

  useEffect(() => {
    applyFilters()
  }, [applyFilters])

  const addText = () => {
    if (!fabricCanvasRef.current) return
    
    const text = new fabric.IText('Click to edit text', {
      left: 100,
      top: 100,
      fontFamily: 'Arial',
      fontSize: 24,
      fill: '#000000'
    })
    
    fabricCanvasRef.current.add(text)
    fabricCanvasRef.current.setActiveObject(text)
    saveToHistory()
  }

  const addShape = (type: 'rectangle' | 'circle') => {
    if (!fabricCanvasRef.current) return
    
    let shape: fabric.Object
    
    if (type === 'rectangle') {
      shape = new fabric.Rect({
        left: 100,
        top: 100,
        width: 100,
        height: 100,
        fill: 'rgba(255, 0, 0, 0.5)',
        stroke: '#ff0000',
        strokeWidth: 2
      })
    } else {
      shape = new fabric.Circle({
        left: 100,
        top: 100,
        radius: 50,
        fill: 'rgba(0, 255, 0, 0.5)',
        stroke: '#00ff00',
        strokeWidth: 2
      })
    }
    
    fabricCanvasRef.current.add(shape)
    fabricCanvasRef.current.setActiveObject(shape)
    saveToHistory()
  }

  const exportImage = async (format: 'png' | 'jpeg' | 'webp' = 'png') => {
    if (!fabricCanvasRef.current) return

    try {
      await PerformanceMonitor.measureAsync('image-export', async () => {
        // Create a temporary canvas for export with watermark
        const tempCanvas = document.createElement('canvas')
        const tempCtx = tempCanvas.getContext('2d')
        if (!tempCtx) return

        tempCanvas.width = fabricCanvasRef.current!.width!
        tempCanvas.height = fabricCanvasRef.current!.height!

        // Register canvas for cleanup
        const canvasId = memoryManager.register(
          `temp-canvas-${Date.now()}`,
          tempCanvas,
          'canvas'
        )

        // Draw the fabric canvas content
        const fabricDataURL = fabricCanvasRef.current!.toDataURL({
          format: 'png',
          quality: 1
        })

        const img = new Image()
        const imgId = memoryManager.register(
          `temp-image-${Date.now()}`,
          img,
          'image'
        )

        img.onload = async () => {
          try {
            tempCtx.drawImage(img, 0, 0)

            // Apply watermark if enabled
            if (watermarkConfig.enabled) {
              applyWatermarkToCanvas(tempCanvas, watermarkConfig)
            }

            // Convert to final format
            const finalDataURL = tempCanvas.toDataURL(`image/${format}`, 0.9)
            const response = await fetch(finalDataURL)
            const blob = await response.blob()

            // Create managed blob
            const { blob: managedBlob, id: blobId } = createManagedBlob([blob])
            setManagedResources(prev => [...prev, blobId])

            onEditComplete(managedBlob)

            // Clean up temporary resources
            memoryManager.release(canvasId)
            memoryManager.release(imgId)
          } catch (error) {
            console.error('Export processing failed:', error)
            toast.error('Failed to process exported image')
          }
        }

        img.onerror = () => {
          memoryManager.release(canvasId)
          memoryManager.release(imgId)
          toast.error('Failed to load image for export')
        }

        img.src = fabricDataURL
      })
    } catch (error) {
      console.error('Export failed:', error)
      toast.error('Failed to export image')
    }
  }

  const resetFilters = () => {
    setFilters({
      brightness: 0,
      contrast: 0,
      saturation: 0,
      blur: 0,
      sepia: 0,
      grayscale: 0
    })
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* Toolbar */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <button
              onClick={undo}
              disabled={historyIndex <= 0}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Undo className="w-4 h-4" />
            </button>
            <button
              onClick={redo}
              disabled={historyIndex >= history.length - 1}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Redo className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => exportImage('png')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Save</span>
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {[
            { id: 'basic', label: 'Basic', icon: RotateCw },
            { id: 'filters', label: 'Filters', icon: Sliders },
            { id: 'text', label: 'Text', icon: Type },
            { id: 'shapes', label: 'Shapes', icon: Square },
            { id: 'watermark', label: 'Watermark', icon: Droplets }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="flex">
        {/* Canvas */}
        <div className="flex-1 p-4">
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
            {isMounted ? (
              <canvas ref={canvasRef} />
            ) : (
              <div className="w-full h-96 flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                <div className="text-gray-500 dark:text-gray-400">Initializing canvas...</div>
              </div>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="w-80 border-l border-gray-200 dark:border-gray-700 p-4">
          {activeTab === 'basic' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Basic Tools</h3>
              
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => rotateImage(90)}
                  className="flex items-center justify-center space-x-2 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <RotateCw className="w-4 h-4" />
                  <span className="text-sm">Rotate Right</span>
                </button>
                <button
                  onClick={() => rotateImage(-90)}
                  className="flex items-center justify-center space-x-2 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <RotateCcw className="w-4 h-4" />
                  <span className="text-sm">Rotate Left</span>
                </button>
                <button
                  onClick={() => flipImage('horizontal')}
                  className="flex items-center justify-center space-x-2 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <FlipHorizontal className="w-4 h-4" />
                  <span className="text-sm">Flip H</span>
                </button>
                <button
                  onClick={() => flipImage('vertical')}
                  className="flex items-center justify-center space-x-2 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <FlipVertical className="w-4 h-4" />
                  <span className="text-sm">Flip V</span>
                </button>
              </div>
            </div>
          )}

          {activeTab === 'filters' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Filters</h3>
                <button
                  onClick={resetFilters}
                  className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Reset
                </button>
              </div>
              
              {Object.entries(filters).map(([key, value]) => (
                <div key={key}>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize">
                    {key}: {value}
                  </label>
                  <input
                    type="range"
                    min={key === 'blur' ? 0 : -100}
                    max={100}
                    value={value}
                    onChange={(e) => setFilters(prev => ({ ...prev, [key]: Number(e.target.value) }))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                </div>
              ))}
            </div>
          )}

          {activeTab === 'text' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Text Tools</h3>
              
              <button
                onClick={addText}
                className="w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Type className="w-4 h-4" />
                <span>Add Text</span>
              </button>
            </div>
          )}

          {activeTab === 'shapes' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Shapes</h3>

              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => addShape('rectangle')}
                  className="flex items-center justify-center space-x-2 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <Square className="w-4 h-4" />
                  <span className="text-sm">Rectangle</span>
                </button>
                <button
                  onClick={() => addShape('circle')}
                  className="flex items-center justify-center space-x-2 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <Circle className="w-4 h-4" />
                  <span className="text-sm">Circle</span>
                </button>
              </div>
            </div>
          )}

          {activeTab === 'watermark' && (
            <WatermarkSettings
              config={watermarkConfig}
              onChange={setWatermarkConfig}
            />
          )}
        </div>
      </div>
    </div>
  )
}
