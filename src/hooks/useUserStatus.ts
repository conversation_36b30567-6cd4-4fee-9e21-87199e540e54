import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useUserEvents } from '@/utils/userEvents'

/**
 * Hook for components that need real-time user status updates
 * Particularly useful for UserMenu and other UI components that display user info
 */
export function useUserStatus() {
  const { user, loading } = useAuth()
  const [displayUser, setDisplayUser] = useState(user)

  // Update display user when auth user changes
  useEffect(() => {
    setDisplayUser(user)
  }, [user])

  // Listen for real-time points updates
  useUserEvents('points_updated', (event) => {
    if (displayUser && event.data.points !== undefined) {
      setDisplayUser({
        ...displayUser,
        points: event.data.points
      })
    }
  }, [displayUser])

  // Listen for membership updates
  useUserEvents('membership_updated', (event) => {
    if (displayUser && event.data.membership_level) {
      setDisplayUser({
        ...displayUser,
        membership_level: event.data.membership_level as any,
        membership_status: event.data.membership_status as any || displayUser.membership_status
      })
    }
  }, [displayUser])

  // Calculate derived properties
  const isProUser = displayUser?.membership_level === 'pro'
  const isTrialUser = displayUser?.membership_status === 'trial'
  const isActiveUser = displayUser?.membership_status === 'active'
  const isFreeUser = displayUser?.membership_level === 'free'

  // Calculate membership days left
  const membershipDaysLeft = displayUser?.membership_end_date 
    ? Math.max(0, Math.ceil((new Date(displayUser.membership_end_date).getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
    : 0

  // Check if membership is active (includes trial and active states)
  const isMembershipActive = displayUser?.membership_end_date
    ? new Date(displayUser.membership_end_date) > new Date() &&
      (displayUser.membership_status === 'active' || displayUser.membership_status === 'trial')
    : false

  return {
    user: displayUser,
    loading,
    isProUser,
    isTrialUser,
    isActiveUser,
    isFreeUser,
    membershipDaysLeft,
    isMembershipActive,
    // Computed display values
    displayPoints: displayUser?.points || 0,
    displayMembershipLevel: displayUser?.membership_level || 'free',
    displayMembershipStatus: displayUser?.membership_status || 'free'
  }
}
