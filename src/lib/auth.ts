import { cookies } from 'next/headers'
import { userOperations } from './database'
import { supabaseAdmin } from './supabase'
import { User } from '@/types/database'

export interface AuthUser extends User {
  is_membership_active: boolean
  membership_days_left: number
}

// Cookie management
export const authCookies = {
  async setUserSession(email: string): Promise<void> {
    const cookieStore = await cookies()
    cookieStore.set('user_email', email, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })
  },

  async getUserSession(): Promise<string | null> {
    const cookieStore = await cookies()
    const userEmail = cookieStore.get('user_email')
    return userEmail?.value || null
  },

  async clearUserSession(): Promise<void> {
    const cookieStore = await cookies()
    cookieStore.delete('user_email')
  }
}

// Authentication functions
export const auth = {
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const email = await authCookies.getUserSession()
      if (!email) {
        console.log('No user session found')
        return null
      }

      console.log('Getting user for email:', email)

      // Get user directly from Supabase to avoid fetch issues
      const { data: user, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('email', email)
        .maybeSingle()

      if (error) {
        console.error('Error fetching current user:', error)
        return null
      }

      if (!user) {
        console.log('User not found in database for email:', email)
        // Clear invalid session
        await authCookies.clearUserSession()
        return null
      }

      // Calculate membership status
      const now = new Date()
      const endDate = user.membership_end_date ? new Date(user.membership_end_date) : null
      const isMembershipExpired = endDate ? now > endDate : true

      // Check if membership is active (includes trial and active states)
      const is_membership_active = !isMembershipExpired &&
        (user.membership_status === 'active' || user.membership_status === 'trial')

      const membership_days_left = endDate
        ? Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)))
        : 0

      return {
        ...user,
        is_membership_active,
        membership_days_left
      }
    } catch (error) {
      console.error('Get current user error:', error)
      return null
    }
  },

  async signIn(email: string, userInfo?: { name?: string; avatar_url?: string }): Promise<{ success: boolean; user?: AuthUser; error?: string }> {
    try {
      // Check if user exists
      let user = await userOperations.getUserByEmail(email)

      if (!user) {
        console.log('Creating new user for email:', email)
        // Create new user with additional info if provided
        const userData = {
          email,
          username: userInfo?.name || email.split('@')[0],
          avatar_url: userInfo?.avatar_url || null,
          points: 50, // Give new users 50 free compressions
          membership_status: 'free' as const,
          membership_level: 'free' as const,
          register_source: 'google_oauth',
          last_login_time: new Date().toISOString()
        }

        user = await userOperations.createUser(userData)

        if (!user) {
          console.error('Failed to create user account')
          return { success: false, error: 'Failed to create user account' }
        }

        console.log('Successfully created new user:', user.id)
      } else {
        console.log('Existing user found, updating login time')
        // Update last login time and avatar if provided
        const updateData: any = {
          last_login_time: new Date().toISOString()
        }

        if (userInfo?.avatar_url && userInfo.avatar_url !== user.avatar_url) {
          updateData.avatar_url = userInfo.avatar_url
        }

        if (userInfo?.name && userInfo.name !== user.username) {
          updateData.username = userInfo.name
        }

        await userOperations.updateUser(user.id, updateData)
      }

      // Set session cookie
      await authCookies.setUserSession(email)

      // Get user with membership status
      const authUser = await userOperations.getUserWithMembershipStatus(email)
      if (!authUser) {
        console.error('Failed to get user data after sign in')
        return { success: false, error: 'Failed to get user data' }
      }

      console.log('Sign in successful for user:', authUser.id)
      return { success: true, user: authUser }
    } catch (error) {
      console.error('Sign in error:', error)
      return { success: false, error: 'An error occurred during sign in' }
    }
  },

  async signOut(): Promise<void> {
    await authCookies.clearUserSession()
  },

  async requireAuth(): Promise<AuthUser> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('Authentication required')
    }
    return user
  }
}

// Middleware helper for protected routes
export async function withAuth<T>(
  handler: (user: AuthUser) => Promise<T>
): Promise<T> {
  const user = await auth.getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }
  return handler(user)
}

// Permission checks
export const permissions = {
  canCompress(user: AuthUser, fileSizeMb: number): { allowed: boolean; reason?: string } {
    // Check file size limits
    const maxFileSize = user.membership_level === 'pro' ? 200 : 10
    if (fileSizeMb > maxFileSize) {
      return {
        allowed: false,
        reason: `File size exceeds ${maxFileSize}MB limit for ${user.membership_level} plan`
      }
    }

    // For free users, check if they have points/compressions left
    if (user.membership_level === 'free' && user.points <= 0) {
      return {
        allowed: false,
        reason: 'No compression credits remaining. Upgrade to Pro for unlimited compressions.'
      }
    }

    // For pro users, check if membership is active
    if (user.membership_level === 'pro' && !user.is_membership_active) {
      return {
        allowed: false,
        reason: 'Pro membership has expired. Please renew your subscription.'
      }
    }

    return { allowed: true }
  },

  canUseAPI(user: AuthUser): { allowed: boolean; reason?: string } {
    if (user.membership_level !== 'pro') {
      return {
        allowed: false,
        reason: 'API access requires Pro membership'
      }
    }

    if (!user.is_membership_active) {
      return {
        allowed: false,
        reason: 'Pro membership has expired'
      }
    }

    return { allowed: true }
  },

  canUseBatchProcessing(user: AuthUser): { allowed: boolean; reason?: string } {
    if (user.membership_level !== 'pro') {
      return {
        allowed: false,
        reason: 'Batch processing requires Pro membership'
      }
    }

    if (!user.is_membership_active) {
      return {
        allowed: false,
        reason: 'Pro membership has expired'
      }
    }

    return { allowed: true }
  },

  canUseAdvancedFormats(user: AuthUser, fileType: string): { allowed: boolean; reason?: string } {
    const advancedFormats = ['gif', 'video', 'mp4', 'avi', 'mov', 'webm']
    const isAdvancedFormat = advancedFormats.some(format => 
      fileType.toLowerCase().includes(format)
    )

    if (isAdvancedFormat && user.membership_level !== 'pro') {
      return {
        allowed: false,
        reason: 'Advanced format support requires Pro membership'
      }
    }

    if (isAdvancedFormat && !user.is_membership_active) {
      return {
        allowed: false,
        reason: 'Pro membership has expired'
      }
    }

    return { allowed: true }
  }
}
