# Stripe 订阅功能设置指南

## 🎯 功能概述

我已经为您实现了完整的Stripe订阅系统，包括：
- 14天免费试用
- 月付/年付订阅
- 自动续费管理
- Webhook事件处理

## 📋 设置步骤

### 1. 数据库更新

首先，在Supabase SQL Editor中执行更新后的数据库脚本：

```sql
-- 执行 database/schema.sql 中的所有内容
-- 这会添加试用期和Stripe相关字段
```

### 2. Stripe Dashboard配置

#### 2.1 创建产品
1. 登录 https://dashboard.stripe.com
2. 确保在测试模式下
3. 导航到 Products → Add product
4. 创建 "Pro Plan" 产品，设置两个价格：
   - Monthly: $9.00 USD
   - Yearly: $84.00 USD

#### 2.2 获取Price IDs
创建后会得到类似这样的ID：
- `price_1234567890abcdef` (月付)
- `price_0987654321fedcba` (年付)

### 3. 环境变量配置

更新您的 `.env.local` 文件：

```env
# Stripe密钥
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# 价格ID (后端使用)
STRIPE_PRICE_ID_PRO_MONTHLY=price_your_monthly_price_id_here
STRIPE_PRICE_ID_PRO_YEARLY=price_your_yearly_price_id_here

# 价格ID (前端使用)
NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY=price_your_monthly_price_id_here
NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_YEARLY=price_your_yearly_price_id_here

# 应用URL
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### 4. Webhook设置

#### 4.1 创建Webhook端点
1. 在Stripe Dashboard中：Developers → Webhooks
2. Add endpoint: `http://localhost:3000/api/webhooks/stripe`
3. 选择事件：
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

#### 4.2 获取Webhook Secret
复制 "Signing secret" 并添加到环境变量中。

## 🧪 测试流程

### 测试卡号
使用Stripe测试卡：`4242 4242 4242 4242`

### 测试场景

1. **新用户免费试用**：
   ```
   访问 /pricing → 点击 "Start 14-Day Free Trial"
   → 用户获得14天Pro权限
   ```

2. **试用转付费**：
   ```
   试用期间 → 点击 "Subscribe Now" 
   → 完成Stripe checkout → 订阅激活
   ```

3. **直接订阅**：
   ```
   已用过试用的用户 → 点击 "Subscribe Now"
   → 直接进入付费流程
   ```

## 🔧 已实现的API端点

### `/api/subscription/create-trial`
- 为新用户创建14天免费试用
- 检查用户是否已使用过试用
- 更新用户状态为 `trial`

### `/api/subscription/create-checkout`
- 创建Stripe checkout会话
- 处理月付/年付选择
- 管理Stripe客户创建

### `/api/webhooks/stripe`
- 处理Stripe webhook事件
- 自动更新用户订阅状态
- 管理试用期转换

## 📊 用户状态管理

### 状态类型
- `free`: 免费用户 (50积分)
- `trial`: 试用用户 (14天Pro权限)
- `active`: 付费用户 (无限制)
- `expired`: 过期用户
- `cancelled`: 已取消用户

### 数据库字段
```sql
-- 新增字段
trial_used BOOLEAN DEFAULT FALSE
trial_start_date TIMESTAMP
trial_end_date TIMESTAMP
stripe_customer_id VARCHAR(255)
stripe_subscription_id VARCHAR(255)
```

## 🎨 前端更新

### Pricing页面
- 智能按钮显示（试用/订阅/当前计划）
- 用户状态检测
- 加载状态管理

### 成功页面
- `/subscription/success` - 订阅成功页面
- 显示Pro功能列表
- 用户订阅详情

## 🚀 部署到生产环境

### 1. 更新Stripe为生产模式
- 切换到Live模式
- 获取生产环境的密钥和价格ID
- 更新环境变量

### 2. 更新Webhook URL
- 使用您的生产域名
- 重新配置webhook端点

### 3. 测试生产环境
- 使用真实信用卡测试
- 验证所有流程正常工作

## 🔍 故障排除

### 常见问题
1. **按钮无响应**：检查环境变量配置
2. **试用未激活**：检查数据库连接和用户状态
3. **Webhook失败**：检查端点URL和事件选择
4. **订阅状态错误**：查看Stripe Dashboard事件日志

### 调试工具
- 浏览器开发者工具
- Stripe Dashboard事件日志
- Supabase实时日志
- 服务器控制台输出

## ✅ 验证清单

- [ ] 数据库schema已更新
- [ ] Stripe产品和价格已创建
- [ ] 环境变量已配置
- [ ] Webhook端点已设置
- [ ] 免费试用功能正常
- [ ] 订阅流程正常
- [ ] 用户状态正确更新

## 🎉 完成！

现在您的应用已经具备完整的订阅功能：
- 新用户可以享受14天免费试用
- 试用结束后可以无缝转为付费订阅
- 支持月付和年付选项
- 自动处理订阅状态变更

如有问题，请检查上述配置步骤或查看相关日志。
