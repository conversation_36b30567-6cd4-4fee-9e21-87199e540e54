# CompressHub 完整设置指南

这个指南将帮助您从零开始设置CompressHub项目，包括所有必需的服务配置。

## 📋 前提条件

- Node.js 18+
- npm/yarn/pnpm
- 一个Google账户（用于OAuth）
- 一个Supabase账户
- 一个Stripe账户

## 🚀 快速开始

### 1. 克隆并安装依赖

```bash
# 克隆项目（如果还没有）
git clone <your-repo-url>
cd image-video-compress

# 安装依赖
npm install
# 或
yarn install
# 或
pnpm install
```

### 2. 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env.local
```

现在编辑 `.env.local` 文件，按照以下步骤获取并填入各项配置。

## 🗄️ Supabase 设置

### 第一步：创建Supabase项目

1. 访问 [Supabase](https://supabase.com) 并登录
2. 点击 "New Project"
3. 填写项目信息：
   - 项目名称：CompressHub
   - 数据库密码：创建一个强密码
   - 地区：选择离用户最近的地区
4. 等待项目初始化完成

### 第二步：获取API密钥

1. 在项目仪表板中，点击 "Settings" > "API"
2. 复制以下信息到 `.env.local`：
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```

### 第三步：设置数据库

1. 在Supabase项目中，点击 "SQL Editor"
2. 点击 "New query"
3. 复制 `database/schema.sql` 的全部内容并粘贴
4. 点击 "Run" 执行SQL脚本

这将创建所有必需的表和安全策略。

## 🔐 Google OAuth 设置

### 第一步：创建Google Cloud项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 "Google+ API" 和 "Google Identity API"

### 第二步：配置OAuth同意屏幕

1. 转到 "APIs & Services" > "OAuth consent screen"
2. 选择 "External" 用户类型
3. 填写应用信息：
   - 应用名称：CompressHub
   - 用户支持邮箱：您的邮箱
   - 开发者联系信息：您的邮箱

### 第三步：创建OAuth凭据

1. 转到 "APIs & Services" > "Credentials"
2. 点击 "Create Credentials" > "OAuth client ID"
3. 选择 "Web application"
4. 添加授权的JavaScript来源：
   - `http://localhost:3000` (开发环境)
   - `https://yourdomain.com` (生产环境)
5. 添加授权的重定向URI：
   - `http://localhost:3000/api/oauth/google/callback`
6. 复制客户端ID和密钥到 `.env.local`：
   ```env
   GOOGLE_CLIENT_ID=your_client_id_here
   GOOGLE_CLIENT_SECRET=your_client_secret_here
   NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_client_id_here
   ```

## 💳 Stripe 设置

### 第一步：创建Stripe账户

1. 访问 [Stripe](https://stripe.com) 并注册
2. 完成账户验证（生产环境需要）

### 第二步：获取API密钥

1. 在Stripe仪表板中，转到 "Developers" > "API keys"
2. 复制密钥到 `.env.local`：
   ```env
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
   STRIPE_SECRET_KEY=sk_test_your_key_here
   ```

### 第三步：创建产品和价格

1. 转到 "Products" > "Add product"
2. 创建两个产品：

**Pro Monthly:**
- 名称：CompressHub Pro Monthly
- 价格：$9.00 USD
- 计费周期：Monthly
- 复制价格ID

**Pro Yearly:**
- 名称：CompressHub Pro Yearly
- 价格：$84.00 USD
- 计费周期：Yearly
- 复制价格ID

3. 将价格ID添加到 `.env.local`：
   ```env
   STRIPE_PRICE_ID_PRO_MONTHLY=price_your_monthly_id
   STRIPE_PRICE_ID_PRO_YEARLY=price_your_yearly_id
   ```

### 第四步：设置Webhook

1. 转到 "Developers" > "Webhooks"
2. 点击 "Add endpoint"
3. 端点URL：`http://localhost:3000/api/webhooks/stripe` (开发环境)
4. 选择事件：
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. 复制Webhook密钥到 `.env.local`：
   ```env
   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
   ```

## 🧪 测试设置

### 启动开发服务器

```bash
npm run dev
```

### 测试功能

1. **访问应用**：打开 http://localhost:3000
2. **测试Google登录**：点击登录按钮，尝试Google OAuth
3. **测试图片压缩**：上传一张图片进行压缩
4. **测试订阅流程**：
   - 访问 `/pricing` 页面
   - 点击 "Start Free Trial"
   - 使用Stripe测试卡号：`4242 4242 4242 4242`
5. **检查数据库**：在Supabase中查看用户和订单数据

## 🔧 故障排除

### 常见问题

1. **Google OAuth错误**
   - 检查回调URL是否正确配置
   - 确保客户端ID和密钥正确

2. **Supabase连接错误**
   - 验证项目URL和API密钥
   - 检查数据库是否正确初始化

3. **Stripe支付失败**
   - 确保使用测试环境的密钥
   - 检查Webhook端点是否可访问

4. **图片压缩失败**
   - 检查Sharp库是否正确安装
   - 确保临时目录有写入权限

### 开发工具

```bash
# 查看日志
npm run dev

# 检查数据库连接
# 在Supabase项目中查看日志

# 测试Stripe Webhook（需要Stripe CLI）
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```

## 🚀 部署到生产环境

### 环境变量更新

1. 将所有测试密钥替换为生产密钥
2. 更新回调URL为生产域名
3. 激活Stripe账户

### 推荐部署平台

- **Vercel**（推荐）：与Next.js完美集成
- **Netlify**：支持静态站点和函数
- **Railway**：简单的全栈部署

## 📚 更多资源

- [Supabase文档](https://supabase.com/docs)
- [Stripe文档](https://stripe.com/docs)
- [Google OAuth文档](https://developers.google.com/identity/protocols/oauth2)
- [Next.js部署指南](https://nextjs.org/docs/deployment)

## 🆘 获取帮助

如果遇到问题：

1. 检查浏览器控制台错误
2. 查看服务器日志
3. 验证所有环境变量
4. 参考各服务的官方文档

---

完成以上设置后，您就拥有了一个功能完整的图片视频压缩平台！🎉