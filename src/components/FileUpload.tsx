'use client'

import { useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { motion } from 'framer-motion'
import { Upload, FileImage, AlertCircle } from 'lucide-react'
import toast from 'react-hot-toast'

interface FileUploadProps {
  onFileUpload: (files: File[]) => void
  acceptedTypes?: Record<string, string[]>
  maxFileSize?: number
  title?: string
  subtitle?: string
  multiple?: boolean
}

export function FileUpload({
  onFileUpload,
  acceptedTypes = {
    'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
    'video/*': ['.mp4', '.avi', '.mov', '.webm', '.mkv']
  },
  maxFileSize = 100 * 1024 * 1024, // 100MB default
  title = "Drop your files here",
  subtitle = "or click to browse",
  multiple = true
}: FileUploadProps) {
  
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) =>
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      ).join('\n')
      toast.error(`Some files were rejected:\n${errors}`)
    }
    
    if (acceptedFiles.length > 0) {
      onFileUpload(acceptedFiles)
    }
  }, [onFileUpload])

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: acceptedTypes,
    maxSize: maxFileSize,
    multiple
  })

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer
          transition-all duration-300 ease-in-out
          ${isDragActive && !isDragReject
            ? 'border-blue-400 dark:border-blue-500 bg-blue-50 dark:bg-blue-900/20 scale-105'
            : isDragReject
            ? 'border-red-400 dark:border-red-500 bg-red-50 dark:bg-red-900/20'
            : 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700'
          }
        `}
      >
        <input {...getInputProps()} />
        
        {/* Upload Icon */}
        <motion.div
          animate={isDragActive ? { scale: 1.1, rotate: 5 } : { scale: 1, rotate: 0 }}
          transition={{ duration: 0.2 }}
          className="flex justify-center mb-6"
        >
          {isDragReject ? (
            <AlertCircle className="w-16 h-16 text-red-500 dark:text-red-400" />
          ) : (
            <div className={`
              w-20 h-20 rounded-full flex items-center justify-center
              ${isDragActive
                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              }
            `}>
              {isDragActive ? (
                <FileImage className="w-10 h-10" />
              ) : (
                <Upload className="w-10 h-10" />
              )}
            </div>
          )}
        </motion.div>

        {/* Text */}
        <div className="space-y-2">
          <h3 className={`
            text-xl font-semibold
            ${isDragReject
              ? 'text-red-600 dark:text-red-400'
              : isDragActive
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-gray-900 dark:text-gray-100'
            }
          `}>
            {isDragReject
              ? 'Invalid file type'
              : isDragActive
              ? 'Drop files here'
              : title
            }
          </h3>
          <p className={`
            text-sm
            ${isDragReject
              ? 'text-red-500 dark:text-red-400'
              : isDragActive
              ? 'text-blue-500 dark:text-blue-400'
              : 'text-gray-500 dark:text-gray-400'
            }
          `}>
            {isDragReject
              ? 'Please check file type and size requirements'
              : subtitle
            }
          </p>
        </div>

        {/* File Requirements */}
        <div className="mt-6 text-xs text-gray-400 dark:text-gray-500 space-y-1">
          <div>
            Supported formats: {Object.values(acceptedTypes).flat().join(', ')}
          </div>
          <div>
            Maximum file size: {Math.round(maxFileSize / (1024 * 1024))}MB
          </div>
        </div>

        {/* Animated Background */}
        {isDragActive && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 dark:from-blue-500/10 dark:to-purple-500/10 rounded-2xl"
          />
        )}
      </div>
    </motion.div>
  )
}
