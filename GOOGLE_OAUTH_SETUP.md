# Google OAuth Setup Guide

This guide will help you set up Google OAuth authentication for your CompressHub application.

## Prerequisites

1. A Google account
2. Access to Google Cloud Console

## Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" at the top of the page
3. Click "New Project"
4. Enter a project name (e.g., "CompressHub")
5. Click "Create"

## Step 2: Enable Google+ API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google+ API" or "Google Identity"
3. Click on "Google+ API" and click "Enable"
4. Also enable "Google Identity and Access Management (IAM) API"

## Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace account)
3. Click "Create"
4. Fill in the required information:
   - App name: CompressHub
   - User support email: Your email
   - Developer contact information: Your email
5. Click "Save and Continue"
6. Skip the "Scopes" section for now (click "Save and Continue")
7. Add test users if needed (for development)
8. Click "Save and Continue"

## Step 4: Create OAuth Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Web application" as the application type
4. Enter a name (e.g., "CompressHub Web Client")
5. Add authorized JavaScript origins:
   - `http://localhost:3000` (for development)
   - Your production domain (e.g., `https://yourdomain.com`)
6. Add authorized redirect URIs:
   - `http://localhost:3000/api/oauth/google/callback` (for development)
   - `https://yourdomain.com/api/oauth/google/callback` (for production)
7. Click "Create"

## Step 5: Configure Environment Variables

1. Copy the Client ID and Client Secret from the credentials you just created
2. Update your `.env.local` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_actual_google_client_id_here
GOOGLE_CLIENT_SECRET=your_actual_google_client_secret_here
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_actual_google_client_id_here
NEXT_PUBLIC_GOOGLE_CALLBACK_URL=http://localhost:3000/api/oauth/google/callback
```

**Important:** Replace `your_actual_google_client_id_here` and `your_actual_google_client_secret_here` with the actual values from Google Cloud Console.

## Step 6: Test the Integration

1. Start your development server: `npm run dev`
2. Open your application in a browser
3. Try to sign in using Google OAuth
4. You should see either:
   - Google One Tap prompt (automatic)
   - Google sign-in button in the auth modal

## Features Implemented

### 1. Traditional OAuth Flow
- Click "Continue with Google" button
- Redirects to Google's authorization page
- Returns to your app with user authenticated

### 2. Google One Tap
- Automatic popup for signed-in Google users
- Seamless authentication experience
- Only shows when user is not already logged in

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" error**
   - Make sure the redirect URI in Google Cloud Console exactly matches your callback URL
   - Check for trailing slashes and protocol (http vs https)

2. **"invalid_client" error**
   - Verify your Client ID and Client Secret are correct
   - Make sure you're using the right environment variables

3. **Google One Tap not showing**
   - Check browser console for errors
   - Ensure you're on HTTPS in production (localhost is OK for development)
   - Make sure the user isn't already signed in

4. **CORS errors**
   - Verify your JavaScript origins are correctly configured in Google Cloud Console

### Development vs Production

- **Development**: Use `http://localhost:3000`
- **Production**: Use your actual domain with HTTPS

Make sure to update both the Google Cloud Console settings and your environment variables when deploying to production.

## Security Notes

1. Never commit your `.env.local` file to version control
2. Use different OAuth credentials for development and production
3. Regularly rotate your client secrets
4. Monitor your OAuth usage in Google Cloud Console

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your Google Cloud Console configuration
3. Ensure all environment variables are set correctly
4. Test with a fresh browser session (clear cookies/cache)
