import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { authCookies } from '@/lib/auth'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export async function POST(request: NextRequest) {
  try {
    // Get user from auth cookies
    const userEmail = await authCookies.getUserSession()
    if (!userEmail) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // Get user from database
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', userEmail)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user has already used their trial
    if (user.trial_used) {
      return NextResponse.json({ 
        error: 'Trial already used. Please subscribe to continue using Pro features.' 
      }, { status: 400 })
    }

    // Calculate trial dates (14 days from now)
    const trialStart = new Date()
    const trialEnd = new Date()
    trialEnd.setDate(trialEnd.getDate() + 14)

    // Update user with trial information
    const { error: updateError } = await supabaseAdmin
      .from('users')
      .update({
        membership_status: 'trial',
        membership_level: 'pro',
        trial_used: true,
        trial_start_date: trialStart.toISOString(),
        trial_end_date: trialEnd.toISOString(),
        membership_start_date: trialStart.toISOString(),
        membership_end_date: trialEnd.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id)

    if (updateError) {
      console.error('Error updating user trial:', updateError)
      return NextResponse.json({ error: 'Failed to start trial' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Trial started successfully!',
      trial_end_date: trialEnd.toISOString()
    })

  } catch (error) {
    console.error('Create trial error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
