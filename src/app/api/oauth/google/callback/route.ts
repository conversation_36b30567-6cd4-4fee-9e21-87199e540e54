import { NextRequest, NextResponse } from 'next/server'
import { Google } from 'arctic'
import { OAuth2Client } from 'google-auth-library'
import { supabaseAdmin } from '@/lib/supabase'
import { authCookies } from '@/lib/auth'

const CALLBACK_URL = process.env.NEXT_PUBLIC_GOOGLE_CALLBACK_URL || 'http://localhost:3000/api/oauth/google/callback'

async function updateOrCreateUser(username: string, email: string, avatarUrl: string) {
  try {
    // Check if user exists
    const { data: existingUser, error: selectError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', email)
      .maybeSingle()

    if (selectError) {
      console.error('Error checking existing user:', selectError)
      throw selectError
    }

    if (existingUser) {
      // Update existing user
      const { data: updatedUser, error: updateError } = await supabaseAdmin
        .from('users')
        .update({
          last_login_time: new Date().toISOString(),
          avatar_url: avatarUrl || existingUser.avatar_url,
          username: username || existingUser.username,
          updated_at: new Date().toISOString()
        })
        .eq('email', email)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating user:', updateError)
        throw updateError
      }

      return updatedUser
    }

    // Create new user
    const { data: newUser, error: insertError } = await supabaseAdmin
      .from('users')
      .insert([
        {
          username,
          email,
          avatar_url: avatarUrl,
          last_login_time: new Date().toISOString(),
          points: 50,
          membership_level: 'free',
          membership_status: 'free',
          register_source: 'google_oauth',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
      ])
      .select()
      .single()

    if (insertError) {
      console.error('Error creating user:', insertError)
      throw insertError
    }

    return newUser
  } catch (error) {
    console.error('Database operation failed:', error)
    throw error
  }
}

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const code = url.searchParams.get('code')
    const state = url.searchParams.get('state')
    
    // Get stored state and code verifier from cookies
    const storedState = request.cookies.get('google_oauth_state')?.value
    const codeVerifier = request.cookies.get('google_oauth_code_verifier')?.value

    if (!code || !state || !storedState || !codeVerifier) {
      return NextResponse.redirect(new URL('/?error=missing_parameters', request.url))
    }

    if (state !== storedState) {
      return NextResponse.redirect(new URL('/?error=invalid_state', request.url))
    }

    const googleAuth = new Google(
      process.env.GOOGLE_CLIENT_ID!,
      process.env.GOOGLE_CLIENT_SECRET!,
      CALLBACK_URL
    )

    // Exchange code for tokens
    const tokens = await googleAuth.validateAuthorizationCode(code, codeVerifier)

    // Use OAuth2Client to get user info more reliably
    const oauth2Client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID)
    oauth2Client.setCredentials({
      access_token: tokens.accessToken()
    })

    // Get user info using OAuth2Client
    const userInfoResponse = await oauth2Client.request({
      url: 'https://www.googleapis.com/oauth2/v1/userinfo'
    })

    const googleUser = userInfoResponse.data as any
    console.log('Google OAuth callback user:', googleUser)

    // Update or create user directly using Supabase (same as One Tap)
    const user = await updateOrCreateUser(
      googleUser.name,
      googleUser.email,
      googleUser.picture
    )

    // Set authentication cookies
    await authCookies.setUserSession(googleUser.email)

    console.log('Google OAuth callback sign in successful')

    // Clear OAuth cookies
    const response = NextResponse.redirect(new URL('/', request.url))
    response.cookies.delete('google_oauth_state')
    response.cookies.delete('google_oauth_code_verifier')

    return response
  } catch (error) {
    console.error('Google OAuth callback error:', error)
    return NextResponse.redirect(new URL('/?error=oauth_failed', request.url))
  }
}
