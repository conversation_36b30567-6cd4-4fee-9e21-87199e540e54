# Stripe Setup Guide

这个指南将帮助您为CompressHub项目设置Stripe支付系统。

## 前提条件

1. 一个Stripe账户
2. 已完成Supabase设置（参考SUPABASE_SETUP.md）

## 第一步：创建Stripe账户

1. 访问 [Stripe](https://stripe.com)
2. 点击"Sign up"创建账户
3. 填写必要的商业信息
4. 验证您的邮箱地址

## 第二步：获取API密钥

1. 登录Stripe仪表板
2. 点击右上角的"Developers"
3. 点击"API keys"
4. 复制以下密钥：
   - **Publishable key** (pk_test_...): 用于客户端
   - **Secret key** (sk_test_...): 用于服务端

**注意**: 开发阶段使用测试密钥，生产环境需要激活账户并使用实时密钥。

## 第三步：创建产品和价格

### 创建Pro Monthly产品

1. 在Stripe仪表板中，点击"Products"
2. 点击"Add product"
3. 填写产品信息：
   - **Name**: CompressHub Pro Monthly
   - **Description**: Unlimited image and video compression with advanced features
   - **Image**:         
4. 在"Pricing"部分：
   - **Price**: $9.00
   - **Currency**: USD
   - **Billing period**: Monthly
   - **Usage type**: Licensed (fixed price)
5. 点击"Save product"
6. 复制生成的**Price ID** (price_xxx) prod_SqeSkCR7urJ8wI

### 创建Pro Yearly产品

1. 重复上述步骤，但修改以下信息：
   - **Name**: CompressHub Pro Yearly
   - **Price**: $84.00
   - **Billing period**: Yearly
2. 复制生成的**Price ID** (price_xxx) prod_SqeTMlspIL6lQA

## 第四步：配置Webhook

### 创建Webhook端点

1. 在Stripe仪表板中，点击"Developers" > "Webhooks"
2. 点击"Add endpoint"
3. 填写端点信息：
   - **Endpoint URL**: `https://yourdomain.com/api/webhooks/stripe`
   - 开发环境: `http://localhost:3000/api/webhooks/stripe`
4. 选择要监听的事件：
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. 点击"Add endpoint"
6. 复制生成的**Webhook secret** (whsec_xxx)

### 测试Webhook（开发环境）

对于本地开发，您需要使用Stripe CLI：

1. 安装Stripe CLI：
   ```bash
   # macOS
   brew install stripe/stripe-cli/stripe

   # Windows
   # 下载并安装 https://github.com/stripe/stripe-cli/releases
   ```

2. 登录Stripe CLI：
   ```bash
   stripe login
   ```

3. 转发Webhook到本地：
   ```bash
   stripe listen --forward-to localhost:3000/api/webhooks/stripe
   ```

4. 复制显示的webhook secret用于本地开发

## 第五步：更新环境变量

更新 `.env.local` 文件：

```env
# Stripe 配置
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs
STRIPE_PRICE_ID_PRO_MONTHLY=price_your_monthly_price_id_here
STRIPE_PRICE_ID_PRO_YEARLY=price_your_yearly_price_id_here
```

## 第六步：更新代码中的价格ID

编辑 `src/lib/stripe.ts` 文件：

```typescript
// Stripe price IDs for different plans
export const stripePriceIds = {
  pro_monthly: 'price_your_actual_monthly_price_id', // 替换为实际的价格ID
  pro_yearly: 'price_your_actual_yearly_price_id'   // 替换为实际的价格ID
}
```

## 第七步：测试支付流程

### 使用测试卡号

Stripe提供测试卡号用于开发：

- **成功支付**: 4242 4242 4242 4242
- **需要验证**: 4000 0025 0000 3155
- **被拒绝**: 4000 0000 0000 0002
- **过期日期**: 任何未来日期
- **CVC**: 任何3位数字
- **邮编**: 任何5位数字

### 测试步骤

1. 启动开发服务器：`npm run dev`
2. 访问定价页面：`http://localhost:3000/pricing`
3. 点击"Start 14-Day Free Trial"
4. 使用测试卡号完成支付
5. 检查Stripe仪表板中的支付记录
6. 验证用户会员状态是否正确更新

## 第八步：配置客户门户（可选）

客户门户允许用户管理自己的订阅：

1. 在Stripe仪表板中，点击"Settings" > "Billing"
2. 点击"Customer portal"
3. 配置门户设置：
   - 允许客户取消订阅
   - 允许客户更新支付方式
   - 允许客户查看发票历史
4. 保存设置

## 故障排除

### 常见问题

1. **支付失败**
   - 检查API密钥是否正确
   - 确保使用正确的测试卡号
   - 查看Stripe仪表板的错误日志

2. **Webhook未触发**
   - 检查Webhook URL是否正确
   - 确保端点可以从外部访问
   - 验证Webhook secret是否正确

3. **订阅状态未更新**
   - 检查Webhook处理逻辑
   - 确保数据库连接正常
   - 查看服务器日志

4. **本地开发Webhook问题**
   - 确保Stripe CLI正在运行
   - 检查转发URL是否正确
   - 使用ngrok暴露本地端口

### 调试技巧

1. **查看Stripe日志**：
   - 在Stripe仪表板中查看"Logs"
   - 检查API请求和响应

2. **使用Stripe CLI**：
   ```bash
   # 查看实时事件
   stripe listen

   # 重新发送Webhook事件
   stripe events resend evt_xxx
   ```

3. **测试Webhook**：
   ```bash
   # 触发测试事件
   stripe trigger checkout.session.completed
   ```

## 生产环境配置

### 激活Stripe账户

1. 完成Stripe账户验证
2. 提供必要的商业信息
3. 激活实时支付

### 更新生产环境变量

```env
# 生产环境Stripe配置
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret
```

### 更新Webhook端点

1. 在Stripe仪表板中更新Webhook URL为生产域名
2. 确保HTTPS证书有效
3. 测试生产环境的支付流程

## 安全注意事项

1. **永远不要**在客户端暴露Secret Key
2. **验证**所有Webhook签名
3. **使用HTTPS**处理所有支付相关请求
4. **定期轮换**API密钥
5. **监控**异常支付活动

## 支持和资源

- [Stripe文档](https://stripe.com/docs)
- [Stripe CLI文档](https://stripe.com/docs/stripe-cli)
- [测试卡号](https://stripe.com/docs/testing)
- [Webhook指南](https://stripe.com/docs/webhooks)

## 下一步

完成Stripe设置后，您可以：
1. 测试完整的支付流程
2. 实现订阅管理功能
3. 添加发票和收据功能
4. 配置税务设置（如需要）