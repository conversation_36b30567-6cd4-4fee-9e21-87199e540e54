# Supabase Setup Guide

这个指南将帮助您为CompressHub项目设置Supabase数据库和认证服务。

## 前提条件

1. 一个Supabase账户
2. 已完成Google OAuth设置（参考GOOGLE_OAUTH_SETUP.md）

## 第一步：创建Supabase项目

1. 访问 [Supabase](https://supabase.com)
2. 点击"New Project"
3. 选择您的组织（或创建新组织）
4. 填写项目信息：
   - **项目名称**: CompressHub
   - **数据库密码**: 创建一个强密码并保存
   - **地区**: 选择离您用户最近的地区
5. 点击"Create new project"
6. 等待项目初始化完成（通常需要2-3分钟）

## 第二步：获取项目配置信息

项目创建完成后：

1. 在项目仪表板中，点击左侧的"Settings"
2. 点击"API"标签
3. 复制以下信息：
   - **Project URL**: `https://your-project-id.supabase.co`
   - **anon public key**: `eyJ...` (公开密钥)
   - **service_role secret key**: `eyJ...` (服务密钥，保密)

## 第三步：配置环境变量

在项目根目录创建或更新 `.env.local` 文件：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_public_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_secret_key_here

# Google OAuth 配置（如果还没有配置）
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id

# Stripe 配置（稍后配置）
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# 应用配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_random_secret_key_here
```

**重要**:
- 将 `your-project-id` 替换为您的实际项目ID
- 将密钥替换为从Supabase复制的实际值
- 不要将 `.env.local` 文件提交到版本控制

## 第四步：执行数据库迁移

1. 在Supabase项目仪表板中，点击左侧的"SQL Editor"
2. 点击"New query"
3. 复制 `database/schema.sql` 文件的全部内容
4. 粘贴到SQL编辑器中
5. 点击"Run"执行SQL脚本

这将创建以下表：
- `users` - 用户账户信息
- `membership_levels` - 订阅计划定义
- `orders` - 订单和支付记录
- `user_usage` - 用户使用统计
- `compression_logs` - 文件处理日志

## 第五步：配置行级安全策略 (RLS)

数据库脚本已经包含了完整的RLS策略，确保：
- 用户只能访问自己的数据
- 服务角色可以访问所有数据
- 匿名用户无法访问敏感数据

## 第六步：验证设置

1. 启动开发服务器：`npm run dev`
2. 打开浏览器访问 `http://localhost:3000`
3. 尝试使用Google登录
4. 检查浏览器控制台是否有错误

## 第七步：配置认证提供商（可选）

如果您想启用其他登录方式：

1. 在Supabase项目中，点击"Authentication" > "Providers"
2. 启用您需要的提供商（Google已通过OAuth配置）
3. 配置相应的客户端ID和密钥

## 数据库表结构说明

### users 表
存储用户基本信息和会员状态：
- `id`: 用户唯一标识
- `email`: 用户邮箱
- `membership_level`: 会员等级 (free/pro)
- `membership_status`: 会员状态 (free/active/expired/cancelled)
- `points`: 免费用户的压缩次数

### membership_levels 表
定义订阅计划：
- Free: $0/月，50次压缩，10MB文件限制
- Pro Monthly: $9/月，无限压缩，200MB文件限制
- Pro Yearly: $84/年，无限压缩，200MB文件限制

### orders 表
记录所有订单和支付信息：
- 支持Stripe支付集成
- 跟踪订单状态和支付状态

### user_usage 表
跟踪用户每日使用情况：
- 压缩次数统计
- API调用统计
- 文件大小统计

### compression_logs 表
记录每次压缩操作的详细信息：
- 文件信息和压缩比
- 处理时间和用户信息

## 故障排除

### 常见问题

1. **连接错误**
   - 检查项目URL和API密钥是否正确
   - 确保项目已完全初始化

2. **权限错误**
   - 检查RLS策略是否正确应用
   - 确保使用正确的服务角色密钥

3. **SQL执行错误**
   - 检查SQL语法是否正确
   - 确保没有重复执行创建表的语句

4. **认证问题**
   - 检查Google OAuth配置
   - 确保回调URL正确设置

### 开发 vs 生产

- **开发环境**: 使用测试数据库，可以随时重置
- **生产环境**: 创建单独的Supabase项目，使用不同的环境变量

## 安全注意事项

1. **永远不要**将服务角色密钥暴露给客户端
2. **定期轮换**API密钥和数据库密码
3. **监控**数据库使用情况和异常访问
4. **备份**重要数据

## 下一步

完成Supabase设置后，继续配置Stripe支付系统（参考STRIPE_SETUP.md）。

## 支持

如果遇到问题：
1. 检查Supabase项目日志
2. 查看浏览器控制台错误
3. 验证环境变量配置
4. 参考Supabase官方文档