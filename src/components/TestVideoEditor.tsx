'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Pause, Save, SkipBack, SkipForward, Volume2, VolumeX, Scissors } from 'lucide-react'
import toast from 'react-hot-toast'

interface FinalVideoEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

export function FinalVideoEditor({ file, onEditComplete }: FinalVideoEditorProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)
  const [setupComplete, setSetupComplete] = useState(false)

  const handleVideoRef = (element: HTMLVideoElement | null) => {
    if (element && !setupComplete) {
      console.log('🎯 Video element ref callback triggered')
      videoRef.current = element
      setSetupComplete(true)

      // Setup video immediately
      setTimeout(() => {
        setupVideo()
      }, 50)
    }
  }

  useEffect(() => {
    console.log('FinalVideoEditor: Component mounted with file:', file.name)
    console.log('File type:', file.type, 'Size:', file.size)

    let retryCount = 0
    const maxRetries = 10

    const trySetup = () => {
      console.log(`Attempt ${retryCount + 1}/${maxRetries} to find video element`)

      if (videoRef.current) {
        console.log('✅ Video element found, setting up...')
        setupVideo()
      } else if (retryCount < maxRetries - 1) {
        retryCount++
        console.log(`❌ Video element not found, retrying in 100ms...`)
        setTimeout(trySetup, 100)
      } else {
        console.error('❌ Video element not found after all retries')
        setIsLoading(false)
        toast.error('Failed to initialize video player')
      }
    }

    // Start trying immediately, then with delays
    trySetup()
  }, [file])

  const setupVideo = () => {
    const video = videoRef.current
    if (!video) {
      console.error('No video element available')
      return
    }

    console.log('Setting up video element...')
    
    // Create object URL
    const url = URL.createObjectURL(file)
    console.log('Created object URL:', url)

    // Set up event listeners
    const handleLoadedMetadata = () => {
      console.log('✅ Video metadata loaded successfully!')
      console.log('Duration:', video.duration)
      console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight)
      
      if (video.duration && !isNaN(video.duration)) {
        setDuration(video.duration)
        setEndTime(video.duration)
        setIsLoading(false)
        console.log('✅ Video is ready to play')
      } else {
        console.error('❌ Invalid video duration')
        setIsLoading(false)
        toast.error('Invalid video file')
      }
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handlePlay = () => {
      console.log('Video started playing')
      setIsPlaying(true)
    }

    const handlePause = () => {
      console.log('Video paused')
      setIsPlaying(false)
    }

    const handleError = (e: Event) => {
      console.error('❌ Video error:', e)
      console.error('Video error details:', video.error)
      setIsLoading(false)
      toast.error('Failed to load video: ' + (video.error?.message || 'Unknown error'))
    }

    const handleLoadStart = () => {
      console.log('📥 Video load started')
    }

    const handleCanPlay = () => {
      console.log('✅ Video can play')
    }

    const handleLoadedData = () => {
      console.log('✅ Video data loaded')
    }

    // Add event listeners
    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('error', handleError)
    video.addEventListener('loadstart', handleLoadStart)
    video.addEventListener('canplay', handleCanPlay)
    video.addEventListener('loadeddata', handleLoadedData)

    // Set source and load
    console.log('Setting video source and calling load()...')
    video.src = url
    video.load()

    // Cleanup function
    return () => {
      console.log('Cleaning up video resources')
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('error', handleError)
      video.removeEventListener('loadstart', handleLoadStart)
      video.removeEventListener('canplay', handleCanPlay)
      video.removeEventListener('loadeddata', handleLoadedData)
      URL.revokeObjectURL(url)
    }
  }

  const togglePlay = () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }

  const seekTo = (time: number) => {
    if (!videoRef.current) return
    videoRef.current.currentTime = Math.max(0, Math.min(time, duration))
  }

  const toggleMute = () => {
    if (!videoRef.current) return

    if (isMuted) {
      videoRef.current.volume = volume
      setIsMuted(false)
    } else {
      videoRef.current.volume = 0
      setIsMuted(true)
    }
  }

  const handleVolumeChange = (newVolume: number) => {
    if (!videoRef.current) return

    setVolume(newVolume)
    videoRef.current.volume = newVolume
    setIsMuted(newVolume === 0)
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleExport = async () => {
    try {
      const blob = new Blob([file], { type: file.type })
      onEditComplete(blob)
      toast.success('Video exported successfully!')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export video')
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-gray-100">
        Video Editor - {file.name}
      </h2>
      
      {isLoading && (
        <div className="flex items-center justify-center h-64 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading video...</p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              File: {file.name} ({Math.round(file.size / 1024 / 1024)}MB)
            </p>
          </div>
        </div>
      )}

      {!isLoading && (
        <>
          {/* Video Player */}
          <div className="relative bg-black rounded-lg overflow-hidden mb-6">
            <video
              ref={handleVideoRef}
              className="w-full h-auto max-h-96 object-contain"
              controls={false}
              playsInline
              preload="metadata"
            />
            
            {/* Enhanced Controls */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              {/* Progress Bar */}
              <div className="mb-4">
                <input
                  type="range"
                  min={0}
                  max={duration}
                  value={currentTime}
                  onChange={(e) => seekTo(Number(e.target.value))}
                  className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-white text-sm mt-1">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => seekTo(Math.max(0, currentTime - 10))}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    <SkipBack className="w-6 h-6" />
                  </button>

                  <button
                    onClick={togglePlay}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                  </button>

                  <button
                    onClick={() => seekTo(Math.min(duration, currentTime + 10))}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    <SkipForward className="w-6 h-6" />
                  </button>
                </div>

                <div className="flex items-center space-x-4">
                  {/* Volume Control */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={toggleMute}
                      className="text-white hover:text-blue-400 transition-colors"
                    >
                      {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                    </button>
                    <input
                      type="range"
                      min={0}
                      max={1}
                      step={0.1}
                      value={isMuted ? 0 : volume}
                      onChange={(e) => handleVolumeChange(Number(e.target.value))}
                      className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>

                  <button
                    onClick={handleExport}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>Export</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Trim Controls */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100 flex items-center">
              <Scissors className="w-5 h-5 mr-2" />
              Trim Video
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Start Time: {formatTime(startTime)}
                </label>
                <input
                  type="range"
                  min={0}
                  max={duration}
                  value={startTime}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setStartTime(Math.min(value, endTime - 1))
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-600"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  End Time: {formatTime(endTime)}
                </label>
                <input
                  type="range"
                  min={0}
                  max={duration}
                  value={endTime}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setEndTime(Math.max(value, startTime + 1))
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-600"
                />
              </div>
            </div>

            <div className="flex space-x-2 mt-4">
              <button
                onClick={() => seekTo(startTime)}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Go to Start
              </button>
              <button
                onClick={() => seekTo(endTime)}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Go to End
              </button>
            </div>
          </div>

          {/* Video Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Video Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Duration:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(duration)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Trim Duration:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(endTime - startTime)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">File Size:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{Math.round(file.size / 1024 / 1024)}MB</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Format:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{file.type}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Current Time:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(currentTime)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                <span className="ml-2 text-green-600 dark:text-green-400">Ready</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
