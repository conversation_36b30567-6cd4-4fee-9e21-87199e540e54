import toast from 'react-hot-toast'

export interface ErrorHandlerOptions {
  showToast?: boolean
  logError?: boolean
  fallbackMessage?: string
}

/**
 * Standardized error handler for compression services
 */
export class CompressionErrorHandler {
  /**
   * Process and format error messages for user display
   */
  static processError(
    error: unknown, 
    context: 'image' | 'video' | 'gif' = 'image',
    options: ErrorHandlerOptions = {}
  ): string {
    const { showToast = false, logError = true, fallbackMessage } = options
    
    let errorMessage = fallbackMessage || `${context} compression failed`
    
    if (error instanceof Error) {
      errorMessage = error.message
      
      // Handle specific API error cases
      if (errorMessage.includes('Advanced format support requires Pro membership')) {
        errorMessage = this.getProMembershipMessage(context)
      } else if (errorMessage.includes('File size exceeds')) {
        errorMessage = 'File size too large. Please upgrade to Pro for larger file support.'
      } else if (errorMessage.includes('Not authenticated')) {
        errorMessage = `Please sign in to compress ${context} files.`
      } else if (errorMessage.includes('Video compression requires Pro membership')) {
        errorMessage = 'Video compression requires Pro membership. Sign up for a free 14-day trial!'
      } else if (errorMessage.includes('GIF compression requires Pro membership')) {
        errorMessage = 'GIF compression requires Pro membership. Sign up for a free 14-day trial!'
      } else if (errorMessage.includes('codec') || errorMessage.includes('FFmpeg')) {
        errorMessage = 'Server processing unavailable. Please try again later.'
      } else if (errorMessage.includes('Invalid data found')) {
        errorMessage = 'Invalid file format. Please check your file and try again.'
      } else if (errorMessage.includes('Permission denied')) {
        errorMessage = 'Server permission error. Please try again later.'
      } else if (errorMessage.includes('Network')) {
        errorMessage = 'Network error. Please check your connection and try again.'
      }
    }
    
    if (logError) {
      console.error(`${context} compression error:`, error)
    }
    
    if (showToast && this.shouldShowToast(errorMessage)) {
      toast.error(errorMessage)
    }
    
    return errorMessage
  }
  
  /**
   * Get Pro membership message based on file type
   */
  private static getProMembershipMessage(context: 'image' | 'video' | 'gif'): string {
    switch (context) {
      case 'video':
        return 'Video compression requires Pro membership. Sign up for a free 14-day trial!'
      case 'gif':
        return 'GIF compression requires Pro membership. Sign up for a free 14-day trial!'
      case 'image':
        return 'Advanced image formats require Pro membership. Please upgrade to continue.'
      default:
        return 'This feature requires Pro membership. Please upgrade to continue.'
    }
  }
  
  /**
   * Determine if error should trigger a toast notification
   */
  private static shouldShowToast(errorMessage: string): boolean {
    const toastTriggers = [
      'Pro membership',
      'upgrade',
      'sign in',
      'free trial',
      'File size too large',
      'Invalid file format',
      'Network error'
    ]
    
    return toastTriggers.some(trigger => 
      errorMessage.toLowerCase().includes(trigger.toLowerCase())
    )
  }
  
  /**
   * Create a standardized error result for compression services
   */
  static createErrorResult(
    error: unknown,
    originalSize: number,
    context: 'image' | 'video' | 'gif' = 'image',
    options: ErrorHandlerOptions = {}
  ) {
    const errorMessage = this.processError(error, context, options)
    
    return {
      success: false as const,
      originalSize,
      compressedSize: 0,
      compressionRatio: 0,
      error: errorMessage
    }
  }
}

/**
 * Quick error processing function for common use cases
 */
export function processCompressionError(
  error: unknown,
  context: 'image' | 'video' | 'gif' = 'image',
  showToast = false
): string {
  return CompressionErrorHandler.processError(error, context, { showToast })
}

/**
 * Error boundary for compression operations
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: 'image' | 'video' | 'gif' = 'image',
  fallbackValue: T
): Promise<T> {
  try {
    return await operation()
  } catch (error) {
    CompressionErrorHandler.processError(error, context, { 
      showToast: true, 
      logError: true 
    })
    return fallbackValue
  }
}
