'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { 
  <PERSON>rop, 
  RotateCw, 
  Move, 
  ZoomIn, 
  ZoomOut, 
  Undo, 
  Redo, 
  Download,
  Filter,
  Type,
  Square,
  Circle,
  Sliders
} from 'lucide-react'
import toast from 'react-hot-toast'

interface TextElement {
  id: string
  text: string
  x: number // relative to image (0-1)
  y: number // relative to image (0-1)
  fontSize: number
  color: string
}

interface EditState {
  zoom: number
  rotation: number
  imagePosition: { x: number; y: number }
  imageSize: { width: number; height: number }
  originalImageSize: { width: number; height: number }
  filters: {
    brightness: number
    contrast: number
    saturation: number
    blur: number
  }
  textElements: TextElement[]
  activeTool: 'select' | 'crop' | 'rotate' | 'text' | 'shape' | 'filter'
  showFilterPanel: boolean
  showTextPanel: boolean
  cropArea: {
    x: number
    y: number
    width: number
    height: number
  } | null
}

interface AdvancedImageEditorProps {
  file: File
  initialEditState?: EditState
  onEditComplete: (blob: Blob) => void
  onStateChange: (state: EditState) => void
}

type Tool = 'select' | 'crop' | 'rotate' | 'text' | 'shape' | 'filter'

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

export function AdvancedImageEditor({ file, initialEditState, onEditComplete, onStateChange }: AdvancedImageEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [activeTool, setActiveTool] = useState<Tool>(initialEditState?.activeTool || 'select')
  const [zoom, setZoom] = useState(initialEditState?.zoom || 100)
  const [rotation, setRotation] = useState(initialEditState?.rotation || 0)
  const [cropArea, setCropArea] = useState<CropArea | null>(initialEditState?.cropArea || null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imagePosition, setImagePosition] = useState(initialEditState?.imagePosition || { x: 0, y: 0 })
  const [imageSize, setImageSize] = useState(initialEditState?.imageSize || { width: 0, height: 0 })
  const [originalImageSize, setOriginalImageSize] = useState(initialEditState?.originalImageSize || { width: 0, height: 0 })
  const [filters, setFilters] = useState(initialEditState?.filters || {
    brightness: 0,
    contrast: 0,
    saturation: 0,
    blur: 0
  })
  const [showFilterPanel, setShowFilterPanel] = useState(initialEditState?.showFilterPanel || false)
  const [textElements, setTextElements] = useState<TextElement[]>(initialEditState?.textElements || [])
  const [showTextPanel, setShowTextPanel] = useState(initialEditState?.showTextPanel || false)

  const tools = [
    { id: 'select', icon: Move, label: 'Select', shortcut: 'V' },
    { id: 'crop', icon: Crop, label: 'Crop', shortcut: 'C' },
    { id: 'rotate', icon: RotateCw, label: 'Rotate', shortcut: 'R' },
    { id: 'filter', icon: Filter, label: 'Filter', shortcut: 'F' },
    { id: 'text', icon: Type, label: 'Text', shortcut: 'T' },
    // { id: 'shape', icon: Square, label: 'Shape', shortcut: 'S' }
  ]

  const initializeCanvas = () => {
    if (!canvasRef.current || !containerRef.current) return

    const canvas = canvasRef.current
    const container = containerRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size to container size
    const containerRect = container.getBoundingClientRect()
    canvas.width = containerRect.width
    canvas.height = containerRect.height

    // Create image element
    const img = new Image()
    imageRef.current = img

    img.onload = () => {
      console.log('Image loaded:', img.width, 'x', img.height)

      // Set original image size if not already set
      if (originalImageSize.width === 0 && originalImageSize.height === 0) {
        setOriginalImageSize({ width: img.width, height: img.height })
      }

      // Only calculate initial size and position if not provided in initial state
      if (!initialEditState || (imageSize.width === 0 && imageSize.height === 0)) {
        // Calculate initial image size and position to fit in canvas
        const canvasWidth = canvas.width
        const canvasHeight = canvas.height
        const imgAspectRatio = img.width / img.height
        const canvasAspectRatio = canvasWidth / canvasHeight

        let displayWidth, displayHeight

        if (imgAspectRatio > canvasAspectRatio) {
          // Image is wider than canvas
          displayWidth = canvasWidth * 0.8 // Leave some margin
          displayHeight = displayWidth / imgAspectRatio
        } else {
          // Image is taller than canvas
          displayHeight = canvasHeight * 0.8 // Leave some margin
          displayWidth = displayHeight * imgAspectRatio
        }

        setImageSize({ width: displayWidth, height: displayHeight })
        setImagePosition({
          x: (canvasWidth - displayWidth) / 2,
          y: (canvasHeight - displayHeight) / 2
        })
      }

      setIsLoading(false)
      drawCanvas()
    }

    img.onerror = () => {
      console.error('Failed to load image')
      setIsLoading(false)
      toast.error('Failed to load image')
    }

    // Load image from file
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      img.src = result
    }
    
    reader.onerror = () => {
      console.error('Failed to read file')
      setIsLoading(false)
      toast.error('Failed to read file')
    }
    
    reader.readAsDataURL(file)
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      initializeCanvas()
    }, 50)

    return () => clearTimeout(timer)
  }, [file])

  const drawCanvas = useCallback(() => {
    if (!canvasRef.current || !imageRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    const img = imageRef.current

    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw checkerboard background
    drawCheckerboard(ctx, canvas.width, canvas.height)

    // Save context
    ctx.save()

    // Apply transformations
    const centerX = imagePosition.x + imageSize.width / 2
    const centerY = imagePosition.y + imageSize.height / 2

    ctx.translate(centerX, centerY)
    ctx.rotate((rotation * Math.PI) / 180)
    ctx.scale(zoom / 100, zoom / 100)

    // Apply filters
    let filterString = ''
    if (filters.brightness !== 0) {
      filterString += `brightness(${100 + filters.brightness}%) `
    }
    if (filters.contrast !== 0) {
      filterString += `contrast(${100 + filters.contrast}%) `
    }
    if (filters.saturation !== 0) {
      filterString += `saturate(${100 + filters.saturation}%) `
    }
    if (filters.blur > 0) {
      filterString += `blur(${filters.blur}px) `
    }

    if (filterString) {
      ctx.filter = filterString.trim()
    }

    // Draw image
    ctx.drawImage(
      img,
      -imageSize.width / 2,
      -imageSize.height / 2,
      imageSize.width,
      imageSize.height
    )

    // Reset filter
    ctx.filter = 'none'

    // Restore context
    ctx.restore()

    // Draw text elements (relative to image position)
    textElements.forEach(textElement => {
      const zoomFactor = zoom / 100
      const textX = imagePosition.x + (textElement.x * imageSize.width * zoomFactor)
      const textY = imagePosition.y + (textElement.y * imageSize.height * zoomFactor)
      const fontSize = textElement.fontSize * zoomFactor

      ctx.font = `${fontSize}px Arial`
      ctx.fillStyle = textElement.color
      ctx.fillText(textElement.text, textX, textY)
    })

    // Draw crop overlay if crop tool is active
    if (activeTool === 'crop' && cropArea) {
      drawCropOverlay(ctx, cropArea)
    }
  }, [zoom, rotation, imagePosition, imageSize, activeTool, cropArea, filters, textElements])

  const drawCheckerboard = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    const squareSize = 20
    ctx.fillStyle = '#f0f0f0'
    ctx.fillRect(0, 0, width, height)
    
    ctx.fillStyle = '#e0e0e0'
    for (let x = 0; x < width; x += squareSize) {
      for (let y = 0; y < height; y += squareSize) {
        if ((Math.floor(x / squareSize) + Math.floor(y / squareSize)) % 2 === 0) {
          ctx.fillRect(x, y, squareSize, squareSize)
        }
      }
    }
  }

  const drawCropOverlay = (ctx: CanvasRenderingContext2D, crop: CropArea) => {
    const canvas = canvasRef.current!

    // Save the current state
    ctx.save()

    // Draw dark overlay over entire canvas
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Use composite operation to "cut out" the crop area (make it transparent)
    ctx.globalCompositeOperation = 'destination-out'
    ctx.fillRect(crop.x, crop.y, crop.width, crop.height)

    // Reset composite operation
    ctx.globalCompositeOperation = 'source-over'

    // Draw crop border
    ctx.strokeStyle = '#007bff'
    ctx.lineWidth = 2
    ctx.strokeRect(crop.x, crop.y, crop.width, crop.height)

    // Draw corner handles
    const handleSize = 8
    const handles = [
      { x: crop.x - handleSize/2, y: crop.y - handleSize/2 },
      { x: crop.x + crop.width - handleSize/2, y: crop.y - handleSize/2 },
      { x: crop.x - handleSize/2, y: crop.y + crop.height - handleSize/2 },
      { x: crop.x + crop.width - handleSize/2, y: crop.y + crop.height - handleSize/2 }
    ]

    ctx.fillStyle = '#007bff'
    handles.forEach(handle => {
      ctx.fillRect(handle.x, handle.y, handleSize, handleSize)
    })

    // Restore the state
    ctx.restore()
  }

  useEffect(() => {
    if (!isLoading) {
      drawCanvas()
    }
  }, [zoom, rotation, imagePosition, imageSize, activeTool, cropArea, isLoading, filters, textElements, drawCanvas])

  // Save state changes
  useEffect(() => {
    if (!isLoading && onStateChange) {
      const currentState: EditState = {
        zoom,
        rotation,
        imagePosition,
        imageSize,
        originalImageSize,
        filters,
        textElements,
        activeTool,
        showFilterPanel,
        showTextPanel,
        cropArea
      }
      onStateChange(currentState)
    }
  }, [zoom, rotation, imagePosition, imageSize, originalImageSize, filters, textElements, activeTool, showFilterPanel, showTextPanel, cropArea, isLoading, onStateChange])

  const handleCanvasMouseDown = (e: React.MouseEvent) => {
    if (activeTool === 'select') {
      setIsDragging(true)
      setDragStart({ x: e.clientX, y: e.clientY })
    } else if (activeTool === 'crop') {
      const rect = canvasRef.current!.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      setCropArea({
        x: x,
        y: y,
        width: 0,
        height: 0
      })
      setIsDragging(true)
      setDragStart({ x, y })
    } else if (activeTool === 'text') {
      const rect = canvasRef.current!.getBoundingClientRect()
      const canvasX = e.clientX - rect.left
      const canvasY = e.clientY - rect.top

      // Check if click is within image bounds
      const zoomFactor = zoom / 100
      const imageLeft = imagePosition.x
      const imageTop = imagePosition.y
      const imageRight = imagePosition.x + imageSize.width * zoomFactor
      const imageBottom = imagePosition.y + imageSize.height * zoomFactor

      if (canvasX >= imageLeft && canvasX <= imageRight &&
          canvasY >= imageTop && canvasY <= imageBottom) {

        // Convert canvas coordinates to relative image coordinates (0-1)
        const relativeX = (canvasX - imageLeft) / (imageSize.width * zoomFactor)
        const relativeY = (canvasY - imageTop) / (imageSize.height * zoomFactor)

        const newText = {
          id: Date.now().toString(),
          text: 'New Text',
          x: relativeX,
          y: relativeY,
          fontSize: 24,
          color: '#000000'
        }

        setTextElements(prev => [...prev, newText])
        toast.success('Text added! Edit in the text panel.')
      } else {
        toast.error('Please click on the image to add text.')
      }
    }
  }

  const handleCanvasMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return

    if (activeTool === 'select') {
      const deltaX = e.clientX - dragStart.x
      const deltaY = e.clientY - dragStart.y
      
      setImagePosition(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }))
      
      setDragStart({ x: e.clientX, y: e.clientY })
    } else if (activeTool === 'crop' && cropArea) {
      const rect = canvasRef.current!.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top
      
      setCropArea({
        x: Math.min(dragStart.x, x),
        y: Math.min(dragStart.y, y),
        width: Math.abs(x - dragStart.x),
        height: Math.abs(y - dragStart.y)
      })
    }
  }

  const handleCanvasMouseUp = () => {
    setIsDragging(false)
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 500))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25))
  }



  const applyCrop = () => {
    if (!cropArea || !canvasRef.current || !imageRef.current) return

    console.log('Applying crop:', cropArea)
    console.log('Image position:', imagePosition)
    console.log('Image size:', imageSize)
    console.log('Zoom:', zoom)
    console.log('Rotation:', rotation)

    // Create a new canvas for the cropped image
    const cropCanvas = document.createElement('canvas')
    const cropCtx = cropCanvas.getContext('2d')
    if (!cropCtx) return

    // Set crop canvas size to crop area size
    cropCanvas.width = cropArea.width
    cropCanvas.height = cropArea.height

    // Calculate the visible image bounds on canvas
    const zoomFactor = zoom / 100
    const rotationRad = (rotation * Math.PI) / 180

    // For simplicity, let's handle the case without rotation first
    if (rotation === 0) {
      // Calculate which part of the original image the crop area represents
      const scaleX = originalImageSize.width / (imageSize.width * zoomFactor)
      const scaleY = originalImageSize.height / (imageSize.height * zoomFactor)

      // Calculate crop area relative to the image
      const cropRelativeX = (cropArea.x - imagePosition.x) * scaleX
      const cropRelativeY = (cropArea.y - imagePosition.y) * scaleY
      const cropRelativeWidth = cropArea.width * scaleX
      const cropRelativeHeight = cropArea.height * scaleY

      console.log('Crop relative:', { cropRelativeX, cropRelativeY, cropRelativeWidth, cropRelativeHeight })

      // Ensure we don't go outside image bounds
      const sourceX = Math.max(0, cropRelativeX)
      const sourceY = Math.max(0, cropRelativeY)
      const sourceWidth = Math.min(cropRelativeWidth, originalImageSize.width - sourceX)
      const sourceHeight = Math.min(cropRelativeHeight, originalImageSize.height - sourceY)

      console.log('Source:', { sourceX, sourceY, sourceWidth, sourceHeight })

      // Draw the cropped portion
      cropCtx.drawImage(
        imageRef.current,
        sourceX, sourceY, sourceWidth, sourceHeight,
        0, 0, cropArea.width, cropArea.height
      )
    } else {
      // For rotated images, we'll use a simpler approach
      // Create a temporary canvas with the rotated image
      const tempCanvas = document.createElement('canvas')
      const tempCtx = tempCanvas.getContext('2d')
      if (!tempCtx) return

      const cos = Math.abs(Math.cos(rotationRad))
      const sin = Math.abs(Math.sin(rotationRad))
      const newWidth = originalImageSize.width * cos + originalImageSize.height * sin
      const newHeight = originalImageSize.width * sin + originalImageSize.height * cos

      tempCanvas.width = newWidth
      tempCanvas.height = newHeight

      tempCtx.translate(newWidth / 2, newHeight / 2)
      tempCtx.rotate(rotationRad)
      tempCtx.drawImage(
        imageRef.current,
        -originalImageSize.width / 2,
        -originalImageSize.height / 2
      )

      // Now crop from the rotated image
      const scaleX = newWidth / (imageSize.width * zoomFactor)
      const scaleY = newHeight / (imageSize.height * zoomFactor)

      const cropRelativeX = (cropArea.x - imagePosition.x) * scaleX
      const cropRelativeY = (cropArea.y - imagePosition.y) * scaleY
      const cropRelativeWidth = cropArea.width * scaleX
      const cropRelativeHeight = cropArea.height * scaleY

      cropCtx.drawImage(
        tempCanvas,
        cropRelativeX, cropRelativeY, cropRelativeWidth, cropRelativeHeight,
        0, 0, cropArea.width, cropArea.height
      )
    }

    // Create a new image from the cropped canvas and update the editor
    cropCanvas.toBlob((blob) => {
      if (blob) {
        // Create a new image from the blob and update the editor
        const newImg = new Image()
        newImg.onload = () => {
          // Update the current image
          imageRef.current = newImg
          setOriginalImageSize({ width: newImg.width, height: newImg.height })

          // Reset transformations
          setRotation(0)
          setZoom(100)

          // Update text elements to maintain their relative positions after crop
          if (cropArea) {
            const cropRelativeX = (cropArea.x - imagePosition.x) / (imageSize.width * (zoom / 100))
            const cropRelativeY = (cropArea.y - imagePosition.y) / (imageSize.height * (zoom / 100))
            const cropRelativeWidth = cropArea.width / (imageSize.width * (zoom / 100))
            const cropRelativeHeight = cropArea.height / (imageSize.height * (zoom / 100))

            setTextElements(prev => prev.map(textElement => {
              // Adjust text position relative to the new cropped image
              const newX = (textElement.x - cropRelativeX) / cropRelativeWidth
              const newY = (textElement.y - cropRelativeY) / cropRelativeHeight

              // Only keep text elements that are within the cropped area
              if (newX >= 0 && newX <= 1 && newY >= 0 && newY <= 1) {
                return {
                  ...textElement,
                  x: newX,
                  y: newY
                }
              }
              return null
            }).filter(Boolean) as typeof prev)
          }

          // Recalculate image size and position
          const canvas = canvasRef.current!
          const canvasWidth = canvas.width
          const canvasHeight = canvas.height
          const imgAspectRatio = newImg.width / newImg.height
          const canvasAspectRatio = canvasWidth / canvasHeight

          let displayWidth, displayHeight

          if (imgAspectRatio > canvasAspectRatio) {
            displayWidth = canvasWidth * 0.8
            displayHeight = displayWidth / imgAspectRatio
          } else {
            displayHeight = canvasHeight * 0.8
            displayWidth = displayHeight * imgAspectRatio
          }

          setImageSize({ width: displayWidth, height: displayHeight })
          setImagePosition({
            x: (canvasWidth - displayWidth) / 2,
            y: (canvasHeight - displayHeight) / 2
          })

          toast.success('Image cropped successfully!')
        }

        newImg.src = URL.createObjectURL(blob)

        // Also call the completion callback
        onEditComplete(blob)
      }
    }, 'image/png', 0.9)

    setCropArea(null)
    setActiveTool('select')
  }

  const handleRotateClick = () => {
    if (!imageRef.current || !canvasRef.current) return

    console.log('Rotating image by 90 degrees')

    // Create a new canvas for the rotated image
    const rotateCanvas = document.createElement('canvas')
    const rotateCtx = rotateCanvas.getContext('2d')
    if (!rotateCtx) return

    const img = imageRef.current
    const newRotation = (rotation + 90) % 360
    const rotationRad = (newRotation * Math.PI) / 180

    // Calculate new canvas dimensions after rotation
    const cos = Math.abs(Math.cos(rotationRad))
    const sin = Math.abs(Math.sin(rotationRad))
    const newWidth = originalImageSize.width * cos + originalImageSize.height * sin
    const newHeight = originalImageSize.width * sin + originalImageSize.height * cos

    rotateCanvas.width = newWidth
    rotateCanvas.height = newHeight

    // Draw the rotated image
    rotateCtx.translate(newWidth / 2, newHeight / 2)
    rotateCtx.rotate(rotationRad)
    rotateCtx.drawImage(
      img,
      -originalImageSize.width / 2,
      -originalImageSize.height / 2
    )

    // Create a new image from the rotated canvas
    rotateCanvas.toBlob((blob) => {
      if (blob) {
        const newImg = new Image()
        newImg.onload = () => {
          // Update the current image
          imageRef.current = newImg
          setOriginalImageSize({ width: newImg.width, height: newImg.height })

          // Reset rotation since we've applied it to the image
          setRotation(0)

          // Update text elements for 90-degree rotation
          // For 90-degree clockwise rotation: new_x = 1 - old_y, new_y = old_x
          setTextElements(prev => prev.map(textElement => ({
            ...textElement,
            x: 1 - textElement.y,
            y: textElement.x
          })))

          // Recalculate image size and position
          const canvas = canvasRef.current!
          const canvasWidth = canvas.width
          const canvasHeight = canvas.height
          const imgAspectRatio = newImg.width / newImg.height
          const canvasAspectRatio = canvasWidth / canvasHeight

          let displayWidth, displayHeight

          if (imgAspectRatio > canvasAspectRatio) {
            displayWidth = canvasWidth * 0.8
            displayHeight = displayWidth / imgAspectRatio
          } else {
            displayHeight = canvasHeight * 0.8
            displayWidth = displayHeight * imgAspectRatio
          }

          setImageSize({ width: displayWidth, height: displayHeight })
          setImagePosition({
            x: (canvasWidth - displayWidth) / 2,
            y: (canvasHeight - displayHeight) / 2
          })

          toast.success('Image rotated 90°')
        }

        newImg.src = URL.createObjectURL(blob)

        // Also call the completion callback
        onEditComplete(blob)
      }
    }, 'image/png', 0.9)
  }

  const handleExport = () => {
    if (!canvasRef.current || !imageRef.current) return

    console.log('Exporting image with all effects...')

    // Create a new canvas for export with all effects applied
    const exportCanvas = document.createElement('canvas')
    const exportCtx = exportCanvas.getContext('2d')
    if (!exportCtx) return

    // Set export canvas size to original image size
    exportCanvas.width = originalImageSize.width
    exportCanvas.height = originalImageSize.height

    // Apply filters to export context
    let filterString = ''
    if (filters.brightness !== 0) {
      filterString += `brightness(${100 + filters.brightness}%) `
    }
    if (filters.contrast !== 0) {
      filterString += `contrast(${100 + filters.contrast}%) `
    }
    if (filters.saturation !== 0) {
      filterString += `saturate(${100 + filters.saturation}%) `
    }
    if (filters.blur > 0) {
      filterString += `blur(${filters.blur}px) `
    }

    if (filterString) {
      exportCtx.filter = filterString.trim()
    }

    // Draw the image with filters
    exportCtx.drawImage(imageRef.current, 0, 0)

    // Reset filter for text
    exportCtx.filter = 'none'

    // Draw text elements using relative coordinates
    textElements.forEach(textElement => {
      // Convert relative coordinates (0-1) to absolute coordinates on original image
      const textX = textElement.x * originalImageSize.width
      const textY = textElement.y * originalImageSize.height
      const fontSize = textElement.fontSize * (originalImageSize.width / imageSize.width)

      // Only draw text if it's within image bounds (should always be true with relative coords)
      if (textX >= 0 && textX <= originalImageSize.width &&
          textY >= 0 && textY <= originalImageSize.height) {
        exportCtx.font = `${fontSize}px Arial`
        exportCtx.fillStyle = textElement.color
        exportCtx.fillText(textElement.text, textX, textY)
      }
    })

    // Create download
    exportCanvas.toBlob((blob) => {
      if (blob) {
        // Create download link
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `edited-image-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        // Also call the completion callback
        onEditComplete(blob)
        toast.success('Image downloaded successfully!')
      }
    }, 'image/png', 0.9)
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden relative h-full flex flex-col">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white dark:bg-gray-800 z-20 flex items-center justify-center">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <span className="ml-4 text-gray-600 dark:text-gray-400">Loading image...</span>
          </div>
        </div>
      )}

      {/* Top Toolbar */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-1">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => {
                setActiveTool(tool.id as Tool)
                if (tool.id === 'rotate') {
                  handleRotateClick()
                  return
                }
                if (tool.id === 'filter') {
                  setShowFilterPanel(!showFilterPanel)
                  setShowTextPanel(false)
                } else if (tool.id === 'text') {
                  setShowTextPanel(!showTextPanel)
                  setShowFilterPanel(false)
                } else {
                  setShowFilterPanel(false)
                  setShowTextPanel(false)
                }
              }}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeTool === tool.id
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
              title={`${tool.label} (${tool.shortcut})`}
            >
              <tool.icon className="w-5 h-5" />
              <span className="text-xs mt-1">{tool.label}</span>
            </button>
          ))}
        </div>

        <div className="flex items-center space-x-4">
          {/* Zoom Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleZoomOut}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <span className="text-sm text-gray-600 dark:text-gray-300 w-16 text-center">
              {zoom}%
            </span>
            <button
              onClick={handleZoomIn}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {/* <button
              onClick={handleRotateClick}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <RotateCw className="w-4 h-4" />
            </button> */}
            
            {activeTool === 'crop' && cropArea && (
              <button
                onClick={applyCrop}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Apply Crop
              </button>
            )}
            
            <button
              onClick={handleExport}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Save</span>
            </button>
          </div>
        </div>
      </div>

      {/* Canvas Area */}
      <div className="flex-1 flex">
        <div
          ref={containerRef}
          className="flex-1 relative overflow-hidden bg-gray-100 dark:bg-gray-900"
          style={{ cursor: activeTool === 'select' ? 'move' : activeTool === 'crop' ? 'crosshair' : 'default' }}
        >
          <canvas
            ref={canvasRef}
            className="absolute inset-0"
            onMouseDown={handleCanvasMouseDown}
            onMouseMove={handleCanvasMouseMove}
            onMouseUp={handleCanvasMouseUp}
            onMouseLeave={handleCanvasMouseUp}
          />
        </div>

        {/* Filter Panel */}
        {showFilterPanel && (
          <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Filters</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Brightness: {filters.brightness}
                </label>
                <input
                  type="range"
                  min={-100}
                  max={100}
                  value={filters.brightness}
                  onChange={(e) => setFilters(prev => ({ ...prev, brightness: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contrast: {filters.contrast}
                </label>
                <input
                  type="range"
                  min={-100}
                  max={100}
                  value={filters.contrast}
                  onChange={(e) => setFilters(prev => ({ ...prev, contrast: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Saturation: {filters.saturation}
                </label>
                <input
                  type="range"
                  min={-100}
                  max={100}
                  value={filters.saturation}
                  onChange={(e) => setFilters(prev => ({ ...prev, saturation: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Blur: {filters.blur}px
                </label>
                <input
                  type="range"
                  min={0}
                  max={10}
                  value={filters.blur}
                  onChange={(e) => setFilters(prev => ({ ...prev, blur: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <button
                onClick={() => setFilters({ brightness: 0, contrast: 0, saturation: 0, blur: 0 })}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Reset Filters
              </button>
            </div>
          </div>
        )}

        {/* Text Panel */}
        {showTextPanel && (
          <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Text Elements</h3>

            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Click on the canvas to add text
              </p>

              {textElements.map((textElement) => (
                <div key={textElement.id} className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={textElement.text}
                      onChange={(e) => {
                        setTextElements(prev => prev.map(el =>
                          el.id === textElement.id ? { ...el, text: e.target.value } : el
                        ))
                      }}
                      className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm"
                      placeholder="Enter text..."
                    />

                    <div className="flex items-center space-x-2">
                      <label className="text-xs text-gray-600 dark:text-gray-400">Size:</label>
                      <input
                        type="range"
                        min={12}
                        max={72}
                        value={textElement.fontSize}
                        onChange={(e) => {
                          setTextElements(prev => prev.map(el =>
                            el.id === textElement.id ? { ...el, fontSize: Number(e.target.value) } : el
                          ))
                        }}
                        className="flex-1 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                      />
                      <span className="text-xs text-gray-600 dark:text-gray-400 w-8">{textElement.fontSize}</span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <label className="text-xs text-gray-600 dark:text-gray-400">Color:</label>
                      <input
                        type="color"
                        value={textElement.color}
                        onChange={(e) => {
                          setTextElements(prev => prev.map(el =>
                            el.id === textElement.id ? { ...el, color: e.target.value } : el
                          ))
                        }}
                        className="w-8 h-6 rounded border border-gray-300 dark:border-gray-600 cursor-pointer"
                      />
                    </div>

                    <button
                      onClick={() => {
                        setTextElements(prev => prev.filter(el => el.id !== textElement.id))
                      }}
                      className="w-full px-2 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
