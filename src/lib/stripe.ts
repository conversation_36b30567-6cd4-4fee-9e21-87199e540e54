import Stripe from 'stripe'

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia'
})

export const stripeConfig = {
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
  secretKey: process.env.STRIPE_SECRET_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!
}

// Stripe price IDs for different plans (these would be created in Stripe dashboard)
export const stripePriceIds = {
  pro_monthly: 'price_pro_monthly', // Replace with actual Stripe price ID
  pro_yearly: 'price_pro_yearly'   // Replace with actual Stripe price ID
}

export async function createCheckoutSession({
  priceId,
  userId,
  userEmail,
  orderId,
  successUrl,
  cancelUrl
}: {
  priceId: string
  userId: string
  userEmail: string
  orderId: string
  successUrl: string
  cancelUrl: string
}) {
  const session = await stripe.checkout.sessions.create({
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: successUrl,
    cancel_url: cancelUrl,
    client_reference_id: orderId,
    customer_email: userEmail,
    metadata: {
      user_id: userId,
      order_id: orderId
    },
    subscription_data: {
      metadata: {
        user_id: userId,
        order_id: orderId
      }
    }
  })

  return session
}

export async function createCustomerPortalSession(customerId: string, returnUrl: string) {
  const session = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  })

  return session
}

export async function getSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId)
    return subscription
  } catch (error) {
    console.error('Error retrieving subscription:', error)
    return null
  }
}

export async function cancelSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.cancel(subscriptionId)
    return subscription
  } catch (error) {
    console.error('Error canceling subscription:', error)
    throw error
  }
}

export async function updateSubscription(subscriptionId: string, updates: Stripe.SubscriptionUpdateParams) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, updates)
    return subscription
  } catch (error) {
    console.error('Error updating subscription:', error)
    throw error
  }
}

// Helper function to format Stripe amounts (cents to dollars)
export function formatStripeAmount(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount / 100)
}

// Helper function to get subscription status in human-readable format
export function getSubscriptionStatusText(status: Stripe.Subscription.Status): string {
  switch (status) {
    case 'active':
      return 'Active'
    case 'trialing':
      return 'Trial'
    case 'past_due':
      return 'Past Due'
    case 'canceled':
      return 'Canceled'
    case 'unpaid':
      return 'Unpaid'
    case 'incomplete':
      return 'Incomplete'
    case 'incomplete_expired':
      return 'Expired'
    case 'paused':
      return 'Paused'
    default:
      return 'Unknown'
  }
}
