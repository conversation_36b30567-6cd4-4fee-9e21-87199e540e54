'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { CheckCircle, Crown, ArrowRight, Home } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'

export default function SubscriptionSuccessPage() {
  const router = useRouter()
  const { user, refreshUser } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Refresh user data to get updated subscription info
    const refreshData = async () => {
      await refreshUser()
      setIsLoading(false)
    }

    // Wait a moment for webhook to process
    setTimeout(refreshData, 3000)
  }, [refreshUser])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Processing your subscription...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-16">
        <motion.div
          className="max-w-2xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Success Icon */}
          <motion.div
            className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <CheckCircle className="w-12 h-12 text-green-600" />
          </motion.div>

          {/* Success Message */}
          <motion.h1
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Welcome to Pro! 🎉
          </motion.h1>

          <motion.p
            className="text-xl text-gray-600 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Your subscription has been activated successfully. You now have access to all Pro features!
          </motion.p>

          {/* Pro Features */}
          <motion.div
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <div className="flex items-center justify-center mb-6">
              <Crown className="w-8 h-8 text-yellow-500 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Pro Features Unlocked</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-gray-700">Unlimited compressions</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-gray-700">200MB max file size</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-gray-700">All formats support</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-gray-700">Batch processing</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-gray-700">API access</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-gray-700">Priority support</span>
              </div>
            </div>
          </motion.div>

          {/* User Info */}
          {user && (
            <motion.div
              className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Subscription Details</h3>
              <div className="text-blue-700">
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Plan:</strong> Pro {user.membership_status === 'trial' ? '(Trial)' : ''}</p>
                {user.membership_end_date && (
                  <p><strong>Next billing:</strong> {new Date(user.membership_end_date).toLocaleDateString()}</p>
                )}
              </div>
            </motion.div>
          )}

          {/* Action Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <button
              onClick={() => router.push('/')}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg flex items-center justify-center"
            >
              Start Compressing
              <ArrowRight className="w-5 h-5 ml-2" />
            </button>
            
            <button
              onClick={() => router.push('/profile')}
              className="border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-900 font-semibold py-3 px-8 rounded-xl transition-colors flex items-center justify-center"
            >
              <Home className="w-5 h-5 mr-2" />
              View Profile
            </button>
          </motion.div>

          {/* Support Info */}
          <motion.div
            className="mt-12 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <p className="text-gray-500 text-sm">
              Need help? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
