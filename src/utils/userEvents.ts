/**
 * User events system for real-time updates
 */

export interface UserUpdateEvent {
  type: 'points_updated' | 'membership_updated' | 'user_refreshed'
  data: {
    points?: number
    membership_level?: string
    membership_status?: string
    [key: string]: any
  }
}

class UserEventEmitter {
  private listeners: Map<string, Array<(event: UserUpdateEvent) => void>> = new Map()

  /**
   * Subscribe to user update events
   */
  on(eventType: string, callback: (event: UserUpdateEvent) => void) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, [])
    }
    this.listeners.get(eventType)!.push(callback)

    // Return unsubscribe function
    return () => {
      const callbacks = this.listeners.get(eventType)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  /**
   * Emit user update event
   */
  emit(event: UserUpdateEvent) {
    const callbacks = this.listeners.get(event.type)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          console.error('Error in user event callback:', error)
        }
      })
    }
  }

  /**
   * Remove all listeners for an event type
   */
  off(eventType: string) {
    this.listeners.delete(eventType)
  }

  /**
   * Remove all listeners
   */
  removeAllListeners() {
    this.listeners.clear()
  }
}

// Global instance
export const userEvents = new UserEventEmitter()

/**
 * Helper functions for common user update events
 */
export const UserEventHelpers = {
  /**
   * Emit points updated event
   */
  emitPointsUpdated(newPoints: number) {
    userEvents.emit({
      type: 'points_updated',
      data: { points: newPoints }
    })
  },

  /**
   * Emit membership updated event
   */
  emitMembershipUpdated(membershipLevel: string, membershipStatus?: string) {
    userEvents.emit({
      type: 'membership_updated',
      data: { 
        membership_level: membershipLevel,
        membership_status: membershipStatus
      }
    })
  },

  /**
   * Emit user refreshed event
   */
  emitUserRefreshed(userData: any) {
    userEvents.emit({
      type: 'user_refreshed',
      data: userData
    })
  }
}

/**
 * React hook for listening to user events
 */
import { useEffect } from 'react'

export function useUserEvents(
  eventType: string, 
  callback: (event: UserUpdateEvent) => void,
  deps: any[] = []
) {
  useEffect(() => {
    const unsubscribe = userEvents.on(eventType, callback)
    return unsubscribe
  }, deps)
}
