'use client'

import { useState, useCallback, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileUpload } from '@/components/FileUpload'
import { VideoEditor } from '@/components/VideoEditor'
import { AuthModal } from '@/components/AuthModal'
import { useAuth, usePermissions } from '@/hooks/useAuth'
import { getUploadedFile, clearUploadedFile, getFileCategory, base64ToFile } from '@/utils/fileHandler'
import toast from 'react-hot-toast'
import { 
  Scissors, 
  Sparkles, 
  Zap, 
  Download,
  Settings,
  Video,
  Music,
  Volume2,
  Film,
  Play
} from 'lucide-react'

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  file: File
  originalSize: number
  status: 'pending' | 'editing' | 'completed' | 'error'
  editedFile?: Blob
  downloadUrl?: string
}

export default function VideoEditorPage() {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [currentFile, setCurrentFile] = useState<UploadedFile | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showAuthModal, setShowAuthModal] = useState(false)
  const { user, refreshUser } = useAuth()
  const permissions = usePermissions()

  // Check for uploaded file from home page
  useEffect(() => {
    const uploadedFile = getUploadedFile()
    if (uploadedFile && getFileCategory(uploadedFile.type) === 'video') {
      const file = base64ToFile(uploadedFile.data, uploadedFile.name, uploadedFile.type, Date.now())
      handleFileUpload([file])
      clearUploadedFile()
    }
  }, [])

  const handleFileUpload = useCallback((uploadedFiles: File[]) => {
    const videoFiles = uploadedFiles.filter(file => file.type.startsWith('video/'))
    
    if (videoFiles.length === 0) {
      toast.error('Please upload video files only')
      return
    }

    const newFiles: UploadedFile[] = videoFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      file,
      originalSize: file.size,
      status: 'pending'
    }))

    setFiles(prev => [...prev, ...newFiles])
    
    // Auto-select first file for editing
    if (newFiles.length > 0 && !currentFile) {
      setCurrentFile(newFiles[0])
    }
  }, [currentFile])

  const handleFileSelect = (file: UploadedFile) => {
    setCurrentFile(file)
  }

  const handleEditComplete = (editedBlob: Blob) => {
    if (!currentFile) return

    const downloadUrl = URL.createObjectURL(editedBlob)
    
    setFiles(prev => prev.map(file => 
      file.id === currentFile.id 
        ? { 
            ...file, 
            status: 'completed',
            editedFile: editedBlob,
            downloadUrl 
          }
        : file
    ))

    setCurrentFile(prev => prev ? {
      ...prev,
      status: 'completed',
      editedFile: editedBlob,
      downloadUrl
    } : null)

    toast.success('Video edited successfully!')
  }

  const handleDownload = (file: UploadedFile) => {
    if (!file.downloadUrl) return

    const link = document.createElement('a')
    link.href = file.downloadUrl
    link.download = `edited_${file.name}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => {
      const newFiles = prev.filter(f => f.id !== fileId)
      if (currentFile?.id === fileId) {
        setCurrentFile(newFiles.length > 0 ? newFiles[0] : null)
      }
      return newFiles
    })
  }

  const clearAllFiles = () => {
    files.forEach(file => {
      if (file.downloadUrl) {
        URL.revokeObjectURL(file.downloadUrl)
      }
    })
    setFiles([])
    setCurrentFile(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mr-4">
              <Scissors className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Video Editor
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Professional video editing in your browser
              </p>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Scissors className="w-6 h-6 text-blue-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Trim & Cut</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Music className="w-6 h-6 text-purple-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Add Audio</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Volume2 className="w-6 h-6 text-green-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Volume Control</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Film className="w-6 h-6 text-orange-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Effects</span>
            </div>
          </div>
        </motion.div>

        {files.length === 0 ? (
          /* Upload Section */
          <motion.div
            initial={{ opacity: 1, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <FileUpload
              onFileUpload={handleFileUpload}
              acceptedTypes={{
                'video/mp4': ['.mp4'],
                'video/avi': ['.avi'],
                'video/mov': ['.mov'],
                'video/webm': ['.webm'],
                'video/mkv': ['.mkv'],
                'video/wmv': ['.wmv']
              }}
              maxFileSize={500 * 1024 * 1024} // 500MB for videos
              title="Drop your videos here"
              subtitle="Support for MP4, AVI, MOV, WebM, MKV, WMV up to 500MB"
            />
          </motion.div>
        ) : (
          /* Editor Section */
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* File List */}
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Files</h3>
                  <button
                    onClick={clearAllFiles}
                    className="text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Clear All
                  </button>
                </div>
                <div className="space-y-2">
                  {files.map((file) => (
                    <div
                      key={file.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        currentFile?.id === file.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                      }`}
                      onClick={() => handleFileSelect(file)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            {file.name}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {file.status === 'completed' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDownload(file)
                              }}
                              className="p-1 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                            >
                              <Download className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              removeFile(file.id)
                            }}
                            className="p-1 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Editor */}
            <div className="lg:col-span-3">
              {currentFile && (
                <VideoEditor
                  file={currentFile.file}
                  onEditComplete={handleEditComplete}
                />
              )}
            </div>
          </div>
        )}

        {/* Auth Modal */}
        <AuthModal 
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </div>
    </div>
  )
}
