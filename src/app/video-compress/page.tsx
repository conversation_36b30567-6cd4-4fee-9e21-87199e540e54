'use client'

import { useState, useCallback, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileUpload } from '@/components/FileUpload'
import { FileCard } from '@/components/FileCard'
import { CompressionSettings } from '@/components/CompressionSettings'
import { ProcessingStats } from '@/components/ProcessingStats'
import { AuthModal } from '@/components/AuthModal'
import { useCompressionManager } from '@/hooks/useCompressionManager'
import { useAuth, usePermissions } from '@/hooks/useAuth'
import { CompressionOptions } from '@/types'
import { getUploadedFile, clearUploadedFile, getFileCategory, base64ToFile } from '@/utils/fileHandler'
import toast from 'react-hot-toast'
import { 
  Video, 
  Sparkles, 
  Zap, 
  Download,
  Settings,
  Play,
  Film,
  Monitor,
  Smartphone,
  Globe
} from 'lucide-react'

export default function VideoCompressPage() {
  const { user } = useAuth()
  const { canCompress, canUseAdvancedFormats } = usePermissions()
  const [showAuthModal, setShowAuthModal] = useState(false)

  const [compressionOptions, setCompressionOptions] = useState<CompressionOptions>({
    quality: 0.5, // Balanced quality for videos
    maintainAspectRatio: true,
    removeMetadata: true,
    outputFormat: 'mp4',
    // Video-specific options (will be handled by type casting in API)
    ...(({
      codec: 'h264',
      fps: 30,
      bitrate: '1000k'
    }) as any)
  })

  const {
    files,
    isProcessing,
    currentProcessingIndex,
    addFiles,
    removeFile,
    compressAllFiles,
    retryFile,
    reprocessFile,
    downloadFile: downloadSingleFile,
    downloadAllFiles,
    clearAllFiles,
    getStats
  } = useCompressionManager()

  const stats = getStats()

  // Check for uploaded file from hero section
  useEffect(() => {
    const uploadedFileData = getUploadedFile()
    if (uploadedFileData) {
      // Check if it's a video file
      const fileCategory = getFileCategory(uploadedFileData.type)
      if (fileCategory === 'video') {
        try {
          // Create a File object from the base64 data
          const file = base64ToFile(
            uploadedFileData.data,
            uploadedFileData.name,
            uploadedFileData.type,
            uploadedFileData.lastModified
          )

          // Add the file to the compression manager
          addFiles([file])

          // Clear the uploaded file data
          clearUploadedFile()

          // Show success message
          toast.success(`Video "${uploadedFileData.name}" loaded successfully!`)
        } catch (error) {
          console.error('Failed to load uploaded file:', error)
          toast.error('Failed to load the uploaded file. Please try uploading again.')
          clearUploadedFile()
        }
      } else {
        // Wrong file type, clear and show error
        clearUploadedFile()
        if (fileCategory === 'image') {
          toast.error('Image files should be processed on the image compression page.')
        } else if (fileCategory === 'gif') {
          toast.error('GIF files should be processed on the GIF compression page.')
        } else {
          toast.error('This file type is not supported on the video compression page.')
        }
      }
    }
  }, [addFiles])

  const handleFileUpload = useCallback((uploadedFiles: File[]) => {
    // Filter only video files
    const videoFiles = uploadedFiles.filter(file =>
      file.type.startsWith('video/')
    )

    if (videoFiles.length !== uploadedFiles.length) {
      toast.error('Only video files are allowed on this page. Image files should be processed on the Image or GIF compression pages.')
    }

    // Check permissions for each video file
    for (const file of videoFiles) {
      const fileSizeMb = file.size / (1024 * 1024)

      // Check file size permission
      const compressionPermission = canCompress(fileSizeMb)
      if (!compressionPermission.allowed) {
        if (!user) {
          setShowAuthModal(true)
        } else {
          toast.error(compressionPermission.reason || 'Permission denied')
        }
        return
      }

      // Check format permission (video files require Pro)
      const formatPermission = canUseAdvancedFormats(file.type)
      if (!formatPermission.allowed) {
        if (!user) {
          setShowAuthModal(true)
        } else {
          toast.error(formatPermission.reason || 'Video compression requires Pro membership')
        }
        return
      }
    }

    if (videoFiles.length > 0) {
      addFiles(videoFiles)
    }
  }, [addFiles, canCompress, canUseAdvancedFormats, user, setShowAuthModal])

  const handleCompress = useCallback(() => {
    compressAllFiles(compressionOptions)
  }, [compressAllFiles, compressionOptions])

  const handleRetry = (id: string) => {
    retryFile(id, compressionOptions)
  }

  const handleReprocess = (id: string) => {
    reprocessFile(id, compressionOptions)
  }

  const handlePreview = (id: string) => {
    console.log('Preview video:', id)
  }

  return (
    <div className="bg-gradient-to-br from-green-50 via-white to-emerald-50 dark:from-green-900/20 dark:via-gray-900 dark:to-emerald-900/20">

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 1, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 dark:from-green-400 dark:to-emerald-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Video className="w-10 h-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-400 dark:bg-red-500 rounded-full flex items-center justify-center animate-pulse">
                <Play className="w-4 h-4 text-red-800 dark:text-red-900" />
              </div>
            </div>
          </div>

          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Video <span className="text-green-600 dark:text-green-400">Compression</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            Compress video files with professional-grade encoding. Support for MP4, AVI, MOV, WebM
            with advanced codec options and quality control. Convert videos to animated GIFs for social media.
          </p>

          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center">
              <Film className="w-4 h-4 mr-2 text-green-500 dark:text-green-400" />
              Multiple Formats
            </div>
            <div className="flex items-center">
              <Monitor className="w-4 h-4 mr-2 text-blue-500 dark:text-blue-400" />
              Resolution Control
            </div>
            <div className="flex items-center">
              <Smartphone className="w-4 h-4 mr-2 text-purple-500 dark:text-purple-400" />
              Mobile Optimized
            </div>
            <div className="flex items-center">
              <Globe className="w-4 h-4 mr-2 text-orange-500 dark:text-orange-400" />
              Web Streaming Ready
            </div>
          </div>
        </motion.div>

        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 1, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <FileUpload
            onFileUpload={handleFileUpload}
            acceptedTypes={{
              'video/mp4': ['.mp4'],
              'video/avi': ['.avi'],
              'video/mov': ['.mov'],
              'video/webm': ['.webm'],
              'video/mkv': ['.mkv'],
              'video/wmv': ['.wmv']
            }}
            maxFileSize={500 * 1024 * 1024} // 500MB for videos
            title="Drop your videos here"
            subtitle="Support for MP4, AVI, MOV, WebM, MKV, WMV up to 500MB"
          />
        </motion.div>

        {/* Settings and Stats */}
        {files.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
            <div className="lg:col-span-2">
              <CompressionSettings
                type="video"
                options={compressionOptions}
                onChange={setCompressionOptions}
                files={files}
              />
            </div>
            <div>
              <ProcessingStats
                stats={stats}
                isProcessing={isProcessing}
                currentProcessingIndex={currentProcessingIndex}
                onCompress={handleCompress}
                onDownloadAll={downloadAllFiles}
                onClearAll={clearAllFiles}
              />
            </div>
          </div>
        )}

        {/* Files List */}
        {files.length > 0 && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                <Settings className="w-6 h-6 mr-3 text-green-600 dark:text-green-400" />
                Video Files ({files.length})
              </h2>
              {stats.completed > 0 && (
                <button
                  onClick={downloadAllFiles}
                  className="flex items-center px-4 py-2 bg-green-600 dark:bg-green-500 text-white rounded-lg hover:bg-green-700 dark:hover:bg-green-600 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download All ({stats.completed})
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {files.map((file, index) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 1, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <FileCard
                    item={file}
                    onRemove={removeFile}
                    onRetry={handleRetry}
                    onReprocess={handleReprocess}
                    onDownload={downloadSingleFile}
                    onPreview={handlePreview}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Features Section */}
        {files.length === 0 && (
          <motion.div
            className="mt-16"
            initial={{ opacity: 1, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Professional Video Encoding
              </h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Server-side FFmpeg processing with advanced codec support and quality optimization
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Film className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Multi-Format</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Support for MP4, AVI, MOV, WebM, MKV, and convert to GIF
                </p>
              </div>

              <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Monitor className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Quality Control</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Precise CRF-based quality control for optimal results
                </p>
              </div>

              <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Smartphone className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Mobile Ready</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Optimized encoding for mobile devices and streaming
                </p>
              </div>

              <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-orange-600 dark:text-orange-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Fast Processing</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Server-side processing for maximum speed and reliability
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </main>

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        title="Pro Membership Required"
        description="Video compression requires Pro membership. Sign up for a free 14-day trial to get started!"
      />
    </div>
  )
}
