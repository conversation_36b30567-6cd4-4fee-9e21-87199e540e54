'use client'

import { useAuth } from '@/hooks/useAuth'
import { useUserStatus } from '@/hooks/useUserStatus'
import { debugUserStatus, debugPermissions } from '@/utils/debugUser'
import { useEffect } from 'react'

export default function DebugUserPage() {
  const { user, loading } = useAuth()
  const userStatus = useUserStatus()

  useEffect(() => {
    if (user) {
      debugUserStatus(user)
      debugPermissions(user, 'video/mp4', 50) // Test with video file
    }
  }, [user])

  if (loading) {
    return <div className="p-8">Loading...</div>
  }

  if (!user) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">User Debug - Not Logged In</h1>
        <p>Please sign in to see user debug information.</p>
      </div>
    )
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">User Debug Information</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Raw User Data */}
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Raw User Data</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>

        {/* User Status Hook Data */}
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">User Status Hook</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              isProUser: userStatus.isProUser,
              isTrialUser: userStatus.isTrialUser,
              isActiveUser: userStatus.isActiveUser,
              isFreeUser: userStatus.isFreeUser,
              membershipDaysLeft: userStatus.membershipDaysLeft,
              isMembershipActive: userStatus.isMembershipActive,
              displayPoints: userStatus.displayPoints,
              displayMembershipLevel: userStatus.displayMembershipLevel,
              displayMembershipStatus: userStatus.displayMembershipStatus
            }, null, 2)}
          </pre>
        </div>

        {/* Calculated Values */}
        <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Calculated Values</h2>
          <div className="space-y-2 text-sm">
            <div><strong>Current Time:</strong> {new Date().toISOString()}</div>
            <div><strong>Membership End:</strong> {user.membership_end_date || 'N/A'}</div>
            <div><strong>Trial End:</strong> {user.trial_end_date || 'N/A'}</div>
            <div><strong>Is Expired:</strong> {
              user.membership_end_date 
                ? new Date() > new Date(user.membership_end_date) ? 'Yes' : 'No'
                : 'N/A'
            }</div>
            <div><strong>Days Until Expiry:</strong> {
              user.membership_end_date 
                ? Math.ceil((new Date(user.membership_end_date).getTime() - Date.now()) / (1000 * 60 * 60 * 24))
                : 'N/A'
            }</div>
          </div>
        </div>

        {/* Permission Tests */}
        <div className="bg-green-100 dark:bg-green-900 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-3">Permission Tests</h2>
          <div className="space-y-2 text-sm">
            <div><strong>Can Compress (10MB):</strong> {
              user.membership_level === 'pro' 
                ? user.is_membership_active ? 'Yes' : 'No (Expired)'
                : user.points > 0 ? 'Yes' : 'No (No Credits)'
            }</div>
            <div><strong>Can Use Video:</strong> {
              user.membership_level === 'pro' && user.is_membership_active ? 'Yes' : 'No'
            }</div>
            <div><strong>Can Use GIF:</strong> {
              user.membership_level === 'pro' && user.is_membership_active ? 'Yes' : 'No'
            }</div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="mt-6 space-x-4">
        <button
          onClick={() => debugUserStatus(user)}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Log User Status to Console
        </button>
        <button
          onClick={() => debugPermissions(user, 'video/mp4', 50)}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Log Video Permissions to Console
        </button>
      </div>

      {/* Console Instructions */}
      <div className="mt-6 p-4 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
        <h3 className="font-semibold mb-2">Console Debug</h3>
        <p className="text-sm">
          Open browser console (F12) and click the buttons above to see detailed debug information.
          You can also use <code>window.debugUser.debugUserStatus()</code> and 
          <code>window.debugUser.debugPermissions()</code> directly in the console.
        </p>
      </div>
    </div>
  )
}
