'use client'

import { useEffect } from 'react'
import { Toaster, toast } from 'react-hot-toast'

export function DynamicToaster() {
  useEffect(() => {
    // Function to get current theme
    const getCurrentTheme = () => {
      return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
    }

    // Function to update toast styles based on theme
    const updateToastStyles = () => {
      const isDark = getCurrentTheme() === 'dark'
      
      // Update default toast options
      toast.dismiss() // Clear existing toasts to apply new styles
    }

    // Initial setup
    updateToastStyles()

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          updateToastStyles()
        }
      })
    })

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })

    return () => {
      observer.disconnect()
    }
  }, [])

  // Get current theme for styling
  const isDark = typeof window !== 'undefined' && 
    document.documentElement.classList.contains('dark')

  return (
    <Toaster
      position="top-center"
      toastOptions={{
        duration: 4000,
        style: {
          background: isDark ? '#374151' : '#ffffff',
          color: isDark ? '#f3f4f6' : '#374151',
          border: isDark ? '1px solid #4b5563' : '1px solid #e5e7eb',
          borderRadius: '12px',
          boxShadow: isDark 
            ? '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)'
            : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          fontSize: '14px',
          fontWeight: '500',
          padding: '12px 16px',
          maxWidth: '400px',
        },
        success: {
          style: {
            background: isDark ? '#065f46' : '#f0fdf4',
            color: isDark ? '#d1fae5' : '#166534',
            border: isDark ? '1px solid #047857' : '1px solid #bbf7d0',
          },
          iconTheme: {
            primary: isDark ? '#10b981' : '#16a34a',
            secondary: isDark ? '#065f46' : '#f0fdf4',
          },
        },
        error: {
          style: {
            background: isDark ? '#7f1d1d' : '#fef2f2',
            color: isDark ? '#fecaca' : '#991b1b',
            border: isDark ? '1px solid #991b1b' : '1px solid #fecaca',
          },
          iconTheme: {
            primary: isDark ? '#ef4444' : '#dc2626',
            secondary: isDark ? '#7f1d1d' : '#fef2f2',
          },
        },
        loading: {
          style: {
            background: isDark ? '#1e293b' : '#f8fafc',
            color: isDark ? '#cbd5e1' : '#475569',
            border: isDark ? '1px solid #334155' : '1px solid #e2e8f0',
          },
          iconTheme: {
            primary: isDark ? '#60a5fa' : '#3b82f6',
            secondary: isDark ? '#1e293b' : '#f8fafc',
          },
        },
      }}
    />
  )
}
