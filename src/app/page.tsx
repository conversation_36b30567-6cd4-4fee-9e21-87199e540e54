import { <PERSON>ada<PERSON> } from "next";
import { HeroSection } from "@/components/home/<USER>";
import { Features } from "@/components/home/<USER>";
import { Stats } from "@/components/home/<USER>";
import { HowItWorks } from "@/components/home/<USER>";
import { FAQ } from "@/components/home/<USER>";
import { BrandCarousel } from "@/components/BrandCarousel";
import { WhyCompress } from "@/components/WhyCompress";
import { ImageComparison } from "@/components/ImageComparison";
import "@/styles/hero-animations.css";

export const metadata: Metadata = {
  title: "CompressHub - Free Online Image & Video Compression Tool",
  description: "Compress images and videos online for free. Reduce file sizes by up to 90% while maintaining quality. Support for JPEG, PNG, WebP, AVIF, MP4, and more. No upload required - all processing happens in your browser.",
  keywords: "image compression, video compression, file size reducer, optimize images, compress photos, reduce file size, JPEG compressor, PNG optimizer, WebP converter, AVIF compression",
  authors: [{ name: "CompressHub" }],
  creator: "CompressH<PERSON>",
  publisher: "CompressHub",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://compresshub.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "CompressHub - Free Online Image & Video Compression Tool",
    description: "Compress images and videos online for free. Reduce file sizes by up to 90% while maintaining quality. No upload required - all processing happens in your browser.",
    url: 'https://compresshub.com',
    siteName: 'CompressHub',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'CompressHub - Image and Video Compression Tool',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "CompressHub - Free Online Image & Video Compression Tool",
    description: "Compress images and videos online for free. Reduce file sizes by up to 90% while maintaining quality.",
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function Home() {
  return (
    <div className="w-full bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
        {/* Hero Section */}
        <HeroSection />

        {/* Features Section */}
        <Features />

        {/* Stats Section */}
        <Stats />

        {/* How It Works Section */}
        <HowItWorks />

        {/* FAQ Section */}

        {/* Brand Carousel Section */}
        <BrandCarousel />

        {/* Why Compress Section */}
        <WhyCompress />

        {/* Image Comparison Section */}
        <ImageComparison
          beforeImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=800&fit=crop&q=100"
          afterImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=800&fit=crop&q=75"
          beforeLabel="ORIGINAL"
          afterLabel="COMPRESSED"
          beforeSize="1.1 MB"
          afterSize="188 KB"
        />

        <FAQ />

        {/* Final CTA Section */}
        {/* <FinalCTA /> */}
    </div>
  );
}
