'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Volume2,
  VolumeX,
  Download,
  Scissors,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Square,
  Circle
} from 'lucide-react'
import toast from 'react-hot-toast'

interface ProfessionalVideoEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

interface TimelineFrame {
  time: number
  thumbnail: string
}

export function ProfessionalVideoEditor({ file, onEditComplete }: ProfessionalVideoEditorProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const timelineRef = useRef<HTMLDivElement>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)
  const [zoom, setZoom] = useState(1)
  const [timelineFrames, setTimelineFrames] = useState<TimelineFrame[]>([])
  const [playbackRate, setPlaybackRate] = useState(1)

  // Generate timeline frames for preview
  const generateTimelineFrames = useCallback(async (video: HTMLVideoElement) => {
    if (!video || !duration) return
    
    const frames: TimelineFrame[] = []
    const frameCount = Math.min(20, Math.ceil(duration)) // Generate up to 20 frames
    const interval = duration / frameCount
    
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    canvas.width = 160
    canvas.height = 90
    
    for (let i = 0; i < frameCount; i++) {
      const time = i * interval
      video.currentTime = time
      
      await new Promise(resolve => {
        const handleSeeked = () => {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
          const thumbnail = canvas.toDataURL('image/jpeg', 0.7)
          frames.push({ time, thumbnail })
          video.removeEventListener('seeked', handleSeeked)
          resolve(void 0)
        }
        video.addEventListener('seeked', handleSeeked)
      })
    }
    
    setTimelineFrames(frames)
    video.currentTime = 0 // Reset to beginning
  }, [duration])

  const initializeVideo = () => {
    console.log('Initializing video...')
    if (!videoRef.current) {
      console.log('Video ref not available')
      setIsLoading(false)
      toast.error('Failed to initialize video player')
      return
    }

    const video = videoRef.current
    const url = URL.createObjectURL(file)
    console.log('Created video URL:', url)

    const handleLoadedMetadata = async () => {
      try {
        console.log('Video metadata loaded, duration:', video.duration)
        if (video.duration && !isNaN(video.duration)) {
          setDuration(video.duration)
          setEndTime(video.duration)
          setIsLoading(false)
          console.log('Video editor ready')
          
          // Generate timeline frames after metadata is loaded
          setTimeout(() => {
            generateTimelineFrames(video)
          }, 100)
        } else {
          throw new Error('Invalid video duration')
        }
      } catch (error) {
        console.error('Error loading video metadata:', error)
        setIsLoading(false)
        toast.error('Failed to load video metadata')
      }
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    
    const handleError = () => {
      console.error('Video loading error')
      setIsLoading(false)
      toast.error('Failed to load video file')
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('error', handleError)

    video.src = url
    video.load()

    // Cleanup function
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('error', handleError)
      URL.revokeObjectURL(url)
    }
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      initializeVideo()
    }, 50)

    return () => clearTimeout(timer)
  }, [file])

  const togglePlay = () => {
    if (!videoRef.current) return
    
    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }

  const seekTo = (time: number) => {
    if (!videoRef.current) return
    videoRef.current.currentTime = Math.max(0, Math.min(time, duration))
  }

  const toggleMute = () => {
    if (!videoRef.current) return
    
    if (isMuted) {
      videoRef.current.volume = volume
      setIsMuted(false)
    } else {
      videoRef.current.volume = 0
      setIsMuted(true)
    }
  }

  const handleVolumeChange = (newVolume: number) => {
    if (!videoRef.current) return

    setVolume(newVolume)
    videoRef.current.volume = newVolume
    setIsMuted(newVolume === 0)
  }

  const handleTimelineClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!timelineRef.current || !duration) return

    const rect = timelineRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const timelineWidth = duration * zoom * 50
    const clickTime = (clickX / timelineWidth) * duration

    seekTo(Math.max(0, Math.min(clickTime, duration)))
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleExport = async () => {
    if (!videoRef.current) return

    try {
      // For now, we'll just call the completion callback with the original file
      // In a real implementation, you would process the video with the trim settings
      const blob = new Blob([file], { type: file.type })
      onEditComplete(blob)
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export video')
    }
  }

  // Update playback rate when changed
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.playbackRate = playbackRate
    }
  }, [playbackRate])

  return (
    <div className="w-full h-[80vh] bg-gray-900 text-white flex flex-col rounded-lg overflow-hidden">
      {isLoading && (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <span className="ml-3 text-gray-300">Loading video...</span>
        </div>
      )}

      {!isLoading && (
        <>
          {/* Top Toolbar */}
          <div className="bg-gray-800 border-b border-gray-700 p-3 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold">{file.name}</h2>
              <div className="text-sm text-gray-400">
                {formatTime(duration)} • {Math.round(file.size / 1024 / 1024)}MB
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setZoom(Math.max(0.25, zoom - 0.25))}
                className="p-2 hover:bg-gray-700 rounded"
                title="Zoom Out"
              >
                <ZoomOut className="w-4 h-4" />
              </button>
              <span className="text-sm px-2">{Math.round(zoom * 100)}%</span>
              <button
                onClick={() => setZoom(Math.min(4, zoom + 0.25))}
                className="p-2 hover:bg-gray-700 rounded"
                title="Zoom In"
              >
                <ZoomIn className="w-4 h-4" />
              </button>
              
              <div className="w-px h-6 bg-gray-600 mx-2"></div>
              
              <button
                onClick={handleExport}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex">
            {/* Video Preview */}
            <div className="flex-1 bg-black flex items-center justify-center relative">
              <video
                ref={videoRef}
                className="max-w-full max-h-full object-contain"
                controls={false}
                playsInline
              />
              
              {/* Video Overlay Controls */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 rounded-lg px-4 py-2 flex items-center space-x-3">
                <button
                  onClick={() => seekTo(Math.max(0, currentTime - 10))}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  <SkipBack className="w-5 h-5" />
                </button>
                
                <button
                  onClick={togglePlay}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>
                
                <button
                  onClick={() => seekTo(Math.min(duration, currentTime + 10))}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  <SkipForward className="w-5 h-5" />
                </button>
                
                <div className="w-px h-5 bg-gray-500 mx-2"></div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={toggleMute}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                  </button>
                  <input
                    type="range"
                    min={0}
                    max={1}
                    step={0.1}
                    value={isMuted ? 0 : volume}
                    onChange={(e) => handleVolumeChange(Number(e.target.value))}
                    className="w-16 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
                
                <div className="text-white text-sm">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </div>
              </div>
            </div>

            {/* Right Panel - Tools */}
            <div className="w-80 bg-gray-800 border-l border-gray-700 p-4">
              <h3 className="text-lg font-semibold mb-4">Video Tools</h3>

              {/* Trim Section */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-300 mb-3 flex items-center">
                  <Scissors className="w-4 h-4 mr-2" />
                  Trim Video
                </h4>

                <div className="space-y-3">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">
                      Start Time: {formatTime(startTime)}
                    </label>
                    <input
                      type="range"
                      min={0}
                      max={duration}
                      value={startTime}
                      onChange={(e) => {
                        const value = Number(e.target.value)
                        setStartTime(Math.min(value, endTime - 1))
                      }}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>

                  <div>
                    <label className="block text-xs text-gray-400 mb-1">
                      End Time: {formatTime(endTime)}
                    </label>
                    <input
                      type="range"
                      min={0}
                      max={duration}
                      value={endTime}
                      onChange={(e) => {
                        const value = Number(e.target.value)
                        setEndTime(Math.max(value, startTime + 1))
                      }}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => seekTo(startTime)}
                      className="flex-1 px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs"
                    >
                      Go to Start
                    </button>
                    <button
                      onClick={() => seekTo(endTime)}
                      className="flex-1 px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs"
                    >
                      Go to End
                    </button>
                  </div>
                </div>
              </div>

              {/* Playback Speed */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-300 mb-3">Playback Speed</h4>
                <div className="grid grid-cols-4 gap-1">
                  {[0.5, 1, 1.5, 2].map(speed => (
                    <button
                      key={speed}
                      onClick={() => setPlaybackRate(speed)}
                      className={`px-2 py-1 rounded text-xs ${
                        playbackRate === speed
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                      }`}
                    >
                      {speed}x
                    </button>
                  ))}
                </div>
              </div>

              {/* Video Info */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-300 mb-3">Video Information</h4>
                <div className="space-y-2 text-xs text-gray-400">
                  <div className="flex justify-between">
                    <span>Duration:</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Trim Duration:</span>
                    <span>{formatTime(endTime - startTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>File Size:</span>
                    <span>{Math.round(file.size / 1024 / 1024)}MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Format:</span>
                    <span>{file.type.split('/')[1].toUpperCase()}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Timeline */}
          <div className="bg-gray-800 border-t border-gray-700 p-4">
            <div className="mb-2 flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-300">Timeline</h4>
              <div className="text-xs text-gray-400">
                Duration: {formatTime(endTime - startTime)}
              </div>
            </div>

            {/* Timeline Container */}
            <div className="relative bg-gray-900 rounded-lg p-2 h-28 overflow-x-auto">
              <div
                ref={timelineRef}
                className="relative h-full flex items-center cursor-pointer"
                style={{ width: `${Math.max(100, duration * zoom * 50)}px` }}
                onClick={handleTimelineClick}
              >
                {/* Timeline Background */}
                <div className="absolute inset-0 bg-gradient-to-r from-gray-800 to-gray-700 rounded"></div>

                {/* Timeline Frames */}
                <div className="relative flex h-20 space-x-0.5">
                  {timelineFrames.map((frame, index) => (
                    <div
                      key={index}
                      className="relative flex-shrink-0 cursor-pointer hover:scale-105 transition-transform duration-150"
                      style={{ width: `${zoom * 50}px` }}
                      onClick={() => seekTo(frame.time)}
                    >
                      <img
                        src={frame.thumbnail}
                        alt={`Frame at ${formatTime(frame.time)}`}
                        className="w-full h-full object-cover rounded border-2 border-gray-600 hover:border-blue-400 transition-colors"
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent text-white text-xs px-1 py-1 text-center">
                        {formatTime(frame.time)}
                      </div>

                      {/* Frame number indicator */}
                      <div className="absolute top-1 left-1 bg-black/70 text-white text-xs px-1 rounded">
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Trim Selection Area */}
                <div
                  className="absolute top-0 bottom-0 bg-blue-500/20 border-l-2 border-r-2 border-blue-500 z-5"
                  style={{
                    left: `${(startTime / duration) * (duration * zoom * 50)}px`,
                    width: `${((endTime - startTime) / duration) * (duration * zoom * 50)}px`
                  }}
                >
                  <div className="absolute top-0 left-0 bg-blue-500 text-white text-xs px-1 py-0.5 rounded-br">
                    Start
                  </div>
                  <div className="absolute top-0 right-0 bg-blue-500 text-white text-xs px-1 py-0.5 rounded-bl">
                    End
                  </div>
                </div>

                {/* Current Time Indicator */}
                <div
                  className="absolute top-0 bottom-0 w-1 bg-red-500 z-20 shadow-lg"
                  style={{
                    left: `${(currentTime / duration) * (duration * zoom * 50)}px`
                  }}
                >
                  <div className="absolute -top-3 -left-2 w-5 h-5 bg-red-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                    <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                  </div>
                  <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-red-500 rotate-45"></div>
                </div>

                {/* Trim Handles */}
                <div
                  className="absolute top-0 bottom-0 w-1 bg-green-500 z-15 cursor-ew-resize"
                  style={{
                    left: `${(startTime / duration) * (duration * zoom * 50)}px`
                  }}
                >
                  <div className="absolute -top-2 -left-1.5 w-4 h-4 bg-green-500 rounded border-2 border-white shadow-md"></div>
                </div>

                <div
                  className="absolute top-0 bottom-0 w-1 bg-green-500 z-15 cursor-ew-resize"
                  style={{
                    left: `${(endTime / duration) * (duration * zoom * 50)}px`
                  }}
                >
                  <div className="absolute -top-2 -left-1.5 w-4 h-4 bg-green-500 rounded border-2 border-white shadow-md"></div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
