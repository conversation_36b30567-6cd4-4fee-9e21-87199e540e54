export const googleOneTapOptions = {
  client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
  auto_select: false,
  cancel_on_tap_outside: false,
  context: 'signin' as const,
  itp_support: true,
  use_fedcm_for_prompt: false, // Disable FedCM to avoid CORS issues
}

export const initializeGoogleOneTap = (
  options: typeof googleOneTapOptions,
  callback: (response: any) => void
) => {
  if (!options.client_id) {
    throw new Error('Google Client ID is required')
  }

  if (typeof window === 'undefined') return

  const loadGoogleScript = () => {
    return new Promise<void>((resolve, reject) => {
      // Check if script already exists
      const existingScript = document.querySelector(
        'script[src="https://accounts.google.com/gsi/client"]'
      )
      
      if (existingScript) {
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://accounts.google.com/gsi/client'
      script.async = true
      script.defer = true
      script.onload = () => resolve()
      script.onerror = () => reject(new Error('Failed to load Google GSI client'))
      
      document.head.appendChild(script)
    })
  }

  const initializeOneTap = () => {
    // @ts-ignore
    if (window.google && window.google.accounts) {
      try {
        // @ts-ignore
        window.google.accounts.id.initialize({
          client_id: options.client_id,
          callback: callback,
          auto_select: options.auto_select,
          cancel_on_tap_outside: options.cancel_on_tap_outside,
          context: options.context,
          itp_support: options.itp_support,
          use_fedcm_for_prompt: options.use_fedcm_for_prompt,
        })

        // Show the One Tap prompt
        setTimeout(() => {
          try {
            // @ts-ignore
            window.google.accounts.id.prompt((notification: any) => {
              if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                console.log('Google One Tap was not displayed:', notification.getNotDisplayedReason())
              }
            })
          } catch (promptError) {
            console.error('Error showing Google One Tap prompt:', promptError)
          }
        }, 1000)
      } catch (error) {
        console.error('Error initializing Google One Tap:', error)
      }
    } else {
      console.error('Google GSI client not available')
    }
  }

  loadGoogleScript()
    .then(initializeOneTap)
    .catch(console.error)
}

// Check if environment supports Google One Tap
export const isGoogleOneTapSupported = (): boolean => {
  if (typeof window === 'undefined') return false

  // Check if HTTPS (required for production)
  const isSecure = window.location.protocol === 'https:' ||
                   window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1'

  if (!isSecure) {
    console.warn('Google One Tap requires HTTPS in production')
    return false
  }

  // Check if current page should show One Tap
  const currentPath = window.location.pathname
  const restrictedPaths = ['/dashboard', '/profile', '/settings', '/admin']

  if (restrictedPaths.some(path => currentPath.startsWith(path))) {
    console.log('Google One Tap disabled on restricted path:', currentPath)
    return false
  }

  // Check if One Tap was disabled for this session
  if (sessionStorage.getItem('google_one_tap_disabled')) {
    console.log('Google One Tap disabled for this session')
    return false
  }

  return true
}

// Get Google One Tap configuration
export const getGoogleOneTapConfig = () => {
  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID

  if (!clientId) {
    console.error('NEXT_PUBLIC_GOOGLE_CLIENT_ID is not configured')
    return null
  }

  return {
    ...googleOneTapOptions,
    client_id: clientId,
  }
}

// Cancel Google One Tap prompt
export const cancelGoogleOneTap = () => {
  if (typeof window !== 'undefined' && window.google?.accounts?.id) {
    try {
      window.google.accounts.id.cancel()
      console.log('Google One Tap cancelled')
      return true
    } catch (error) {
      console.log('No active Google One Tap to cancel')
      return false
    }
  }
  return false
}

// Check if Google One Tap is currently active
export const isGoogleOneTapActive = (): boolean => {
  if (typeof window === 'undefined') return false

  // Check if Google One Tap prompt is visible
  const oneTapElement = document.querySelector('[data-testid="one-tap-google-sign-in-prompt"]') ||
                       document.querySelector('.g_id_signin') ||
                       document.querySelector('[id^="credential_picker"]')

  return !!oneTapElement
}

// Global One Tap state management
let isOneTapInitialized = false
let oneTapInitPromise: Promise<void> | null = null

// Reset One Tap state (useful for testing or when user logs out)
export const resetGoogleOneTapState = () => {
  isOneTapInitialized = false
  oneTapInitPromise = null
  cancelGoogleOneTap()

  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('google_one_tap_disabled')
  }
}

// Enhanced initialize function with global state management
export const initializeGoogleOneTapOnce = async (
  options: typeof googleOneTapOptions,
  callback: (response: any) => void
): Promise<void> => {
  // Check if disabled for this session
  if (typeof window !== 'undefined' && sessionStorage.getItem('google_one_tap_disabled')) {
    console.log('Google One Tap disabled for this session')
    return
  }

  // If already initialized, don't initialize again
  if (isOneTapInitialized) {
    console.log('Google One Tap already initialized globally')
    return
  }

  // If initialization is in progress, wait for it
  if (oneTapInitPromise) {
    console.log('Google One Tap initialization in progress, waiting...')
    return oneTapInitPromise
  }

  // Start initialization
  oneTapInitPromise = new Promise<void>((resolve, reject) => {
    try {
      initializeGoogleOneTap(options, (response) => {
        callback(response)
        // Disable after successful login
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('google_one_tap_disabled', 'true')
        }
      })
      isOneTapInitialized = true
      resolve()
    } catch (error) {
      reject(error)
    }
  })

  return oneTapInitPromise
}
