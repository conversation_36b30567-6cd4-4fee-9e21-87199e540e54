'use client'

import React, { useState, useEffect, create<PERSON>ontext, useContext, ReactNode } from 'react'
import { AuthUser } from '@/lib/auth'
import { userEvents, UserEventHelpers } from '@/utils/userEvents'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string) => Promise<{ success: boolean; error?: string }>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
  updateUserPoints: (newPoints: number) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchUser = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Error fetching user:', error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setUser(data.user)
        return { success: true }
      } else {
        return { success: false, error: data.error || 'Sign in failed' }
      }
    } catch (error) {
      console.error('Sign in error:', error)
      return { success: false, error: 'An error occurred during sign in' }
    }
  }

  const signOut = async () => {
    try {
      await fetch('/api/auth/signout', { method: 'POST' })
      setUser(null)

      // Reset Google One Tap state when user logs out
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem('google_one_tap_disabled')
        // Import and call reset function dynamically to avoid circular imports
        const { resetGoogleOneTapState } = await import('@/utils/google-one-tap')
        resetGoogleOneTapState()
      }
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const refreshUser = async () => {
    await fetchUser()
  }

  const updateUserPoints = (newPoints: number) => {
    if (user) {
      setUser({
        ...user,
        points: newPoints
      })
      // Emit event for other components to listen
      UserEventHelpers.emitPointsUpdated(newPoints)
    }
  }

  useEffect(() => {
    fetchUser()
  }, [])

  const contextValue = {
    user,
    loading,
    signIn,
    signOut,
    refreshUser,
    updateUserPoints
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for checking permissions
export function usePermissions() {
  const { user } = useAuth()

  const canCompress = (fileSizeMb: number) => {
    if (!user) return { allowed: false, reason: 'Please sign in to compress files' }
    
    const maxFileSize = user.membership_level === 'pro' ? 200 : 10
    if (fileSizeMb > maxFileSize) {
      return {
        allowed: false,
        reason: `File size exceeds ${maxFileSize}MB limit for ${user.membership_level} plan`
      }
    }

    if (user.membership_level === 'free' && user.points <= 0) {
      return {
        allowed: false,
        reason: 'No compression credits remaining. Upgrade to Pro for unlimited compressions.'
      }
    }

    // For pro users, check if membership is active (including trial)
    if (user.membership_level === 'pro' && !user.is_membership_active) {
      return {
        allowed: false,
        reason: 'Pro membership has expired. Please renew your subscription.'
      }
    }

    return { allowed: true }
  }

  const canUseAdvancedFormats = (fileType: string) => {
    if (!user) return { allowed: false, reason: 'Please sign in to use advanced formats' }
    
    const advancedFormats = ['gif', 'video', 'mp4', 'avi', 'mov', 'webm']
    const isAdvancedFormat = advancedFormats.some(format => 
      fileType.toLowerCase().includes(format)
    )

    if (isAdvancedFormat && user.membership_level !== 'pro') {
      return {
        allowed: false,
        reason: 'Advanced format support requires Pro membership'
      }
    }

    if (isAdvancedFormat && !user.is_membership_active) {
      return {
        allowed: false,
        reason: 'Pro membership has expired'
      }
    }

    return { allowed: true }
  }

  const canUseBatchProcessing = () => {
    if (!user) return { allowed: false, reason: 'Please sign in to use batch processing' }
    
    if (user.membership_level !== 'pro') {
      return {
        allowed: false,
        reason: 'Batch processing requires Pro membership'
      }
    }

    if (!user.is_membership_active) {
      return {
        allowed: false,
        reason: 'Pro membership has expired'
      }
    }

    return { allowed: true }
  }

  return {
    canCompress,
    canUseAdvancedFormats,
    canUseBatchProcessing
  }
}
