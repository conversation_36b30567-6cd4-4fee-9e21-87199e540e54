'use client'

import { useState, useRef, useEffect } from 'react'
import { Save, RotateCw, RotateCcw } from 'lucide-react'
import toast from 'react-hot-toast'

interface SimpleImageEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

export function SimpleImageEditor({ file, onEditComplete }: SimpleImageEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [rotation, setRotation] = useState(0)
  const [scale, setScale] = useState(1)

  useEffect(() => {
    console.log('SimpleImageEditor: Loading file', file.name)
    
    if (!canvasRef.current) {
      console.log('Canvas ref not available')
      return
    }

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.log('Canvas context not available')
      return
    }

    // Create image element
    const img = new Image()
    imageRef.current = img

    img.onload = () => {
      console.log('Image loaded successfully', img.width, 'x', img.height)
      
      // Set canvas size
      const maxWidth = 800
      const maxHeight = 600
      const aspectRatio = img.width / img.height
      
      let canvasWidth = maxWidth
      let canvasHeight = maxWidth / aspectRatio
      
      if (canvasHeight > maxHeight) {
        canvasHeight = maxHeight
        canvasWidth = maxHeight * aspectRatio
      }
      
      canvas.width = canvasWidth
      canvas.height = canvasHeight
      
      // Draw image
      drawImage()
      setIsLoading(false)
      console.log('Image editor ready')
    }

    img.onerror = () => {
      console.error('Failed to load image')
      setIsLoading(false)
      toast.error('Failed to load image')
    }

    // Load image from file
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      console.log('File read successfully, data URL length:', result.length)
      img.src = result
    }
    
    reader.onerror = () => {
      console.error('Failed to read file')
      setIsLoading(false)
      toast.error('Failed to read file')
    }
    
    reader.readAsDataURL(file)

  }, [file])

  const drawImage = () => {
    if (!canvasRef.current || !imageRef.current) return
    
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    const img = imageRef.current
    
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    // Save context
    ctx.save()
    
    // Move to center
    ctx.translate(canvas.width / 2, canvas.height / 2)
    
    // Apply rotation
    ctx.rotate((rotation * Math.PI) / 180)
    
    // Apply scale
    ctx.scale(scale, scale)
    
    // Draw image centered
    ctx.drawImage(
      img,
      -img.width / 2,
      -img.height / 2,
      img.width,
      img.height
    )
    
    // Restore context
    ctx.restore()
  }

  useEffect(() => {
    if (!isLoading) {
      drawImage()
    }
  }, [rotation, scale, isLoading])

  const handleRotate = (degrees: number) => {
    setRotation(prev => prev + degrees)
  }

  const handleExport = async () => {
    if (!canvasRef.current) return
    
    try {
      const canvas = canvasRef.current
      
      // Convert canvas to blob
      canvas.toBlob((blob) => {
        if (blob) {
          console.log('Image exported successfully')
          onEditComplete(blob)
          toast.success('Image exported successfully!')
        } else {
          toast.error('Failed to export image')
        }
      }, 'image/png', 0.9)
      
    } catch (error) {
      console.error('Export failed:', error)
      toast.error('Failed to export image')
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <span className="ml-4 text-gray-600 dark:text-gray-400">Loading image...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* Toolbar */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleRotate(-90)}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleRotate(90)}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <RotateCw className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleExport}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Canvas */}
      <div className="p-4">
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-700 flex items-center justify-center">
          <canvas 
            ref={canvasRef}
            className="max-w-full max-h-96"
          />
        </div>
      </div>

      {/* Controls */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Rotation: {rotation}°
            </label>
            <input
              type="range"
              min={-180}
              max={180}
              value={rotation}
              onChange={(e) => setRotation(Number(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Scale: {(scale * 100).toFixed(0)}%
            </label>
            <input
              type="range"
              min={0.1}
              max={2}
              step={0.1}
              value={scale}
              onChange={(e) => setScale(Number(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>
        </div>
      </div>
    </div>
  )
}
