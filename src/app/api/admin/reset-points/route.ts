import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

// This endpoint would typically require admin authentication
// For production, implement proper admin auth check

export async function POST(request: NextRequest) {
  try {
    // In production, add admin authentication here
    // const user = await auth.getCurrentUser()
    // if (!user || user.role !== 'admin') {
    //   return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    // }

    const body = await request.json()
    const { resetType = 'monthly' } = body

    let pointsToReset = 50 // Default monthly points for free users

    if (resetType === 'weekly') {
      pointsToReset = 10
    } else if (resetType === 'daily') {
      pointsToReset = 2
    }

    // Reset points for all free users
    const { data: updatedUsers, error } = await supabaseAdmin
      .from('users')
      .update({
        points: pointsToReset,
        updated_at: new Date().toISOString()
      })
      .eq('membership_level', 'free')
      .select('id, email, points')

    if (error) {
      console.error('Error resetting points:', error)
      return NextResponse.json(
        { error: 'Failed to reset user points' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `Successfully reset points for ${updatedUsers?.length || 0} free users`,
      resetType,
      pointsReset: pointsToReset,
      affectedUsers: updatedUsers?.length || 0
    })
  } catch (error) {
    console.error('Reset points error:', error)
    return NextResponse.json(
      { error: 'Failed to reset points' },
      { status: 500 }
    )
  }
}

// GET endpoint to check when points were last reset
export async function GET() {
  try {
    // Get the most recent update time for free users
    const { data: recentUpdate } = await supabaseAdmin
      .from('users')
      .select('updated_at')
      .eq('membership_level', 'free')
      .order('updated_at', { ascending: false })
      .limit(1)
      .single()

    return NextResponse.json({
      success: true,
      lastResetTime: recentUpdate?.updated_at || null
    })
  } catch (error) {
    console.error('Get reset info error:', error)
    return NextResponse.json(
      { error: 'Failed to get reset information' },
      { status: 500 }
    )
  }
}