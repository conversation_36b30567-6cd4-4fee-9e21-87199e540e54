'use client'

import { useState, useCallback, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileUpload } from '@/components/FileUpload'
import { AdvancedImageEditor } from '@/components/AdvancedImageEditor'
import { AuthModal } from '@/components/AuthModal'
import { useAuth, usePermissions } from '@/hooks/useAuth'
import { getUploadedFile, clearUploadedFile, getFileCategory, base64ToFile } from '@/utils/fileHandler'
import toast from 'react-hot-toast'
import { 
  Edit3, 
  Sparkles, 
  Zap, 
  Download,
  Settings,
  FileImage,
  Palette,
  RotateCw,
  Crop,
  Sliders
} from 'lucide-react'

interface TextElement {
  id: string
  text: string
  x: number // relative to image (0-1)
  y: number // relative to image (0-1)
  fontSize: number
  color: string
}

interface EditState {
  zoom: number
  rotation: number
  imagePosition: { x: number; y: number }
  imageSize: { width: number; height: number }
  originalImageSize: { width: number; height: number }
  filters: {
    brightness: number
    contrast: number
    saturation: number
    blur: number
  }
  textElements: TextElement[]
  activeTool: 'select' | 'crop' | 'rotate' | 'text' | 'shape' | 'filter'
  showFilterPanel: boolean
  showTextPanel: boolean
  cropArea: {
    x: number
    y: number
    width: number
    height: number
  } | null
}

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  file: File
  originalSize: number
  status: 'pending' | 'editing' | 'completed' | 'error'
  editedFile?: Blob
  downloadUrl?: string
  editState?: EditState
}

export default function ImageEditorPage() {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [currentFile, setCurrentFile] = useState<UploadedFile | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [showAuthModal, setShowAuthModal] = useState(false)
  const { user, refreshUser } = useAuth()
  const permissions = usePermissions()

  // Check for uploaded file from home page
  useEffect(() => {
    const uploadedFile = getUploadedFile()
    if (uploadedFile && getFileCategory(uploadedFile.type) === 'image') {
      const file = base64ToFile(uploadedFile.data, uploadedFile.name, uploadedFile.type, Date.now())
      handleFileUpload([file])
      clearUploadedFile()
    }
  }, [])

  const handleFileUpload = useCallback((uploadedFiles: File[]) => {
    console.log('Files uploaded:', uploadedFiles)
    const imageFiles = uploadedFiles.filter(file => file.type.startsWith('image/'))
    console.log('Image files filtered:', imageFiles)

    if (imageFiles.length === 0) {
      toast.error('Please upload image files only')
      return
    }

    const newFiles: UploadedFile[] = imageFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      file,
      originalSize: file.size,
      status: 'pending'
    }))

    console.log('New files created:', newFiles)
    setFiles(prev => [...prev, ...newFiles])

    // Auto-select first file for editing
    if (newFiles.length > 0 && !currentFile) {
      console.log('Setting current file:', newFiles[0])
      setCurrentFile(newFiles[0])
    }
  }, [currentFile])

  const handleFileSelect = (file: UploadedFile) => {
    setCurrentFile(file)
  }

  const handleStateChange = useCallback((state: EditState) => {
    if (!currentFile) return

    // Update the file's edit state
    setFiles(prev => prev.map(file =>
      file.id === currentFile.id
        ? { ...file, editState: state }
        : file
    ))

    // Update current file
    setCurrentFile(prev => prev ? { ...prev, editState: state } : null)
  }, [currentFile])

  const handleEditComplete = (editedBlob: Blob) => {
    if (!currentFile) return

    const downloadUrl = URL.createObjectURL(editedBlob)
    
    setFiles(prev => prev.map(file => 
      file.id === currentFile.id 
        ? { 
            ...file, 
            status: 'completed',
            editedFile: editedBlob,
            downloadUrl 
          }
        : file
    ))

    setCurrentFile(prev => prev ? {
      ...prev,
      status: 'completed',
      editedFile: editedBlob,
      downloadUrl
    } : null)

    toast.success('Image edited successfully!')
  }

  const handleDownload = (file: UploadedFile) => {
    if (!file.downloadUrl) return

    const link = document.createElement('a')
    link.href = file.downloadUrl
    link.download = `edited_${file.name}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const removeFile = (fileId: string) => {
    setFiles(prev => {
      const newFiles = prev.filter(f => f.id !== fileId)
      if (currentFile?.id === fileId) {
        setCurrentFile(newFiles.length > 0 ? newFiles[0] : null)
      }
      return newFiles
    })
  }

  const clearAllFiles = () => {
    files.forEach(file => {
      if (file.downloadUrl) {
        URL.revokeObjectURL(file.downloadUrl)
      }
    })
    setFiles([])
    setCurrentFile(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mr-4">
              <Edit3 className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Image Editor
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Professional image editing in your browser
              </p>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Crop className="w-6 h-6 text-purple-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Crop & Resize</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <RotateCw className="w-6 h-6 text-blue-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Rotate & Flip</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Sliders className="w-6 h-6 text-green-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Filters</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <FileImage className="w-6 h-6 text-orange-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Convert</span>
            </div>
          </div>
        </motion.div>

        {files.length === 0 ? (
          /* Upload Section */
          <motion.div
            initial={{ opacity: 1, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <FileUpload
              onFileUpload={handleFileUpload}
              acceptedTypes={{
                'image/png': ['.png'],
                'image/jpeg': ['.jpg', '.jpeg'],
                'image/webp': ['.webp'],
                'image/avif': ['.avif'],
                'image/gif': ['.gif'],
                'image/bmp': ['.bmp'],
                'image/tiff': ['.tiff']
              }}
              maxFileSize={50 * 1024 * 1024} // 50MB for images
              title="Drop your images here"
              subtitle="Support for PNG, JPEG, WebP, AVIF, GIF, BMP, and TIFF formats"
            />
          </motion.div>
        ) : (
          /* Editor Section */
          <div className="h-[calc(100vh-200px)] flex flex-col">
            {/* File Tabs */}
            <div className="bg-white dark:bg-gray-800 rounded-t-xl shadow-lg border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between p-4">
                <div className="flex items-center space-x-2 overflow-x-auto">
                  {files.map((file) => (
                    <button
                      key={file.id}
                      onClick={() => handleFileSelect(file)}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                        currentFile?.id === file.id
                          ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      <span className="text-sm font-medium truncate max-w-32">
                        {file.name}
                      </span>
                      {file.status === 'completed' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDownload(file)
                          }}
                          className="p-1 text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                        >
                          <Download className="w-3 h-3" />
                        </button>
                      )}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          removeFile(file.id)
                        }}
                        className="p-1 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                      >
                        ×
                      </button>
                    </button>
                  ))}
                </div>

                <button
                  onClick={clearAllFiles}
                  className="text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 ml-4"
                >
                  Clear All
                </button>
              </div>
            </div>

            {/* Editor */}
            <div className="flex-1">
              {currentFile && (
                <AdvancedImageEditor
                  file={currentFile.file}
                  initialEditState={currentFile.editState}
                  onEditComplete={handleEditComplete}
                  onStateChange={handleStateChange}
                />
              )}
            </div>
          </div>
        )}

        {/* Auth Modal */}
        <AuthModal 
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </div>
    </div>
  )
}
