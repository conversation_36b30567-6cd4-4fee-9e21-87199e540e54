/**
 * Memory Management and Performance Optimization Utilities
 * Handles memory cleanup, resource management, and performance monitoring
 */

interface ResourceTracker {
  id: string
  type: 'blob' | 'url' | 'canvas' | 'image' | 'video' | 'audio'
  resource: any
  timestamp: number
  size?: number
}

class MemoryManager {
  private resources: Map<string, ResourceTracker> = new Map()
  private maxAge = 5 * 60 * 1000 // 5 minutes
  private maxResources = 100
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor() {
    this.startCleanupInterval()
    this.setupUnloadHandler()
  }

  /**
   * Register a resource for tracking and automatic cleanup
   */
  register(id: string, resource: any, type: ResourceTracker['type'], size?: number): string {
    const tracker: ResourceTracker = {
      id,
      type,
      resource,
      timestamp: Date.now(),
      size
    }

    this.resources.set(id, tracker)
    
    // Trigger cleanup if we have too many resources
    if (this.resources.size > this.maxResources) {
      this.cleanup()
    }

    return id
  }

  /**
   * Manually release a specific resource
   */
  release(id: string): boolean {
    const tracker = this.resources.get(id)
    if (!tracker) return false

    this.releaseResource(tracker)
    this.resources.delete(id)
    return true
  }

  /**
   * Release all resources of a specific type
   */
  releaseByType(type: ResourceTracker['type']): number {
    let count = 0
    for (const [id, tracker] of this.resources.entries()) {
      if (tracker.type === type) {
        this.releaseResource(tracker)
        this.resources.delete(id)
        count++
      }
    }
    return count
  }

  /**
   * Get memory usage statistics
   */
  getStats() {
    const stats = {
      totalResources: this.resources.size,
      byType: {} as Record<string, number>,
      totalSize: 0,
      oldestResource: 0
    }

    let oldestTimestamp = Date.now()

    for (const tracker of this.resources.values()) {
      stats.byType[tracker.type] = (stats.byType[tracker.type] || 0) + 1
      if (tracker.size) {
        stats.totalSize += tracker.size
      }
      if (tracker.timestamp < oldestTimestamp) {
        oldestTimestamp = tracker.timestamp
      }
    }

    stats.oldestResource = Date.now() - oldestTimestamp

    return stats
  }

  /**
   * Force cleanup of old resources
   */
  cleanup(): number {
    const now = Date.now()
    let cleaned = 0

    for (const [id, tracker] of this.resources.entries()) {
      if (now - tracker.timestamp > this.maxAge) {
        this.releaseResource(tracker)
        this.resources.delete(id)
        cleaned++
      }
    }

    return cleaned
  }

  /**
   * Release all resources
   */
  releaseAll(): void {
    for (const tracker of this.resources.values()) {
      this.releaseResource(tracker)
    }
    this.resources.clear()
  }

  private releaseResource(tracker: ResourceTracker): void {
    try {
      switch (tracker.type) {
        case 'blob':
          // Blobs are garbage collected automatically
          break
        
        case 'url':
          if (typeof tracker.resource === 'string') {
            URL.revokeObjectURL(tracker.resource)
          }
          break
        
        case 'canvas':
          if (tracker.resource instanceof HTMLCanvasElement) {
            const ctx = tracker.resource.getContext('2d')
            if (ctx) {
              ctx.clearRect(0, 0, tracker.resource.width, tracker.resource.height)
            }
            tracker.resource.width = 0
            tracker.resource.height = 0
          }
          break
        
        case 'image':
          if (tracker.resource instanceof HTMLImageElement) {
            tracker.resource.src = ''
            tracker.resource.onload = null
            tracker.resource.onerror = null
          }
          break
        
        case 'video':
          if (tracker.resource instanceof HTMLVideoElement) {
            tracker.resource.pause()
            tracker.resource.src = ''
            tracker.resource.load()
          }
          break
        
        case 'audio':
          if (tracker.resource instanceof HTMLAudioElement) {
            tracker.resource.pause()
            tracker.resource.src = ''
            tracker.resource.load()
          }
          break
      }
    } catch (error) {
      console.warn('Error releasing resource:', error)
    }
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60000) // Cleanup every minute
  }

  private setupUnloadHandler(): void {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.releaseAll()
      })
    }
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.releaseAll()
  }
}

// Singleton instance
export const memoryManager = new MemoryManager()

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static measurements: Map<string, number> = new Map()

  static start(label: string): void {
    this.measurements.set(label, performance.now())
  }

  static end(label: string): number {
    const startTime = this.measurements.get(label)
    if (!startTime) {
      console.warn(`No start time found for measurement: ${label}`)
      return 0
    }

    const duration = performance.now() - startTime
    this.measurements.delete(label)
    
    console.log(`Performance [${label}]: ${duration.toFixed(2)}ms`)
    return duration
  }

  static measure<T>(label: string, fn: () => T): T {
    this.start(label)
    try {
      const result = fn()
      this.end(label)
      return result
    } catch (error) {
      this.end(label)
      throw error
    }
  }

  static async measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.start(label)
    try {
      const result = await fn()
      this.end(label)
      return result
    } catch (error) {
      this.end(label)
      throw error
    }
  }
}

/**
 * File size utilities
 */
export const FileUtils = {
  formatSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  getCompressionRatio(originalSize: number, compressedSize: number): number {
    if (originalSize === 0) return 0
    return ((originalSize - compressedSize) / originalSize) * 100
  },

  isLargeFile(size: number, threshold: number = 50 * 1024 * 1024): boolean {
    return size > threshold // Default 50MB
  }
}

/**
 * Debounce utility for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Throttle utility for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * Chunk processing for large files
 */
export async function processInChunks<T, R>(
  items: T[],
  processor: (chunk: T[]) => Promise<R[]>,
  chunkSize: number = 10
): Promise<R[]> {
  const results: R[] = []
  
  for (let i = 0; i < items.length; i += chunkSize) {
    const chunk = items.slice(i, i + chunkSize)
    const chunkResults = await processor(chunk)
    results.push(...chunkResults)
    
    // Allow other tasks to run
    await new Promise(resolve => setTimeout(resolve, 0))
  }
  
  return results
}

/**
 * Memory-safe blob creation
 */
export function createManagedBlob(data: BlobPart[], options?: BlobPropertyBag): { blob: Blob; id: string } {
  const blob = new Blob(data, options)
  const id = `blob_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  memoryManager.register(id, blob, 'blob', blob.size)
  
  return { blob, id }
}

/**
 * Memory-safe URL creation
 */
export function createManagedURL(blob: Blob): { url: string; id: string } {
  const url = URL.createObjectURL(blob)
  const id = `url_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  memoryManager.register(id, url, 'url', blob.size)
  
  return { url, id }
}
