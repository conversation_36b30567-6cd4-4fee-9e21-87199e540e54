'use client'

import { useState, useCallback, useRef } from 'react'
import { Upload, Play, Zap } from 'lucide-react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'

const DEMO_IMAGES = [
  '/demo-image-1.jpg',
  '/demo-image-2.jpg', 
  '/demo-image-3.jpg',
  '/demo-image-4.jpg'
]

export function HeroSection() {
  const [isDragActive, setIsDragActive] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [loadingPresetIndex, setLoadingPresetIndex] = useState<number | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Highlight the first word with a brush background
  const renderFirstWordHighlighted = (text: string) => {
    if (!text) return text
    const parts = text.split(' ')
    const first = parts.shift() ?? ''
    const rest = parts.join(' ')
    return (
      <>
        <span className="brush-highlight">{first}</span>
        {rest ? ` ${rest}` : ''}
      </>
    )
  }

  const handleGlobalDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.dataTransfer.types.includes('Files')) {
      setIsDragActive(true)
    }
  }, [])

  const handleGlobalDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleGlobalDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const relatedTarget = e.relatedTarget as Element
    const currentTarget = e.currentTarget as Element
    if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
      setIsDragActive(false)
    }
  }, [])

  // Helper function to determine file type and route
  const getFileTypeAndRoute = (file: File) => {
    const type = file.type.toLowerCase()

    if (type.includes('gif')) {
      return { route: '/gif-compress', type: 'gif' }
    } else if (type.startsWith('video/')) {
      return { route: '/video-compress', type: 'video' }
    } else if (type.startsWith('image/')) {
      return { route: '/image-compress', type: 'image' }
    }

    return null
  }

  const handleFiles = useCallback(async (files: File[]) => {
    if (files.length === 0) return

    // Only allow single file upload
    const file = files[0]

    // Check file type
    const supportedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/avif', 'image/gif',
      'video/mp4', 'video/webm', 'video/avi', 'video/mov'
    ]

    if (!supportedTypes.includes(file.type.toLowerCase())) {
      toast.error('Unsupported file type. Please upload JPG, PNG, WebP, AVIF, GIF, MP4, WebM, AVI, or MOV files.')
      return
    }

    // Check file size (100MB limit)
    if (file.size > 100 * 1024 * 1024) {
      toast.error('File size too large. Please upload files smaller than 100MB.')
      return
    }

    setIsUploading(true)

    try {
      // Get the appropriate route based on file type
      const fileInfo = getFileTypeAndRoute(file)

      if (!fileInfo) {
        toast.error('Unsupported file type.')
        return
      }

      // Convert file to base64 for storage
      const reader = new FileReader()
      reader.onload = () => {
        const base64Data = reader.result as string

        // Store file data in sessionStorage for the compress page
        const fileData = {
          name: file.name,
          size: file.size,
          type: file.type,
          data: base64Data,
          lastModified: file.lastModified
        }

        try {
          sessionStorage.setItem('uploadedFile', JSON.stringify(fileData))
          router.push(fileInfo.route)
        }catch(err) {
          sessionStorage.removeItem('uploadedFile')
          router.push(fileInfo.route)
          toast.error('File size too large.')
        }
        // Navigate to the appropriate compress page
        // router.push(fileInfo.route)
      }

      reader.readAsDataURL(file)

    } catch (error) {
      console.error('Failed to process file:', error)
      toast.error('Failed to process file. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }, [router])

  const handleGlobalDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)

    if (e.dataTransfer.files.length > 0) {
      const files = Array.from(e.dataTransfer.files)
      handleFiles(files)
    }
  }, [handleFiles])

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files)
      handleFiles(files)
    }
  }

  const handlePresetClick = useCallback(async (imageUrl: string, index: number) => {
    if (typeof loadingPresetIndex === 'number') return
    
    try {
      setLoadingPresetIndex(index)
      // TODO: Implement preset image processing
      console.log('Processing preset image:', imageUrl)
      await new Promise(resolve => setTimeout(resolve, 1500))
    } catch (error) {
      console.error('Failed to load preset image:', error)
    } finally {
      setTimeout(() => {
        setLoadingPresetIndex(null)
      }, 500)
    }
  }, [loadingPresetIndex])

  return (
    <>
      <div
        className="flex min-h-[calc(100vh-65px)] items-center justify-center overflow-x-clip px-4 pt-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900"
        onDragEnter={handleGlobalDragEnter}
        onDragOver={handleGlobalDragOver}
        onDragLeave={handleGlobalDragLeave}
        onDrop={handleGlobalDrop}
      >
        <div className="w-full">
          <div className="mx-auto w-full px-2 sm:px-6 max-w-7xl relative">
            {/* Decorative elements */}
            <motion.svg
              width="184"
              height="192"
              viewBox="0 0 184 192"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-blue-400/60 absolute hidden md:block -top-16 -right-[20%] xl:-right-0"
              initial={{ opacity: 1, rotate: -10 }}
              animate={{ opacity: 1, rotate: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <path
                d="M182.648 183.128C178.597 187.405 171.028 191.799 163.237 191.977C157.571 192.103 152.323 190.012 148.058 185.927C139.232 177.468 138.372 158.735 137.621 142.22C137.204 133.157 136.747 122.877 134.696 119.768C131.836 115.376 124.509 108.471 107.735 111.458C94.4152 113.834 81.7884 115.329 73.6959 107.665C64.5031 98.9588 66.3544 85.5644 68.5325 76.244C69.271 73.0119 70.4408 69.8949 72.0105 66.9765C67.2371 63.1964 63.8062 58.7353 62.4015 54.3978C60.8072 49.4882 61.1485 43.5448 61.4696 37.8066C61.9457 29.5112 62.3974 21.6751 57.4255 18.3185C52.9599 15.3123 37.4838 14.4287 30.2947 16.7929C23.7769 18.9234 13.5899 18.9589 1.99423 6.93367C1.6401 6.5666 1.36158 6.13357 1.17454 5.65932C0.987495 5.18506 0.895589 4.67887 0.904109 4.16963C0.912629 3.66038 1.02138 3.15807 1.22417 2.69136C1.42696 2.22466 1.71981 1.80269 2.086 1.44957C2.45218 1.09646 2.88452 0.819116 3.35835 0.63335C3.83218 0.447587 4.33822 0.357049 4.84756 0.366916C5.3569 0.376784 5.85958 0.486848 6.32689 0.690842C6.7942 0.894836 7.21699 1.18879 7.57112 1.55585C12.4264 6.59173 19.8904 12.0448 27.8628 9.42376C35.8352 6.80273 54.2649 6.8425 61.7549 11.8939C70.3895 17.7206 69.7629 28.6339 69.2095 38.2642C68.9095 43.5287 68.6214 48.5014 69.7664 52.0262C70.775 55.1189 73.3834 58.1558 76.531 60.6768C76.9819 60.2006 77.4049 59.754 77.8356 59.3765C82.0627 55.4357 86.9774 53.4477 91.2962 53.9361C96.6192 54.5284 100.113 58.7801 100.195 64.7704C100.25 70.0573 97.3594 73.7039 92.4487 74.5175C88.6575 75.1291 83.6402 73.9231 78.5462 71.2419C77.4414 73.3904 76.607 75.6679 76.0619 78.0227C73.2511 90.0426 74.1576 97.4483 79.0031 102.037C84.4653 107.21 95.0526 105.831 106.352 103.814C122.037 101.019 134.401 105.177 141.174 115.524C144.395 120.438 144.815 129.89 145.362 141.875C146.018 156.197 146.832 174.017 153.401 180.345C156.233 183.027 159.368 184.313 163.024 184.23C168.933 184.098 174.615 180.307 176.996 177.793C177.702 177.048 178.675 176.614 179.703 176.588C180.73 176.561 181.727 176.944 182.474 177.651C183.221 178.359 183.657 179.333 183.687 180.361C183.716 181.388 183.336 182.384 182.63 183.129L182.648 183.128Z"
                fill="currentColor"
              />
            </motion.svg>

            <div className="flex flex-col lg:flex-row items-center lg:items-start justify-center md:gap-8 lg:gap-12">
              {/* Left: Content */}
              <motion.div 
                className="flex flex-col md:flex-row lg:flex-col items-center lg:items-start gap-6 md:gap-8 md:max-w-3xl lg:max-w-3xl"
                initial={{ opacity: 1, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
              >
                <div className="flex flex-col gap-4 text-center md:text-left">
                  {/* Demo video/image placeholder */}
                  <motion.div 
                    className="w-full h-auto overflow-hidden rounded-[30px] max-w-[320px] lg:max-w-[420px] mb-2 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-gray-700 dark:to-gray-600 aspect-video flex items-center justify-center"
                    initial={{ opacity: 1, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                  >
                    <div className="text-center">
                      <Play className="w-16 h-16 text-blue-500 dark:text-blue-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 dark:text-gray-300">Demo Video</p>
                    </div>
                  </motion.div>

                  <motion.h1 
                    className="font-bold text-gray-900 dark:text-gray-100 text-[36px] md:text-[48px] lg:text-[48px] leading-[1.05] tracking-tight m-0"
                    initial={{ opacity: 1, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                  >
                    {renderFirstWordHighlighted('Professional')} Image & Video Compression
                  </motion.h1>

                  <motion.h2 
                    className="font-bold text-[28px] md:text-[36px] lg:text-[46px] leading-tight m-0"
                    initial={{ opacity: 1, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                  >
                    <span className="text-[#0f70e6] dark:text-blue-400">Reduce Size</span>
                    <span className="mx-2 text-gray-900/90 dark:text-gray-100/90">and</span>
                    <span className="text-[#ff7b7b] dark:text-red-400">Keep Quality</span>
                  </motion.h2>

                  <motion.p 
                    className="text-[18px] md:text-[20px] text-gray-700/90 dark:text-gray-300/90 leading-relaxed m-0"
                    initial={{ opacity: 1, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.5 }}
                  >
                    Advanced compression algorithms that reduce file sizes by up to 90% while maintaining visual quality. Perfect for web optimization, storage savings, and faster loading times.
                  </motion.p>
                </div>
              </motion.div>

              {/* Right: Upload area */}
              <motion.div 
                className="relative group flex flex-col gap-4 md:gap-8 max-w-md mt-8 lg:mt-16"
                initial={{ opacity: 1, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                {/* Decorative arrow */}
                <motion.svg
                  width="34"
                  height="27"
                  viewBox="0 0 34 27"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-blue-400 absolute hidden md:block -right-24 top-32 transition-transform duration-300 ease-in-out rotate-6 group-hover:rotate-[35deg]  group-hover:-translate-x-4 transform-gpu"
                  initial={{ opacity: 1, rotate: -20 }}
                  animate={{ opacity: 1, rotate: 6 }}
                  transition={{ duration: 1, delay: 1 }}
                >
                  <path
                    d="M33.8167 1.68928C33.68 1.41639 33.4726 1.1859 33.2164 1.0223C32.9602 0.858703 32.6649 0.768091 32.3618 0.760085L1.71198 0H1.67137C1.37184 0.00107942 1.07808 0.0829958 0.820576 0.237246C0.563072 0.391496 0.351224 0.612448 0.207062 0.877146C0.0629004 1.14184 -0.00833251 1.44064 0.000775816 1.74247C0.00988414 2.04431 0.098999 2.33817 0.258855 2.59355L15.055 26.2114C15.2027 26.4471 15.4059 26.6423 15.6464 26.7799C15.887 26.9175 16.1575 26.9931 16.4339 27H16.4745C16.7443 26.9999 17.01 26.9342 17.2492 26.8083C17.4884 26.6825 17.6938 26.5003 17.8482 26.2773L33.7019 3.42128C33.8743 3.16991 33.9761 2.87618 33.9963 2.57124C34.0165 2.2663 33.9544 1.96153 33.8167 1.68928Z"
                    fill="currentColor"
                  />
                </motion.svg>

                {/* Upload card */}
                <div className="w-full flex flex-col sm:justify-center sm:items-center sm:gap-8 sm:pt-36 sm:pb-16 rounded-[28px] bg-white dark:bg-gray-800 shadow-2xl border border-gray-100 hover:shadow-[0_20px_60px_rgba(0,0,0,0.12)] dark:hover:shadow-[0_20px_60px_rgba(255,255,255,.2)] transition-shadow duration-300">
                  <button
                    type="button"
                    onClick={handleUploadClick}
                    disabled={isUploading}
                    className={`rounded-full transition-transform ease-in-out text-center inline-flex items-center justify-center focus-visible:ring focus-visible:ring-offset-2 focus-visible:ring-blue-300 px-8 py-3 text-[22px] font-extrabold text-white hover:scale-[1.02] cursor-pointer ${
                      isUploading
                        ? '!bg-gray-400 cursor-not-allowed'
                        : '!bg-[#0f70e6] hover:!bg-[#187dff] active:!bg-[#0b5fc4]'
                    }`}
                  >
                    {isUploading ? (
                      <div className="flex items-center gap-3">
                        <div className="w-6 h-6 border-2 border-white dark:border-gray-400 border-t-transparent rounded-full animate-spin" />
                        <span>Processing...</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-3">
                        <Upload className="w-6 h-6" />
                        <span>Upload Files</span>
                      </div>
                    )}
                  </button>

                  <div className="hidden sm:flex flex-col gap-1.5 items-center">
                    <p className="m-0 font-bold text-[20px] text-gray-700 dark:text-gray-300">
                      Drag & Drop or Click to Upload
                    </p>
                    <span className="text-[11px] text-gray-500 dark:text-gray-400 text-center">
                      JPG, PNG, WebP, MP4, WebM • Max 100MB
                    </span>
                  </div>
                </div>

                {/* Sample files */}
                <div className="max-w-md">
                  <div className="flex flex-col gap-2 sm:flex-row justify-between items-center">
                    <div className="flex flex-row sm:flex-col sm:justify-center text-gray-600 dark:text-gray-400 font-semibold">
                      <span className="mr-1 sm:mr-0">Try with samples:</span>
                    </div>
                    <div className="flex gap-2">
                      {DEMO_IMAGES.map((imageUrl, index) => (
                        <button
                          key={index}
                          onClick={() => handlePresetClick(imageUrl, index)}
                          disabled={loadingPresetIndex === index}
                          className="select-none shrink-0 relative focus-visible:ring focus-visible:ring-offset-2 focus-visible:ring-blue-300 dark:focus-visible:ring-blue-500 h-12 w-12 sm:w-16 sm:h-16 transition ease-in-out active:scale-[0.98] rounded-[14px] overflow-hidden border border-gray-200 dark:border-gray-600 hover:border-[#187dff] dark:hover:border-blue-400 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 cursor-pointer"
                          type="button"
                        >
                          <div className="w-full h-full z-0 overflow-hidden rounded-xl relative flex items-center justify-center">
                            <Zap className={`w-6 h-6 text-blue-500 dark:text-blue-400 ${loadingPresetIndex === index ? 'opacity-60' : ''}`} />
                          </div>
                          {loadingPresetIndex === index && (
                            <div className="absolute inset-0 flex items-center justify-center bg-black/30 dark:bg-black/50">
                              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                  <p className="text-[10px] sm:text-xs text-gray-500/90 dark:text-gray-400/90 text-center sm:text-left mt-4">
                    <span className="font-medium">🔒 Privacy First:</span> All processing happens locally in your browser. Your files never leave your device.
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Drag overlay */}
      {isDragActive && (
        <motion.div
          className="fixed inset-0 z-50 bg-black/20 dark:bg-black/40 backdrop-blur-sm flex items-center justify-center p-4 pointer-events-none"
          initial={{ opacity: 1 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 1 }}
        >
          <motion.div
            className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-3xl border-4 border-dashed border-blue-400 dark:border-blue-500 p-12 max-w-2xl w-full text-center shadow-2xl relative"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
          >
            {/* Corner decorations */}
            <div className="absolute top-4 left-4 w-8 h-8 border-l-4 border-t-4 border-blue-400 dark:border-blue-500 rounded-tl-lg"></div>
            <div className="absolute top-4 right-4 w-8 h-8 border-r-4 border-t-4 border-blue-400 dark:border-blue-500 rounded-tr-lg"></div>
            <div className="absolute bottom-4 left-4 w-8 h-8 border-l-4 border-b-4 border-blue-400 dark:border-blue-500 rounded-bl-lg"></div>
            <div className="absolute bottom-4 right-4 w-8 h-8 border-r-4 border-b-4 border-blue-400 rounded-br-lg"></div>

            {/* Upload icon */}
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Upload className="w-10 h-10 text-blue-500" />
            </div>

            <div className="text-4xl font-bold text-gray-800 mb-4">
              Drop your files here
            </div>
            <div className="text-lg text-gray-600 mb-8">
              Release to start compression
            </div>

            {/* File type indicators */}
            <div className="flex justify-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg border border-blue-200">
                <div className="w-8 h-8 bg-blue-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">IMG</div>
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-lg border border-green-200">
                <div className="w-8 h-8 bg-green-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">VID</div>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp,image/avif,image/gif,video/mp4,video/webm,video/avi,video/mov"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </>
  )
}
