'use client'

import { ProgressBarProps } from '@/types'
import { CheckCircle, AlertCircle, Loader2, Clock, X } from 'lucide-react'

export function ProgressBar({ 
  progress, 
  status, 
  showPercentage = true, 
  size = 'md' 
}: ProgressBarProps) {
  const getBarHeight = () => {
    switch (size) {
      case 'sm':
        return 'h-1'
      case 'lg':
        return 'h-4'
      default:
        return 'h-2'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-3 h-3'
      case 'lg':
        return 'w-5 h-5'
      default:
        return 'w-4 h-4'
    }
  }

  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs'
      case 'lg':
        return 'text-base'
      default:
        return 'text-sm'
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'pending':
        return {
          bg: 'bg-yellow-200',
          fill: 'bg-yellow-500',
          text: 'text-yellow-700',
          icon: <Clock className={`${getIconSize()} text-yellow-500`} />
        }
      case 'processing':
        return {
          bg: 'bg-blue-200',
          fill: 'bg-gradient-to-r from-blue-500 to-purple-500',
          text: 'text-blue-700',
          icon: <Loader2 className={`${getIconSize()} text-blue-500 animate-spin`} />
        }
      case 'completed':
        return {
          bg: 'bg-green-200',
          fill: 'bg-green-500',
          text: 'text-green-700',
          icon: <CheckCircle className={`${getIconSize()} text-green-500`} />
        }
      case 'error':
        return {
          bg: 'bg-red-200',
          fill: 'bg-red-500',
          text: 'text-red-700',
          icon: <AlertCircle className={`${getIconSize()} text-red-500`} />
        }
      case 'cancelled':
        return {
          bg: 'bg-gray-200',
          fill: 'bg-gray-500',
          text: 'text-gray-700',
          icon: <X className={`${getIconSize()} text-gray-500`} />
        }
      default:
        return {
          bg: 'bg-gray-200',
          fill: 'bg-gray-500',
          text: 'text-gray-700',
          icon: null
        }
    }
  }

  const statusConfig = getStatusColor()
  const displayProgress = status === 'completed' ? 100 : progress

  return (
    <div className="w-full">
      {/* Status and Percentage */}
      {(showPercentage || status !== 'processing') && (
        <div className={`flex items-center justify-between mb-1 ${getTextSize()}`}>
          <div className="flex items-center space-x-2">
            {statusConfig.icon}
            <span className={`capitalize font-medium ${statusConfig.text}`}>
              {status === 'processing' ? 'Processing...' : status}
            </span>
          </div>
          {showPercentage && (
            <span className={statusConfig.text}>
              {Math.round(displayProgress)}%
            </span>
          )}
        </div>
      )}

      {/* Progress Bar */}
      <div className={`w-full ${statusConfig.bg} rounded-full ${getBarHeight()}`}>
        <div 
          className={`${statusConfig.fill} ${getBarHeight()} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${displayProgress}%` }}
        />
      </div>

      {/* Additional Info for Large Size */}
      {size === 'lg' && status === 'processing' && (
        <div className="mt-2 text-xs text-gray-500">
          This may take a few moments depending on file size...
        </div>
      )}
    </div>
  )
}

// Batch Progress Bar for multiple files
interface BatchProgressBarProps {
  totalFiles: number
  completedFiles: number
  currentFile?: string
  overallProgress: number
}

export function BatchProgressBar({
  totalFiles,
  completedFiles,
  currentFile,
  overallProgress
}: BatchProgressBarProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">
          Processing Files ({completedFiles}/{totalFiles})
        </h3>
        <span className="text-sm text-gray-600 dark:text-gray-300">
          {Math.round(overallProgress)}%
        </span>
      </div>

      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-3">
        <div
          className="bg-gradient-to-r from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400 h-3 rounded-full transition-all duration-300"
          style={{ width: `${overallProgress}%` }}
        />
      </div>

      {currentFile && (
        <div className="text-sm text-gray-600 dark:text-gray-300">
          Currently processing: <span className="font-medium">{currentFile}</span>
        </div>
      )}
    </div>
  )
}
