'use client'

import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON>ap, Shield, Star } from 'lucide-react'
import Link from 'next/link'

const features = [
  {
    icon: Zap,
    text: "Lightning Fast Processing"
  },
  {
    icon: Shield,
    text: "100% Privacy Protected"
  },
  {
    icon: Star,
    text: "Professional Quality Results"
  }
]

export function FinalCTA() {
  return (
    <section className="py-20 px-4 bg-gradient-to-r from-blue-600/30 via-purple-600/30 to-blue-700/30 dark:from-blue-800/40 dark:via-purple-800/40 dark:to-blue-900/40 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Animated gradient orbs */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 dark:bg-white/5 rounded-full blur-xl animate-pulse" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/5 dark:bg-white/3 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/5 dark:bg-white/3 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        
        {/* Geometric shapes */}
        <motion.svg
          width="120"
          height="120"
          viewBox="0 0 120 120"
          className="absolute top-16 right-20 text-white/10 dark:text-white/5"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        >
          <polygon points="60,10 90,50 60,90 30,50" fill="currentColor" />
        </motion.svg>
        
        <motion.svg
          width="80"
          height="80"
          viewBox="0 0 80 80"
          className="absolute bottom-16 left-20 text-white/10 dark:text-white/5"
          animate={{ rotate: -360 }}
          transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
        >
          <circle cx="40" cy="40" r="30" fill="none" stroke="currentColor" strokeWidth="2" strokeDasharray="10 5" />
        </motion.svg>
      </div>

      <div className="max-w-4xl mx-auto text-center relative">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Badge */}
          <motion.div
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 dark:bg-white/10 backdrop-blur-sm rounded-full border border-white/30 dark:border-white/20 mb-8"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Star className="w-4 h-4 text-yellow-300" />
            <span className="text-sm font-medium text-white">Trusted by 50,000+ users worldwide</span>
          </motion.div>

          {/* Main heading */}
          <motion.h2 
            className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Ready to Compress <br />
            <span className="text-blue-200">
              Your Files?
            </span>
          </motion.h2>

          {/* Subtitle */}
          <motion.p 
            className="text-xl text-blue-100 mb-12 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Join millions of users who trust our compression tools. Start optimizing your images and videos today - completely free, no registration required.
          </motion.p>

          {/* Features list */}
          <motion.div 
            className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <motion.div 
                  key={index}
                  className="flex items-center gap-3 text-white"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <Icon className="w-4 h-4" />
                  </div>
                  <span className="font-medium">{feature.text}</span>
                </motion.div>
              )
            })}
          </motion.div>

          {/* CTA Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
          >
            <Link href="/compress">
              <motion.button
                className="inline-flex items-center gap-4 px-12 py-6 bg-white text-blue-600 font-bold text-xl rounded-2xl hover:bg-gray-100 transition-all duration-300 shadow-2xl hover:shadow-3xl group cursor-pointer"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                <Zap className="w-6 h-6 group-hover:text-yellow-500 transition-colors duration-300" />
                <span>Get Started for Free</span>
                <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
              </motion.button>
            </Link>
          </motion.div>

          {/* Trust indicators */}
          <motion.div 
            className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 text-blue-100"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm">No registration required</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm">100% free forever</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm">Privacy guaranteed</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
