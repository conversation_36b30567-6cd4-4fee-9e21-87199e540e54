import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/hooks/useAuth";
import { GoogleOneTapProvider } from "@/components/GoogleOneTapProvider";
import { ClientLayout } from "@/components/ClientLayout";
import { ThemeScript } from "@/components/ThemeScript";
import { DynamicToaster } from "@/components/DynamicToaster";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CompressHub - Free Online Image & Video Compression Tool",
  description: "Compress images and videos online for free. Reduce file sizes by up to 90% while maintaining quality. No upload required - all processing happens in your browser.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <ThemeScript />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200`}
      >
        <AuthProvider>
          <GoogleOneTapProvider>
            <ClientLayout>
              {children}
            </ClientLayout>
            <DynamicToaster />
          </GoogleOneTapProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
