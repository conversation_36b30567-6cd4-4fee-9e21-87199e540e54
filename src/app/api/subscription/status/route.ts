import { NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { orderOperations, usageOperations } from '@/lib/database'

export async function GET() {
  try {
    const user = await auth.getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get user's orders
    const orders = await orderOperations.getUserOrders(user.id)
    
    // Get current usage
    const todayUsage = await usageOperations.getUserUsageToday(user.id)
    
    // Calculate usage stats for the current month
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()
    const monthStart = new Date(currentYear, currentMonth, 1)
    
    // This would need a more sophisticated query in a real implementation
    // For now, we'll use today's usage as a placeholder
    const monthlyUsage = {
      compressions: todayUsage?.compressions_count || 0,
      apiCalls: todayUsage?.api_calls_count || 0,
      totalFileSizeMb: todayUsage?.total_file_size_mb || 0
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        membershipLevel: user.membership_level,
        membershipStatus: user.membership_status,
        membershipStartDate: user.membership_start_date,
        membershipEndDate: user.membership_end_date,
        isMembershipActive: user.is_membership_active,
        membershipDaysLeft: user.membership_days_left,
        points: user.points
      },
      usage: {
        today: todayUsage,
        monthly: monthlyUsage
      },
      orders: orders.slice(0, 5) // Return last 5 orders
    })
  } catch (error) {
    console.error('Get subscription status error:', error)
    
    return NextResponse.json(
      { error: 'Failed to fetch subscription status' },
      { status: 500 }
    )
  }
}
