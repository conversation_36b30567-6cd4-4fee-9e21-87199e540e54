import { NextRequest, NextResponse } from 'next/server'
import { OAuth2Client } from 'google-auth-library'
import { supabase, supabaseAdmin } from '@/lib/supabase'
import { authCookies } from '@/lib/auth'

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID)

interface GoogleUser {
  sub: string
  email: string
  name: string
  picture: string
  email_verified: boolean
}

async function updateOrCreateUser(username: string, email: string, avatarUrl: string) {
  try {
    // Check if user exists using maybe<PERSON>ing<PERSON> to avoid errors
    const { data: existingUser, error: selectError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', email)
      .maybeSingle()

    if (selectError) {
      console.error('Error checking existing user:', selectError)
      throw selectError
    }

    console.log('Existing user check result:', existingUser)

    if (existingUser) {
      // Update existing user
      const { data: updatedUser, error: updateError } = await supabaseAdmin
        .from('users')
        .update({
          last_login_time: new Date().toISOString(),
          avatar_url: avatarUrl || existingUser.avatar_url,
          username: username || existingUser.username,
          updated_at: new Date().toISOString()
        })
        .eq('email', email)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating user:', updateError)
        throw updateError
      }

      console.log('User updated successfully:', updatedUser.id)
      return updatedUser
    }

    // Create new user
    const { data: newUser, error: insertError } = await supabaseAdmin
      .from('users')
      .insert([
        {
          username,
          email,
          avatar_url: avatarUrl,
          last_login_time: new Date().toISOString(),
          points: 50, // Give new users 50 free compressions
          membership_level: 'free',
          membership_status: 'free',
          register_source: 'google_oauth',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
      ])
      .select()
      .single()

    if (insertError) {
      console.error('Error creating user:', insertError)
      throw insertError
    }

    console.log('New user created successfully:', newUser.id)
    return newUser
  } catch (error) {
    console.error('Database operation failed:', error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { credential } = body

    if (!credential) {
      return NextResponse.json({ error: 'Missing credential' }, { status: 400 })
    }

    // Verify JWT token
    const ticket = await client.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID,
    })

    const payload = ticket.getPayload()
    if (!payload) {
      return NextResponse.json(
        { error: 'Invalid token payload' },
        { status: 400 }
      )
    }

    const googleUser: GoogleUser = {
      sub: payload.sub,
      email: payload.email!,
      name: payload.name!,
      picture: payload.picture!,
      email_verified: payload.email_verified!,
    }

    console.log('Google One Tap user:', googleUser)

    // Update or create user directly using Supabase
    const user = await updateOrCreateUser(
      googleUser.name,
      googleUser.email,
      googleUser.picture
    )

    // Set authentication cookies
    await authCookies.setUserSession(googleUser.email)

    console.log('Google One Tap sign in successful')
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.username,
        avatar: user.avatar_url,
        membership_level: user.membership_level,
        membership_status: user.membership_status,
        points: user.points
      },
    })
  } catch (error) {
    console.error('Google One Tap verification error:', error)
    return NextResponse.json(
      {
        error: 'Authentication failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
