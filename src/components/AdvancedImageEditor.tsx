'use client'

import { useState, useRef, useEffect } from 'react'
import { 
  <PERSON>rop, 
  RotateCw, 
  Move, 
  ZoomIn, 
  ZoomOut, 
  Undo, 
  Redo, 
  Download,
  Filter,
  Type,
  Square,
  Circle,
  Sliders
} from 'lucide-react'
import toast from 'react-hot-toast'

interface AdvancedImageEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

type Tool = 'select' | 'crop' | 'rotate' | 'text' | 'shape' | 'filter'

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

export function AdvancedImageEditor({ file, onEditComplete }: AdvancedImageEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [activeTool, setActiveTool] = useState<Tool>('select')
  const [zoom, setZoom] = useState(100)
  const [rotation, setRotation] = useState(0)
  const [cropArea, setCropArea] = useState<CropArea | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 })
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })
  const [originalImageSize, setOriginalImageSize] = useState({ width: 0, height: 0 })
  const [filters, setFilters] = useState({
    brightness: 0,
    contrast: 0,
    saturation: 0,
    blur: 0
  })
  const [showFilterPanel, setShowFilterPanel] = useState(false)
  const [textElements, setTextElements] = useState<Array<{
    id: string
    text: string
    x: number
    y: number
    fontSize: number
    color: string
  }>>([])
  const [showTextPanel, setShowTextPanel] = useState(false)

  const tools = [
    { id: 'select', icon: Move, label: 'Select', shortcut: 'V' },
    { id: 'crop', icon: Crop, label: 'Crop', shortcut: 'C' },
    { id: 'rotate', icon: RotateCw, label: 'Rotate', shortcut: 'R' },
    { id: 'filter', icon: Filter, label: 'Filter', shortcut: 'F' },
    { id: 'text', icon: Type, label: 'Text', shortcut: 'T' },
    { id: 'shape', icon: Square, label: 'Shape', shortcut: 'S' }
  ]

  const initializeCanvas = () => {
    if (!canvasRef.current || !containerRef.current) return

    const canvas = canvasRef.current
    const container = containerRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size to container size
    const containerRect = container.getBoundingClientRect()
    canvas.width = containerRect.width
    canvas.height = containerRect.height

    // Create image element
    const img = new Image()
    imageRef.current = img

    img.onload = () => {
      console.log('Image loaded:', img.width, 'x', img.height)
      
      setOriginalImageSize({ width: img.width, height: img.height })
      
      // Calculate initial image size and position to fit in canvas
      const canvasWidth = canvas.width
      const canvasHeight = canvas.height
      const imgAspectRatio = img.width / img.height
      const canvasAspectRatio = canvasWidth / canvasHeight
      
      let displayWidth, displayHeight
      
      if (imgAspectRatio > canvasAspectRatio) {
        // Image is wider than canvas
        displayWidth = canvasWidth * 0.8 // Leave some margin
        displayHeight = displayWidth / imgAspectRatio
      } else {
        // Image is taller than canvas
        displayHeight = canvasHeight * 0.8 // Leave some margin
        displayWidth = displayHeight * imgAspectRatio
      }
      
      setImageSize({ width: displayWidth, height: displayHeight })
      setImagePosition({
        x: (canvasWidth - displayWidth) / 2,
        y: (canvasHeight - displayHeight) / 2
      })
      
      setIsLoading(false)
      drawCanvas()
    }

    img.onerror = () => {
      console.error('Failed to load image')
      setIsLoading(false)
      toast.error('Failed to load image')
    }

    // Load image from file
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      img.src = result
    }
    
    reader.onerror = () => {
      console.error('Failed to read file')
      setIsLoading(false)
      toast.error('Failed to read file')
    }
    
    reader.readAsDataURL(file)
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      initializeCanvas()
    }, 50)

    return () => clearTimeout(timer)
  }, [file])

  const drawCanvas = () => {
    if (!canvasRef.current || !imageRef.current) return
    
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    const img = imageRef.current
    
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    // Draw checkerboard background
    drawCheckerboard(ctx, canvas.width, canvas.height)
    
    // Save context
    ctx.save()
    
    // Apply transformations
    const centerX = imagePosition.x + imageSize.width / 2
    const centerY = imagePosition.y + imageSize.height / 2

    ctx.translate(centerX, centerY)
    ctx.rotate((rotation * Math.PI) / 180)
    ctx.scale(zoom / 100, zoom / 100)

    // Apply filters
    let filterString = ''
    if (filters.brightness !== 0) {
      filterString += `brightness(${100 + filters.brightness}%) `
    }
    if (filters.contrast !== 0) {
      filterString += `contrast(${100 + filters.contrast}%) `
    }
    if (filters.saturation !== 0) {
      filterString += `saturate(${100 + filters.saturation}%) `
    }
    if (filters.blur > 0) {
      filterString += `blur(${filters.blur}px) `
    }

    if (filterString) {
      ctx.filter = filterString.trim()
    }

    // Draw image
    ctx.drawImage(
      img,
      -imageSize.width / 2,
      -imageSize.height / 2,
      imageSize.width,
      imageSize.height
    )

    // Reset filter
    ctx.filter = 'none'

    // Restore context
    ctx.restore()
    
    // Draw text elements
    textElements.forEach(textElement => {
      ctx.font = `${textElement.fontSize}px Arial`
      ctx.fillStyle = textElement.color
      ctx.fillText(textElement.text, textElement.x, textElement.y)
    })

    // Draw crop overlay if crop tool is active
    if (activeTool === 'crop' && cropArea) {
      drawCropOverlay(ctx, cropArea)
    }
  }

  const drawCheckerboard = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    const squareSize = 20
    ctx.fillStyle = '#f0f0f0'
    ctx.fillRect(0, 0, width, height)
    
    ctx.fillStyle = '#e0e0e0'
    for (let x = 0; x < width; x += squareSize) {
      for (let y = 0; y < height; y += squareSize) {
        if ((Math.floor(x / squareSize) + Math.floor(y / squareSize)) % 2 === 0) {
          ctx.fillRect(x, y, squareSize, squareSize)
        }
      }
    }
  }

  const drawCropOverlay = (ctx: CanvasRenderingContext2D, crop: CropArea) => {
    const canvas = canvasRef.current!
    
    // Draw dark overlay
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    // Clear crop area
    ctx.clearRect(crop.x, crop.y, crop.width, crop.height)
    
    // Draw crop border
    ctx.strokeStyle = '#007bff'
    ctx.lineWidth = 2
    ctx.strokeRect(crop.x, crop.y, crop.width, crop.height)
    
    // Draw corner handles
    const handleSize = 8
    const handles = [
      { x: crop.x - handleSize/2, y: crop.y - handleSize/2 },
      { x: crop.x + crop.width - handleSize/2, y: crop.y - handleSize/2 },
      { x: crop.x - handleSize/2, y: crop.y + crop.height - handleSize/2 },
      { x: crop.x + crop.width - handleSize/2, y: crop.y + crop.height - handleSize/2 }
    ]
    
    ctx.fillStyle = '#007bff'
    handles.forEach(handle => {
      ctx.fillRect(handle.x, handle.y, handleSize, handleSize)
    })
  }

  useEffect(() => {
    if (!isLoading) {
      drawCanvas()
    }
  }, [zoom, rotation, imagePosition, imageSize, activeTool, cropArea, isLoading, filters, textElements])

  const handleCanvasMouseDown = (e: React.MouseEvent) => {
    if (activeTool === 'select') {
      setIsDragging(true)
      setDragStart({ x: e.clientX, y: e.clientY })
    } else if (activeTool === 'crop') {
      const rect = canvasRef.current!.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      setCropArea({
        x: x,
        y: y,
        width: 0,
        height: 0
      })
      setIsDragging(true)
      setDragStart({ x, y })
    } else if (activeTool === 'text') {
      const rect = canvasRef.current!.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      const newText = {
        id: Date.now().toString(),
        text: 'New Text',
        x: x,
        y: y,
        fontSize: 24,
        color: '#000000'
      }

      setTextElements(prev => [...prev, newText])
      toast.success('Text added! Edit in the text panel.')
    }
  }

  const handleCanvasMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return

    if (activeTool === 'select') {
      const deltaX = e.clientX - dragStart.x
      const deltaY = e.clientY - dragStart.y
      
      setImagePosition(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }))
      
      setDragStart({ x: e.clientX, y: e.clientY })
    } else if (activeTool === 'crop' && cropArea) {
      const rect = canvasRef.current!.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top
      
      setCropArea({
        x: Math.min(dragStart.x, x),
        y: Math.min(dragStart.y, y),
        width: Math.abs(x - dragStart.x),
        height: Math.abs(y - dragStart.y)
      })
    }
  }

  const handleCanvasMouseUp = () => {
    setIsDragging(false)
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 500))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25))
  }

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360)
    toast.success('Image rotated 90°')
  }

  const applyCrop = () => {
    if (!cropArea || !canvasRef.current || !imageRef.current) return

    // Create a new canvas for the cropped image
    const cropCanvas = document.createElement('canvas')
    const cropCtx = cropCanvas.getContext('2d')
    if (!cropCtx) return

    // Set crop canvas size to crop area size
    cropCanvas.width = cropArea.width
    cropCanvas.height = cropArea.height

    // Calculate the transformation matrix
    const centerX = imagePosition.x + imageSize.width / 2
    const centerY = imagePosition.y + imageSize.height / 2

    // Calculate which part of the original image corresponds to the crop area
    const zoomFactor = zoom / 100
    const rotationRad = (rotation * Math.PI) / 180

    // Transform crop coordinates back to image coordinates
    const cropCenterX = cropArea.x + cropArea.width / 2
    const cropCenterY = cropArea.y + cropArea.height / 2

    // Calculate offset from image center to crop center
    const offsetX = (cropCenterX - centerX) / zoomFactor
    const offsetY = (cropCenterY - centerY) / zoomFactor

    // Apply inverse rotation to get original image coordinates
    const cos = Math.cos(-rotationRad)
    const sin = Math.sin(-rotationRad)
    const rotatedOffsetX = offsetX * cos - offsetY * sin
    const rotatedOffsetY = offsetX * sin + offsetY * cos

    // Calculate source rectangle in original image
    const sourceWidth = cropArea.width / zoomFactor
    const sourceHeight = cropArea.height / zoomFactor
    const sourceCenterX = imageSize.width / 2 + rotatedOffsetX
    const sourceCenterY = imageSize.height / 2 + rotatedOffsetY

    const sourceX = sourceCenterX - sourceWidth / 2
    const sourceY = sourceCenterY - sourceHeight / 2

    // Scale to original image coordinates
    const scaleX = originalImageSize.width / imageSize.width
    const scaleY = originalImageSize.height / imageSize.height

    const finalSourceX = sourceX * scaleX
    const finalSourceY = sourceY * scaleY
    const finalSourceWidth = sourceWidth * scaleX
    const finalSourceHeight = sourceHeight * scaleY

    // Draw the cropped portion
    cropCtx.drawImage(
      imageRef.current,
      Math.max(0, finalSourceX),
      Math.max(0, finalSourceY),
      Math.min(finalSourceWidth, originalImageSize.width - Math.max(0, finalSourceX)),
      Math.min(finalSourceHeight, originalImageSize.height - Math.max(0, finalSourceY)),
      0, 0, cropArea.width, cropArea.height
    )

    // Convert to blob and update
    cropCanvas.toBlob((blob) => {
      if (blob) {
        onEditComplete(blob)
        toast.success('Image cropped successfully!')
      }
    }, 'image/png', 0.9)

    setCropArea(null)
    setActiveTool('select')
  }

  const handleRotateClick = () => {
    setRotation(prev => (prev + 90) % 360)
    toast.success('Image rotated 90°')
  }

  const handleExport = () => {
    if (!canvasRef.current || !imageRef.current) return

    // Create a new canvas with just the image (no background)
    const exportCanvas = document.createElement('canvas')
    const exportCtx = exportCanvas.getContext('2d')
    if (!exportCtx) return

    // Calculate the actual image bounds after transformations
    const zoomFactor = zoom / 100
    const rotationRad = (rotation * Math.PI) / 180

    // For simplicity, export the original image with current rotation
    if (rotation === 0) {
      // No rotation, export original
      exportCanvas.width = originalImageSize.width
      exportCanvas.height = originalImageSize.height
      exportCtx.drawImage(imageRef.current, 0, 0)
    } else {
      // With rotation, calculate new dimensions
      const cos = Math.abs(Math.cos(rotationRad))
      const sin = Math.abs(Math.sin(rotationRad))
      const newWidth = originalImageSize.width * cos + originalImageSize.height * sin
      const newHeight = originalImageSize.width * sin + originalImageSize.height * cos

      exportCanvas.width = newWidth
      exportCanvas.height = newHeight

      exportCtx.translate(newWidth / 2, newHeight / 2)
      exportCtx.rotate(rotationRad)
      exportCtx.drawImage(
        imageRef.current,
        -originalImageSize.width / 2,
        -originalImageSize.height / 2
      )
    }

    exportCanvas.toBlob((blob) => {
      if (blob) {
        onEditComplete(blob)
        toast.success('Image exported successfully!')
      }
    }, 'image/png', 0.9)
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden relative h-full flex flex-col">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white dark:bg-gray-800 z-20 flex items-center justify-center">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <span className="ml-4 text-gray-600 dark:text-gray-400">Loading image...</span>
          </div>
        </div>
      )}

      {/* Top Toolbar */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-1">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => {
                setActiveTool(tool.id as Tool)
                if (tool.id === 'filter') {
                  setShowFilterPanel(!showFilterPanel)
                  setShowTextPanel(false)
                } else if (tool.id === 'text') {
                  setShowTextPanel(!showTextPanel)
                  setShowFilterPanel(false)
                } else {
                  setShowFilterPanel(false)
                  setShowTextPanel(false)
                }
              }}
              className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                activeTool === tool.id
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
              title={`${tool.label} (${tool.shortcut})`}
            >
              <tool.icon className="w-5 h-5" />
              <span className="text-xs mt-1">{tool.label}</span>
            </button>
          ))}
        </div>

        <div className="flex items-center space-x-4">
          {/* Zoom Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleZoomOut}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <span className="text-sm text-gray-600 dark:text-gray-300 w-16 text-center">
              {zoom}%
            </span>
            <button
              onClick={handleZoomIn}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRotateClick}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <RotateCw className="w-4 h-4" />
            </button>
            
            {activeTool === 'crop' && cropArea && (
              <button
                onClick={applyCrop}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Apply Crop
              </button>
            )}
            
            <button
              onClick={handleExport}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Save</span>
            </button>
          </div>
        </div>
      </div>

      {/* Canvas Area */}
      <div className="flex-1 flex">
        <div
          ref={containerRef}
          className="flex-1 relative overflow-hidden bg-gray-100 dark:bg-gray-900"
          style={{ cursor: activeTool === 'select' ? 'move' : activeTool === 'crop' ? 'crosshair' : 'default' }}
        >
          <canvas
            ref={canvasRef}
            className="absolute inset-0"
            onMouseDown={handleCanvasMouseDown}
            onMouseMove={handleCanvasMouseMove}
            onMouseUp={handleCanvasMouseUp}
            onMouseLeave={handleCanvasMouseUp}
          />
        </div>

        {/* Filter Panel */}
        {showFilterPanel && (
          <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Filters</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Brightness: {filters.brightness}
                </label>
                <input
                  type="range"
                  min={-100}
                  max={100}
                  value={filters.brightness}
                  onChange={(e) => setFilters(prev => ({ ...prev, brightness: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Contrast: {filters.contrast}
                </label>
                <input
                  type="range"
                  min={-100}
                  max={100}
                  value={filters.contrast}
                  onChange={(e) => setFilters(prev => ({ ...prev, contrast: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Saturation: {filters.saturation}
                </label>
                <input
                  type="range"
                  min={-100}
                  max={100}
                  value={filters.saturation}
                  onChange={(e) => setFilters(prev => ({ ...prev, saturation: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Blur: {filters.blur}px
                </label>
                <input
                  type="range"
                  min={0}
                  max={10}
                  value={filters.blur}
                  onChange={(e) => setFilters(prev => ({ ...prev, blur: Number(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <button
                onClick={() => setFilters({ brightness: 0, contrast: 0, saturation: 0, blur: 0 })}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Reset Filters
              </button>
            </div>
          </div>
        )}

        {/* Text Panel */}
        {showTextPanel && (
          <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Text Elements</h3>

            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Click on the canvas to add text
              </p>

              {textElements.map((textElement, index) => (
                <div key={textElement.id} className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={textElement.text}
                      onChange={(e) => {
                        setTextElements(prev => prev.map(el =>
                          el.id === textElement.id ? { ...el, text: e.target.value } : el
                        ))
                      }}
                      className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm"
                      placeholder="Enter text..."
                    />

                    <div className="flex items-center space-x-2">
                      <label className="text-xs text-gray-600 dark:text-gray-400">Size:</label>
                      <input
                        type="range"
                        min={12}
                        max={72}
                        value={textElement.fontSize}
                        onChange={(e) => {
                          setTextElements(prev => prev.map(el =>
                            el.id === textElement.id ? { ...el, fontSize: Number(e.target.value) } : el
                          ))
                        }}
                        className="flex-1 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                      />
                      <span className="text-xs text-gray-600 dark:text-gray-400 w-8">{textElement.fontSize}</span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <label className="text-xs text-gray-600 dark:text-gray-400">Color:</label>
                      <input
                        type="color"
                        value={textElement.color}
                        onChange={(e) => {
                          setTextElements(prev => prev.map(el =>
                            el.id === textElement.id ? { ...el, color: e.target.value } : el
                          ))
                        }}
                        className="w-8 h-6 rounded border border-gray-300 dark:border-gray-600 cursor-pointer"
                      />
                    </div>

                    <button
                      onClick={() => {
                        setTextElements(prev => prev.filter(el => el.id !== textElement.id))
                      }}
                      className="w-full px-2 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
