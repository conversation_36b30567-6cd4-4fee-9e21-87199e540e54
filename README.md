# CompressHub - 图片视频压缩工具

一个功能强大的在线图片和视频压缩工具，完全在浏览器中运行，保护您的隐私。

## ✨ 主要功能

### 🖼️ 图片压缩
- **多格式支持**: JPEG, PNG, WebP, GIF, BMP, TIFF
- **批量处理**: 同时处理多个文件
- **质量控制**: 可调节压缩质量和文件大小
- **格式转换**: 支持不同格式之间的转换
- **透明度保持**: PNG图片透明区域完美保留
- **元数据处理**: 可选择保留或移除图片元数据

### 🎥 视频压缩
- **多格式支持**: MP4, AVI, MOV, WMV, FLV, WebM, MKV, 3GP
- **分辨率调整**: 自定义输出分辨率
- **质量控制**: 精确控制压缩质量
- **格式转换**: 支持主流视频格式转换
- **进度显示**: 实时显示压缩进度

### 🎭 GIF优化
- **逐帧处理**: 智能GIF压缩算法
- **颜色优化**: 减少颜色数量以降低文件大小
- **帧率调整**: 可调节GIF播放帧率
- **格式转换**: GIF转视频格式

### 🚀 高级功能
- **并行处理**: 多文件同时压缩
- **队列管理**: 完整的任务队列系统
- **进度监控**: 实时进度条和状态显示
- **暂停/恢复**: 可暂停和恢复压缩任务
- **批量下载**: 一键下载所有压缩文件
- **拖拽上传**: 支持拖拽文件上传
- **响应式设计**: 完美适配移动端

## 🔒 隐私保护

- **本地处理**: 所有文件处理都在您的浏览器中完成
- **无文件上传**: 文件从不离开您的设备
- **离线可用**: 支持离线使用
- **无数据收集**: 不收集任何用户数据

## 🛠️ 技术栈

- **前端框架**: Next.js 15 + React 19
- **样式**: Tailwind CSS
- **UI组件**: Radix UI
- **图片压缩**: browser-image-compression
- **视频处理**: FFmpeg.wasm
- **文件上传**: react-dropzone
- **图标**: Lucide React
- **类型安全**: TypeScript

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm/yarn/pnpm

### 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
npm run build
npm run start
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── compress/          # 压缩页面
│   ├── test/              # 测试页面
│   └── page.tsx           # 首页
├── components/            # React组件
│   ├── FileCard.tsx       # 文件卡片组件
│   ├── ProgressBar.tsx    # 进度条组件
│   ├── CompressionSettings.tsx # 压缩设置
│   ├── QueueManager.tsx   # 队列管理
│   ├── UploadArea.tsx     # 上传区域
│   └── Header.tsx         # 头部导航
├── services/              # 核心服务
│   ├── imageCompression.ts # 图片压缩服务
│   ├── videoCompression.ts # 视频压缩服务
│   └── gifCompression.ts   # GIF处理服务
├── hooks/                 # 自定义Hooks
│   └── useCompressionManager.ts # 压缩管理Hook
├── lib/                   # 工具函数
│   └── utils.ts           # 通用工具
└── types/                 # TypeScript类型定义
    └── index.ts
```

## 🎯 使用方法

### 基本使用
1. 访问应用首页
2. 点击"Start Compressing"进入压缩页面
3. 拖拽或选择要压缩的文件
4. 调整压缩设置（质量、格式等）
5. 点击"Start Compression"开始处理
6. 下载压缩后的文件

### 高级功能
- **批量处理**: 一次选择多个文件进行批量压缩
- **队列管理**: 查看处理队列，暂停/恢复任务
- **格式转换**: 在压缩的同时转换文件格式
- **质量预设**: 使用预设的压缩质量配置

## 🔧 配置选项

### 图片压缩选项
- `quality`: 压缩质量 (0-1)
- `maxWidth/maxHeight`: 最大尺寸限制
- `outputFormat`: 输出格式
- `maintainAspectRatio`: 保持宽高比
- `removeMetadata`: 移除元数据
- `preserveTransparency`: 保持透明度

### 视频压缩选项
- `quality`: 压缩质量 (0-1)
- `bitrate`: 视频比特率
- `fps`: 帧率
- `codec`: 视频编码器
- `audioCodec`: 音频编码器

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

- [browser-image-compression](https://github.com/Donaldcwl/browser-image-compression) - 图片压缩库
- [FFmpeg.wasm](https://github.com/ffmpegwasm/ffmpeg.wasm) - 视频处理库
- [Next.js](https://nextjs.org/) - React框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架
- [Radix UI](https://www.radix-ui.com/) - UI组件库
