'use client'

import { motion } from 'framer-motion'
import { Upload, Settings, Download, Zap, FileImage, Video, Image as ImageIcon } from 'lucide-react'

// Animated Upload Icon
export function AnimatedUpload({ isActive = false }: { isActive?: boolean }) {
  return (
    <motion.div
      animate={isActive ? {
        scale: [1, 1.1, 1],
        rotate: [0, 5, -5, 0]
      } : {}}
      transition={{
        duration: 2,
        repeat: isActive ? Infinity : 0,
        ease: "easeInOut"
      }}
    >
      <Upload className="w-8 h-8" />
    </motion.div>
  )
}

// Animated Processing Icon
export function AnimatedProcessing({ isActive = false }: { isActive?: boolean }) {
  return (
    <motion.div
      animate={isActive ? {
        rotate: 360
      } : {}}
      transition={{
        duration: 2,
        repeat: isActive ? Infinity : 0,
        ease: "linear"
      }}
    >
      <Settings className="w-8 h-8" />
    </motion.div>
  )
}

// Animated Download Icon
export function AnimatedDownload({ isActive = false }: { isActive?: boolean }) {
  return (
    <motion.div
      animate={isActive ? {
        y: [0, -5, 0]
      } : {}}
      transition={{
        duration: 1,
        repeat: isActive ? Infinity : 0,
        ease: "easeInOut"
      }}
    >
      <Download className="w-8 h-8" />
    </motion.div>
  )
}

// Animated Compression Visualization
export function CompressionAnimation({ isActive = false }: { isActive?: boolean }) {
  return (
    <div className="flex items-center space-x-4">
      {/* Large File */}
      <motion.div
        animate={isActive ? {
          scale: [1, 0.8, 1]
        } : {}}
        transition={{
          duration: 2,
          repeat: isActive ? Infinity : 0,
          ease: "easeInOut"
        }}
        className="relative"
      >
        <div className="w-16 h-16 bg-red-100 rounded-xl flex items-center justify-center">
          <FileImage className="w-8 h-8 text-red-500" />
        </div>
        <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
          5MB
        </div>
      </motion.div>

      {/* Arrow with pulse */}
      <motion.div
        animate={isActive ? {
          x: [0, 10, 0],
          opacity: [0.5, 1, 0.5]
        } : {}}
        transition={{
          duration: 1.5,
          repeat: isActive ? Infinity : 0,
          ease: "easeInOut"
        }}
      >
        <Zap className="w-6 h-6 text-blue-500" />
      </motion.div>

      {/* Compressed File */}
      <motion.div
        animate={isActive ? {
          scale: [1, 1.1, 1]
        } : {}}
        transition={{
          duration: 2,
          repeat: isActive ? Infinity : 0,
          ease: "easeInOut",
          delay: 0.5
        }}
        className="relative"
      >
        <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center">
          <FileImage className="w-8 h-8 text-green-500" />
        </div>
        <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
          500KB
        </div>
      </motion.div>
    </div>
  )
}

// Floating File Icons
export function FloatingIcons() {
  const icons = [
    { Icon: ImageIcon, color: 'text-blue-500', delay: 0, startX: 20, endX: 80 },
    { Icon: Video, color: 'text-purple-500', delay: 0.5, startX: 60, endX: 30 },
    { Icon: FileImage, color: 'text-pink-500', delay: 1, startX: 10, endX: 90 }
  ]

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {icons.map(({ Icon, color, delay, startX, endX }, index) => (
        <motion.div
          key={index}
          className={`absolute ${color} opacity-20`}
          initial={{ y: 100, x: startX, opacity: 0 }}
          animate={{
            y: -100,
            x: endX,
            opacity: [0, 0.3, 0],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            delay: delay,
            ease: "linear"
          }}
          style={{
            left: `${startX}%`,
          }}
        >
          <Icon className="w-8 h-8" />
        </motion.div>
      ))}
    </div>
  )
}

// Progress Ring Animation
export function ProgressRing({ progress = 0, size = 120 }: { progress?: number; size?: number }) {
  const radius = (size - 20) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = `${circumference} ${circumference}`
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <div className="relative" style={{ width: size, height: size }}>
      <svg
        className="transform -rotate-90"
        width={size}
        height={size}
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth="8"
          fill="transparent"
          className="text-gray-200"
        />
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="url(#gradient)"
          strokeWidth="8"
          fill="transparent"
          strokeDasharray={strokeDasharray}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          strokeLinecap="round"
        />
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#3B82F6" />
            <stop offset="100%" stopColor="#8B5CF6" />
          </linearGradient>
        </defs>
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-2xl font-bold text-gray-900">{Math.round(progress)}%</span>
      </div>
    </div>
  )
}

// Pulse Animation for buttons
export function PulseButton({ children, isActive = false, ...props }: any) {
  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      animate={isActive ? {
        boxShadow: [
          "0 0 0 0 rgba(59, 130, 246, 0.7)",
          "0 0 0 10px rgba(59, 130, 246, 0)",
          "0 0 0 0 rgba(59, 130, 246, 0)"
        ]
      } : {}}
      transition={{
        duration: 2,
        repeat: isActive ? Infinity : 0,
        ease: "easeInOut"
      }}
      {...props}
    >
      {children}
    </motion.button>
  )
}

// Slide in animation for cards
export function SlideInCard({ children, delay = 0, ...props }: any) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      {...props}
    >
      {children}
    </motion.div>
  )
}
