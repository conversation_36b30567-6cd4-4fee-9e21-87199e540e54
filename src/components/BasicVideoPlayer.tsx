'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Pause, Save } from 'lucide-react'
import toast from 'react-hot-toast'

interface BasicVideoPlayerProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

export function BasicVideoPlayer({ file, onEditComplete }: BasicVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  useEffect(() => {
    console.log('BasicVideoPlayer: Setting up video for file:', file.name)
    
    if (!videoRef.current) {
      console.error('Video ref not available')
      return
    }

    const video = videoRef.current
    const url = URL.createObjectURL(file)
    console.log('Created blob URL:', url)

    // Set up all event listeners before setting src
    const handleLoadedMetadata = () => {
      console.log('🎉 loadedmetadata event fired!')
      console.log('Video duration:', video.duration)
      console.log('Video readyState:', video.readyState)
      
      if (video.duration && !isNaN(video.duration)) {
        setDuration(video.duration)
        setIsLoading(false)
        console.log('✅ Video is ready to play!')
      } else {
        console.error('Invalid duration:', video.duration)
        setIsLoading(false)
        toast.error('Invalid video file')
      }
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handlePlay = () => {
      console.log('Video play event')
      setIsPlaying(true)
    }

    const handlePause = () => {
      console.log('Video pause event')
      setIsPlaying(false)
    }

    const handleError = (e: Event) => {
      console.error('Video error event:', e)
      console.error('Video error object:', video.error)
      setIsLoading(false)
      toast.error('Failed to load video: ' + (video.error?.message || 'Unknown error'))
    }

    const handleLoadStart = () => {
      console.log('📥 loadstart event fired')
    }

    const handleCanPlay = () => {
      console.log('✅ canplay event fired')
    }

    const handleLoadedData = () => {
      console.log('📊 loadeddata event fired')
    }

    // Add all event listeners
    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('error', handleError)
    video.addEventListener('loadstart', handleLoadStart)
    video.addEventListener('canplay', handleCanPlay)
    video.addEventListener('loadeddata', handleLoadedData)

    // Set source and load
    console.log('Setting video src and calling load()')
    video.src = url
    video.load()

    // Cleanup function
    return () => {
      console.log('Cleaning up video resources')
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('error', handleError)
      video.removeEventListener('loadstart', handleLoadStart)
      video.removeEventListener('canplay', handleCanPlay)
      video.removeEventListener('loadeddata', handleLoadedData)
      URL.revokeObjectURL(url)
    }
  }, [file])

  const togglePlay = () => {
    if (!videoRef.current) return
    
    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleExport = async () => {
    try {
      const blob = new Blob([file], { type: file.type })
      onEditComplete(blob)
      toast.success('Video exported successfully!')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export video')
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-gray-100">
        Basic Video Player - {file.name}
      </h2>
      
      {isLoading && (
        <div className="flex items-center justify-center h-64 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading video...</p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              File: {file.name} ({Math.round(file.size / 1024 / 1024)}MB)
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Type: {file.type}
            </p>
          </div>
        </div>
      )}

      {!isLoading && (
        <>
          {/* Video Player */}
          <div className="relative bg-black rounded-lg overflow-hidden mb-6">
            <video
              ref={videoRef}
              className="w-full h-auto max-h-96 object-contain"
              controls={false}
              playsInline
              preload="metadata"
            />
            
            {/* Simple Controls */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={togglePlay}
                    className="text-white hover:text-blue-400 transition-colors"
                  >
                    {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8" />}
                  </button>
                  
                  <div className="text-white text-sm">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </div>
                </div>

                <button
                  onClick={handleExport}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>Export</span>
                </button>
              </div>
            </div>
          </div>

          {/* Video Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Video Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Duration:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(duration)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">File Size:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{Math.round(file.size / 1024 / 1024)}MB</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Format:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{file.type}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Current Time:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(currentTime)}</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
