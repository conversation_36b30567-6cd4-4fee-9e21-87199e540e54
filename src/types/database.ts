export interface User {
  id: string
  created_at: string
  updated_at: string
  email: string
  username?: string
  avatar_url?: string
  last_login_time?: string
  points: number
  membership_status: 'free' | 'trial' | 'active' | 'expired' | 'cancelled'
  membership_level: 'free' | 'pro'
  membership_start_date?: string
  membership_end_date?: string
  trial_used: boolean
  trial_start_date?: string
  trial_end_date?: string
  stripe_customer_id?: string
  stripe_subscription_id?: string
  country?: string
  register_source?: string
  register_ip?: string
}

export interface MembershipLevel {
  id: number
  name: string
  price: number
  currency: string
  billing_period: 'monthly' | 'yearly'
  features: string[]
  max_compressions_per_month: number
  max_file_size_mb: number
  api_calls_per_month: number
  stripe_price_id?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Order {
  id: string
  user_id: string
  order_number: string
  membership_level_id: number
  amount: number
  currency: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  payment_method?: string
  stripe_payment_intent_id?: string
  stripe_subscription_id?: string
  created_at: string
  updated_at: string
}

export interface UserUsage {
  id: string
  user_id: string
  date: string
  compressions_count: number
  api_calls_count: number
  total_file_size_mb: number
  created_at: string
  updated_at: string
}

export interface CompressionLog {
  id: string
  user_id?: string
  file_name: string
  file_type: string
  original_size_mb: number
  compressed_size_mb: number
  compression_ratio: number
  processing_time_ms: number
  ip_address?: string
  user_agent?: string
  created_at: string
}
