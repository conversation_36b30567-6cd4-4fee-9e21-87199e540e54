import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'
import ffmpeg from 'fluent-ffmpeg'

// Configure ffmpeg path if needed
// ffmpeg.setFfmpegPath('/path/to/ffmpeg')

export async function POST(request: NextRequest) {
  const tempDir = path.join(process.cwd(), 'temp')
  let inputPath = ''
  let outputPath = ''

  try {
    // Ensure temp directory exists
    await fs.mkdir(tempDir, { recursive: true })

    const formData = await request.formData()
    const file = formData.get('file') as File
    const operations = formData.get('operations') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!operations) {
      return NextResponse.json({ error: 'No operations provided' }, { status: 400 })
    }

    const buffer = Buffer.from(await file.arrayBuffer())
    const parsedOperations = JSON.parse(operations)

    // Create temporary input file
    const inputId = uuidv4()
    const outputId = uuidv4()
    const inputExt = path.extname(file.name) || '.mp4'
    const outputFormat = formData.get('format') as string || 'mp4'
    
    inputPath = path.join(tempDir, `input_${inputId}${inputExt}`)
    outputPath = path.join(tempDir, `output_${outputId}.${outputFormat}`)

    await fs.writeFile(inputPath, buffer)

    // Process video with ffmpeg
    await new Promise<void>((resolve, reject) => {
      let command = ffmpeg(inputPath)

      // Apply operations
      for (const operation of parsedOperations) {
        switch (operation.type) {
          case 'trim':
            if (operation.startTime !== undefined) {
              command = command.seekInput(operation.startTime)
            }
            if (operation.duration !== undefined) {
              command = command.duration(operation.duration)
            } else if (operation.endTime !== undefined && operation.startTime !== undefined) {
              command = command.duration(operation.endTime - operation.startTime)
            }
            break

          case 'resize':
            if (operation.width && operation.height) {
              command = command.size(`${operation.width}x${operation.height}`)
            } else if (operation.scale) {
              command = command.videoFilters(`scale=iw*${operation.scale}:ih*${operation.scale}`)
            }
            break

          case 'crop':
            if (operation.width && operation.height) {
              const x = operation.x || 0
              const y = operation.y || 0
              command = command.videoFilters(`crop=${operation.width}:${operation.height}:${x}:${y}`)
            }
            break

          case 'rotate':
            if (operation.angle) {
              // Convert angle to radians for ffmpeg
              const radians = (operation.angle * Math.PI) / 180
              command = command.videoFilters(`rotate=${radians}`)
            }
            break

          case 'flip':
            if (operation.horizontal) {
              command = command.videoFilters('hflip')
            }
            if (operation.vertical) {
              command = command.videoFilters('vflip')
            }
            break

          case 'volume':
            if (operation.level !== undefined) {
              command = command.audioFilters(`volume=${operation.level}`)
            }
            break

          case 'speed':
            if (operation.factor && operation.factor !== 1) {
              // Speed up/slow down video
              command = command.videoFilters(`setpts=${1/operation.factor}*PTS`)
              command = command.audioFilters(`atempo=${operation.factor}`)
            }
            break

          case 'brightness':
            if (operation.value !== undefined) {
              command = command.videoFilters(`eq=brightness=${operation.value / 100}`)
            }
            break

          case 'contrast':
            if (operation.value !== undefined) {
              command = command.videoFilters(`eq=contrast=${1 + operation.value / 100}`)
            }
            break

          case 'saturation':
            if (operation.value !== undefined) {
              command = command.videoFilters(`eq=saturation=${1 + operation.value / 100}`)
            }
            break

          case 'blur':
            if (operation.radius > 0) {
              command = command.videoFilters(`boxblur=${operation.radius}:${operation.radius}`)
            }
            break

          case 'watermark':
            if (operation.enabled && operation.text) {
              // Add text watermark
              const fontSize = operation.fontSize || 24
              const color = operation.color || 'white'
              const opacity = operation.opacity || 0.7
              const x = operation.x || 10
              const y = operation.y || 10
              
              command = command.videoFilters(
                `drawtext=text='${operation.text}':fontsize=${fontSize}:fontcolor=${color}@${opacity}:x=${x}:y=${y}`
              )
            }
            break

          case 'fadeIn':
            if (operation.duration > 0) {
              command = command.videoFilters(`fade=t=in:st=0:d=${operation.duration}`)
            }
            break

          case 'fadeOut':
            if (operation.duration > 0 && operation.startTime !== undefined) {
              command = command.videoFilters(`fade=t=out:st=${operation.startTime}:d=${operation.duration}`)
            }
            break

          default:
            console.warn(`Unknown operation type: ${operation.type}`)
        }
      }

      // Set output format and quality
      const quality = parseInt(formData.get('quality') as string) || 23 // CRF value for H.264
      
      command
        .videoCodec('libx264')
        .audioCodec('aac')
        .outputOptions([
          `-crf ${quality}`,
          '-preset medium',
          '-movflags +faststart' // For web streaming
        ])
        .output(outputPath)
        .on('end', () => {
          console.log('Video processing completed')
          resolve()
        })
        .on('error', (err) => {
          console.error('FFmpeg error:', err)
          reject(err)
        })
        .on('progress', (progress) => {
          console.log(`Processing: ${progress.percent}% done`)
        })
        .run()
    })

    // Read the processed video
    const outputBuffer = await fs.readFile(outputPath)
    const stats = await fs.stat(outputPath)

    return new NextResponse(outputBuffer, {
      headers: {
        'Content-Type': `video/${outputFormat}`,
        'Content-Length': outputBuffer.length.toString(),
        'X-Original-Size': buffer.length.toString(),
        'X-Processed-Size': outputBuffer.length.toString(),
        'X-Compression-Ratio': ((1 - outputBuffer.length / buffer.length) * 100).toFixed(2),
        'Content-Disposition': `attachment; filename="edited_${file.name}"`
      }
    })

  } catch (error) {
    console.error('Video editing error:', error)
    return NextResponse.json(
      { error: 'Failed to process video', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  } finally {
    // Clean up temporary files
    try {
      if (inputPath) await fs.unlink(inputPath)
      if (outputPath) await fs.unlink(outputPath)
    } catch (cleanupError) {
      console.error('Cleanup error:', cleanupError)
    }
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Video editing API',
    supportedFormats: ['mp4', 'avi', 'mov', 'webm', 'mkv'],
    supportedOperations: [
      'trim', 'resize', 'crop', 'rotate', 'flip', 'volume', 'speed',
      'brightness', 'contrast', 'saturation', 'blur', 'watermark',
      'fadeIn', 'fadeOut'
    ]
  })
}
