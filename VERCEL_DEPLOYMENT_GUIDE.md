# Vercel 部署指南

## 🚨 重要注意事项

### 临时文件系统限制

在 Vercel 上部署时，需要注意以下限制：

1. **只有 `/tmp` 目录可写**
   - Vercel 的无服务器函数只允许写入 `/tmp` 目录
   - 其他目录都是只读的

2. **临时文件自动清理**
   - Vercel 会在函数执行完成后自动清理 `/tmp` 目录
   - 不需要手动清理，但建议主动清理以释放内存

3. **文件大小限制**
   - 单个函数的内存限制（默认 1GB）
   - 请求体大小限制（默认 4.5MB，Pro 计划 50MB）

## 🔧 当前实现的安全措施

### 1. 安全的文件清理
```typescript
// 使用 Promise.allSettled 避免清理失败导致的错误
await Promise.allSettled([
  fs.unlink(inputPath).catch(() => {}),
  fs.unlink(outputPath).catch(() => {}),
  fs.unlink(paletteFile).catch(() => {})
])
```

### 2. 临时文件管理器
- 创建了 `TempFileManager` 类来统一管理临时文件
- 提供了 `VercelTempUtils` 工具函数
- 自动检测 Vercel 环境并使用适当的临时目录

### 3. 错误处理优化
- 所有文件操作都有适当的错误处理
- 使用 `.catch(() => {})` 静默处理文件不存在的错误
- 避免因清理失败导致的 API 错误

## 📦 FFmpeg 依赖处理

### 当前状态
- 使用 `fluent-ffmpeg` 库进行视频/GIF 处理
- 在 Vercel 上需要确保 FFmpeg 二进制文件可用

### Vercel 上的 FFmpeg 解决方案

#### 选项 1: 使用 Vercel 的 FFmpeg Layer
```bash
# 在 Vercel 项目设置中添加
FFMPEG_PATH=/opt/bin/ffmpeg
FFPROBE_PATH=/opt/bin/ffprobe
```

#### 选项 2: 使用 @ffmpeg/ffmpeg (WebAssembly)
```typescript
// 替代方案：使用 WebAssembly 版本
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile } from '@ffmpeg/util'
```

#### 选项 3: 使用外部服务
- 将视频处理移到专门的服务器
- 使用 AWS Lambda 或其他支持 FFmpeg 的平台

## 🚀 部署步骤

### 1. 环境变量设置
确保在 Vercel 项目设置中配置以下环境变量：

```env
# 数据库
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 认证
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-domain.vercel.app

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_webhook_secret

# FFmpeg (如果使用)
FFMPEG_PATH=/opt/bin/ffmpeg
FFPROBE_PATH=/opt/bin/ffprobe
```

### 2. 构建配置
确保 `next.config.ts` 包含正确的 webpack 配置：

```typescript
webpack: (config, { isServer }) => {
  if (isServer) {
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      {
        module: /node_modules\/fluent-ffmpeg/,
        message: /Critical dependency: the request of a dependency is an expression/,
      },
    ];
  }
  return config;
}
```

### 3. 函数配置
在 `vercel.json` 中配置函数限制：

```json
{
  "functions": {
    "src/app/api/compress-video/route.ts": {
      "maxDuration": 30
    },
    "src/app/api/compress-gif/route.ts": {
      "maxDuration": 30
    }
  }
}
```

## ⚠️ 潜在风险和解决方案

### 1. 内存限制
**风险**: 大文件处理可能超出内存限制
**解决方案**:
- 限制上传文件大小
- 使用流式处理
- 考虑分块处理

### 2. 执行时间限制
**风险**: 复杂的视频处理可能超时
**解决方案**:
- 优化 FFmpeg 参数
- 使用更快的编解码器
- 考虑异步处理

### 3. 并发限制
**风险**: 多个用户同时处理可能导致资源竞争
**解决方案**:
- 实现队列系统
- 使用外部处理服务
- 限制并发数量

### 4. 临时文件冲突
**风险**: 多个请求可能创建同名文件
**解决方案**:
- 使用 UUID 生成唯一文件名 ✅ (已实现)
- 为每个请求创建独立的临时目录

## 🔍 监控和调试

### 1. 日志记录
```typescript
console.log('Processing file:', file.name)
console.log('Temp file created:', tempPath)
console.log('Processing completed in:', processingTime, 'ms')
```

### 2. 错误追踪
- 使用 Vercel Analytics
- 实现自定义错误报告
- 监控函数执行时间和内存使用

### 3. 性能优化
- 监控冷启动时间
- 优化依赖包大小
- 使用适当的 Node.js 版本

## 📋 部署检查清单

- [ ] 环境变量已配置
- [ ] FFmpeg 依赖已解决
- [ ] 文件大小限制已设置
- [ ] 错误处理已完善
- [ ] 临时文件清理已实现
- [ ] 函数超时时间已配置
- [ ] 监控和日志已设置
- [ ] 测试所有压缩功能
- [ ] 验证权限系统工作正常

## 🆘 故障排除

### 常见问题

1. **FFmpeg not found**
   - 检查 FFMPEG_PATH 环境变量
   - 考虑使用 WebAssembly 版本

2. **Function timeout**
   - 增加 maxDuration 设置
   - 优化处理算法

3. **Memory limit exceeded**
   - 减少并发处理
   - 优化内存使用

4. **File not found errors**
   - 检查临时文件路径
   - 确保清理逻辑正确
