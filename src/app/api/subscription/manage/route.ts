import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { orderOperations } from '@/lib/database'
import { createCustomerPortalSession, cancelSubscription } from '@/lib/stripe'
import { z } from 'zod'

const manageSubscriptionSchema = z.object({
  action: z.enum(['portal', 'cancel']),
  returnUrl: z.string().url().optional()
})

export async function POST(request: NextRequest) {
  try {
    const user = await auth.getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, returnUrl } = manageSubscriptionSchema.parse(body)

    // Get user's active subscription
    const orders = await orderOperations.getUserOrders(user.id)
    const activeOrder = orders.find(order =>
      order.status === 'completed' && order.stripe_subscription_id
    )

    if (!activeOrder || !activeOrder.stripe_subscription_id) {
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 404 }
      )
    }

    if (action === 'portal') {
      // Create customer portal session
      // Note: We need to get the customer ID from Stripe subscription
      const session = await createCustomerPortalSession(
        activeOrder.stripe_subscription_id, // This should be customer ID, we'll need to fix this
        returnUrl || `${request.nextUrl.origin}/dashboard`
      )

      return NextResponse.json({
        success: true,
        portalUrl: session.url
      })
    } else if (action === 'cancel') {
      // Cancel subscription
      await cancelSubscription(activeOrder.stripe_subscription_id)

      return NextResponse.json({
        success: true,
        message: 'Subscription cancelled successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Manage subscription API error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to manage subscription' },
      { status: 500 }
    )
  }
}