'use client'

import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, Image as ImageIcon, Video, FileImage, AlertCircle, CheckCircle } from 'lucide-react'
import { UploadAreaProps } from '@/types'
import { formatFileSize } from '@/lib/utils'

export function UploadArea({
  onFilesSelected,
  acceptedTypes,
  maxFiles = 10,
  maxFileSize = 100 * 1024 * 1024, // 100MB
  disabled = false
}: UploadAreaProps) {
  const [dragActive, setDragActive] = useState(false)
  const [uploadErrors, setUploadErrors] = useState<string[]>([])

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setUploadErrors([])
    
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors: string[] = []
      rejectedFiles.forEach(({ file, errors: fileErrors }) => {
        fileErrors.forEach((error: any) => {
          if (error.code === 'file-too-large') {
            errors.push(`${file.name}: File is too large (max ${formatFileSize(maxFileSize)})`)
          } else if (error.code === 'file-invalid-type') {
            errors.push(`${file.name}: File type not supported`)
          } else if (error.code === 'too-many-files') {
            errors.push(`Too many files selected (max ${maxFiles})`)
          } else {
            errors.push(`${file.name}: ${error.message}`)
          }
        })
      })
      setUploadErrors(errors)
    }

    // Handle accepted files
    if (acceptedFiles.length > 0) {
      onFilesSelected(acceptedFiles)
    }
  }, [onFilesSelected, maxFiles, maxFileSize])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    maxFiles,
    maxSize: maxFileSize,
    disabled,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    onDropAccepted: () => setDragActive(false),
    onDropRejected: () => setDragActive(false)
  })

  const getAcceptedFormats = () => {
    const formats = acceptedTypes.map(type => {
      if (type.startsWith('image/')) return 'Images'
      if (type.startsWith('video/')) return 'Videos'
      return type
    })
    return [...new Set(formats)].join(', ')
  }

  return (
    <div className="w-full">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-2xl p-8 md:p-12 text-center cursor-pointer transition-all duration-200
          ${disabled 
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
            : dragActive || isDragActive
              ? 'border-blue-500 bg-blue-50 scale-[1.02]' 
              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
          }
        `}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          {/* Icon */}
          <div className={`
            w-16 h-16 md:w-20 md:h-20 rounded-full flex items-center justify-center transition-all duration-200
            ${disabled 
              ? 'bg-gray-200' 
              : dragActive || isDragActive 
                ? 'bg-blue-100 scale-110' 
                : 'bg-gray-100'
            }
          `}>
            <Upload className={`
              w-8 h-8 md:w-10 md:h-10 transition-colors
              ${disabled 
                ? 'text-gray-400' 
                : dragActive || isDragActive 
                  ? 'text-blue-600' 
                  : 'text-gray-600'
              }
            `} />
          </div>

          {/* Text */}
          <div className="space-y-2">
            <h3 className={`
              text-xl md:text-2xl font-semibold transition-colors
              ${disabled 
                ? 'text-gray-400' 
                : dragActive || isDragActive 
                  ? 'text-blue-900' 
                  : 'text-gray-900'
              }
            `}>
              {disabled 
                ? 'Upload disabled' 
                : dragActive || isDragActive 
                  ? 'Drop files here' 
                  : 'Drag & drop files here'
              }
            </h3>
            
            {!disabled && (
              <>
                <p className="text-gray-600 text-base md:text-lg">
                  or click to select files from your device
                </p>
                
                <div className="text-sm text-gray-500 space-y-1">
                  <p>Supports: {getAcceptedFormats()}</p>
                  <p>Maximum {maxFiles} files, {formatFileSize(maxFileSize)} each</p>
                </div>
              </>
            )}
          </div>

          {/* Visual indicators */}
          {!disabled && (
            <div className="flex items-center justify-center space-x-6 pt-4">
              <div className="flex items-center space-x-2 text-blue-600">
                <ImageIcon className="w-5 h-5" />
                <span className="text-sm font-medium">Images</span>
              </div>
              <div className="flex items-center space-x-2 text-purple-600">
                <Video className="w-5 h-5" />
                <span className="text-sm font-medium">Videos</span>
              </div>
              <div className="flex items-center space-x-2 text-pink-600">
                <FileImage className="w-5 h-5" />
                <span className="text-sm font-medium">GIFs</span>
              </div>
            </div>
          )}
        </div>

        {/* Drag overlay */}
        {(dragActive || isDragActive) && !disabled && (
          <div className="absolute inset-0 bg-blue-500/10 rounded-2xl flex items-center justify-center">
            <div className="bg-white rounded-lg px-6 py-3 shadow-lg">
              <div className="flex items-center space-x-2 text-blue-600">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">Drop to upload</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Messages */}
      {uploadErrors.length > 0 && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-800 mb-1">
                Upload Errors
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {uploadErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500">
          All files are processed locally in your browser. Nothing is uploaded to our servers.
        </p>
      </div>
    </div>
  )
}

// Compact version for smaller spaces
export function CompactUploadArea({ onFilesSelected, acceptedTypes, disabled = false }: Omit<UploadAreaProps, 'maxFiles' | 'maxFileSize'>) {
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: onFilesSelected,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    disabled
  })

  return (
    <div
      {...getRootProps()}
      className={`
        border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
        ${disabled 
          ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
          : isDragActive
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
        }
      `}
    >
      <input {...getInputProps()} />
      <div className="flex flex-col items-center space-y-2">
        <Upload className={`w-6 h-6 ${isDragActive ? 'text-blue-600' : 'text-gray-600'}`} />
        <p className={`text-sm font-medium ${isDragActive ? 'text-blue-900' : 'text-gray-900'}`}>
          {isDragActive ? 'Drop files here' : 'Click or drag files here'}
        </p>
      </div>
    </div>
  )
}
