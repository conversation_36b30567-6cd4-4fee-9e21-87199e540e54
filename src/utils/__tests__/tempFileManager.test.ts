import { TempFileManager, VercelTempUtils } from '../tempFileManager'
import { promises as fs } from 'fs'
import path from 'path'

describe('TempFileManager', () => {
  let tempManager: TempFileManager
  const testTempDir = '/tmp/test-temp-manager'

  beforeEach(() => {
    tempManager = new TempFileManager(testTempDir)
  })

  afterEach(async () => {
    await tempManager.cleanup()
    // Clean up test directory
    try {
      await fs.rmdir(testTempDir, { recursive: true })
    } catch (error) {
      // Directory might not exist, ignore error
    }
  })

  describe('createTempPath', () => {
    it('should create unique file paths', () => {
      const path1 = tempManager.createTempPath('.txt', 'test')
      const path2 = tempManager.createTempPath('.txt', 'test')
      
      expect(path1).not.toBe(path2)
      expect(path1).toMatch(/test\.txt$/)
      expect(path2).toMatch(/test\.txt$/)
    })

    it('should handle empty extension and prefix', () => {
      const filePath = tempManager.createTempPath()
      expect(filePath).toMatch(/\/tmp\/test-temp-manager\/[a-f0-9-]+$/)
    })
  })

  describe('writeFile and readFile', () => {
    it('should write and read files correctly', async () => {
      const filePath = tempManager.createTempPath('.txt', 'test')
      const testData = Buffer.from('Hello, World!')

      await tempManager.writeFile(filePath, testData)
      const readData = await tempManager.readFile(filePath)

      expect(readData).toEqual(testData)
    })
  })

  describe('exists', () => {
    it('should correctly check file existence', async () => {
      const filePath = tempManager.createTempPath('.txt', 'test')
      
      expect(await tempManager.exists(filePath)).toBe(false)
      
      await tempManager.writeFile(filePath, Buffer.from('test'))
      expect(await tempManager.exists(filePath)).toBe(true)
    })
  })

  describe('cleanup', () => {
    it('should remove all created files', async () => {
      const filePath1 = tempManager.createTempPath('.txt', 'test1')
      const filePath2 = tempManager.createTempPath('.txt', 'test2')

      await tempManager.writeFile(filePath1, Buffer.from('test1'))
      await tempManager.writeFile(filePath2, Buffer.from('test2'))

      expect(await tempManager.exists(filePath1)).toBe(true)
      expect(await tempManager.exists(filePath2)).toBe(true)

      await tempManager.cleanup()

      expect(await tempManager.exists(filePath1)).toBe(false)
      expect(await tempManager.exists(filePath2)).toBe(false)
    })
  })
})

describe('VercelTempUtils', () => {
  describe('isVercelEnvironment', () => {
    it('should detect Vercel environment', () => {
      const originalVercel = process.env.VERCEL
      const originalVercelEnv = process.env.VERCEL_ENV

      // Test Vercel detection
      process.env.VERCEL = '1'
      expect(VercelTempUtils.isVercelEnvironment()).toBe(true)

      delete process.env.VERCEL
      process.env.VERCEL_ENV = 'production'
      expect(VercelTempUtils.isVercelEnvironment()).toBe(true)

      delete process.env.VERCEL_ENV
      expect(VercelTempUtils.isVercelEnvironment()).toBe(false)

      // Restore original values
      if (originalVercel) process.env.VERCEL = originalVercel
      if (originalVercelEnv) process.env.VERCEL_ENV = originalVercelEnv
    })
  })

  describe('getTempDir', () => {
    it('should return /tmp for Vercel environment', () => {
      const originalVercel = process.env.VERCEL
      
      process.env.VERCEL = '1'
      expect(VercelTempUtils.getTempDir()).toBe('/tmp')

      delete process.env.VERCEL
      // Should return system temp dir or /tmp
      const tempDir = VercelTempUtils.getTempDir()
      expect(typeof tempDir).toBe('string')
      expect(tempDir.length).toBeGreaterThan(0)

      // Restore original value
      if (originalVercel) process.env.VERCEL = originalVercel
    })
  })
})
