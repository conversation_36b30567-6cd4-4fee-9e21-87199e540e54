import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import Strip<PERSON> from 'stripe'
import { orderOperations, userOperations } from '@/lib/database'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia'
})

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = await headers()
    const signature = headersList.get('stripe-signature')!

    let event: Stripe.Event

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      console.error('Webhook signature verification failed:', err)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break
      
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice)
        break
      
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  const orderId = session.client_reference_id
  const userId = session.metadata?.user_id
  
  if (!orderId || !userId) {
    console.error('Missing order ID or user ID in checkout session')
    return
  }

  // Update order status
  await orderOperations.updateOrderStatus(orderId, 'completed', {
    stripe_payment_intent_id: session.payment_intent as string,
    stripe_subscription_id: session.subscription as string
  })

  // If this is a subscription, update user membership
  if (session.subscription) {
    const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
    await updateUserMembership(userId, subscription)
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const userId = subscription.metadata?.user_id
  
  if (!userId) {
    console.error('Missing user ID in subscription metadata')
    return
  }

  await updateUserMembership(userId, subscription)
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const userId = subscription.metadata?.user_id
  
  if (!userId) {
    console.error('Missing user ID in subscription metadata')
    return
  }

  // Update user to free plan
  await userOperations.updateUser(userId, {
    membership_status: 'cancelled',
    membership_level: 'free',
    membership_end_date: new Date().toISOString()
  })
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  const subscriptionId = invoice.subscription as string
  
  if (!subscriptionId) return

  const subscription = await stripe.subscriptions.retrieve(subscriptionId)
  const userId = subscription.metadata?.user_id
  
  if (!userId) return

  await updateUserMembership(userId, subscription)
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  const subscriptionId = invoice.subscription as string
  
  if (!subscriptionId) return

  const subscription = await stripe.subscriptions.retrieve(subscriptionId)
  const userId = subscription.metadata?.user_id
  
  if (!userId) return

  // Mark membership as expired but don't downgrade immediately
  // Give user a grace period to update payment method
  await userOperations.updateUser(userId, {
    membership_status: 'expired'
  })
}

async function updateUserMembership(userId: string, subscription: Stripe.Subscription) {
  const status = subscription.status
  const currentPeriodEnd = new Date(subscription.current_period_end * 1000)

  let membershipStatus: 'free' | 'trial' | 'active' | 'expired' | 'cancelled' = 'active'
  let membershipLevel: 'free' | 'pro' = 'pro'

  switch (status) {
    case 'trialing':
      membershipStatus = 'trial'
      membershipLevel = 'pro'
      break
    case 'active':
      membershipStatus = 'active'
      membershipLevel = 'pro'
      break
    case 'past_due':
    case 'unpaid':
      membershipStatus = 'expired'
      membershipLevel = 'pro' // Keep pro level during grace period
      break
    case 'canceled':
    case 'incomplete_expired':
      membershipStatus = 'cancelled'
      membershipLevel = 'free'
      break
    default:
      membershipStatus = 'expired'
      membershipLevel = 'free'
  }

  const updateData: any = {
    membership_status: membershipStatus,
    membership_level: membershipLevel,
    membership_start_date: subscription.created ? new Date(subscription.created * 1000).toISOString() : undefined,
    membership_end_date: currentPeriodEnd.toISOString(),
    stripe_subscription_id: subscription.id
  }

  // If this is a trial, also update trial fields
  if (status === 'trialing' && subscription.trial_end) {
    updateData.trial_start_date = subscription.trial_start ? new Date(subscription.trial_start * 1000).toISOString() : undefined
    updateData.trial_end_date = new Date(subscription.trial_end * 1000).toISOString()
    updateData.trial_used = true
  }

  await userOperations.updateUser(userId, updateData)
}
