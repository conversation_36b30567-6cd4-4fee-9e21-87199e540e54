import { NextRequest, NextResponse } from 'next/server'
import ffmpeg from 'fluent-ffmpeg'
import { v4 as uuidv4 } from 'uuid'
import { auth, permissions } from '@/lib/auth'
import { usageOperations, compressionLogOperations, userOperations } from '@/lib/database'
import { createTempManager } from '@/utils/tempFileManager'

// 设置FFmpeg路径（如果需要）
// ffmpeg.setFfmpegPath('/usr/local/bin/ffmpeg')

export async function POST(request: NextRequest) {
  const tempManager = createTempManager()

  try {
    // Get current user (optional for free users)
    const user = await auth.getCurrentUser()

    const formData = await request.formData()
    const file = formData.get('file') as File
    const options = JSON.parse(formData.get('options') as string || '{}')

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // 验证文件类型
    if (!file.type.includes('video')) {
      return NextResponse.json({ error: 'File must be a video' }, { status: 400 })
    }

    const fileSizeMb = file.size / (1024 * 1024)

    // Check permissions if user is logged in
    if (user) {
      // Debug user status
      console.log('🔍 Video Compression - User Status:', {
        email: user.email,
        membership_level: user.membership_level,
        membership_status: user.membership_status,
        is_membership_active: user.is_membership_active,
        membership_end_date: user.membership_end_date,
        points: user.points
      })

      const canCompress = permissions.canCompress(user, fileSizeMb)
      if (!canCompress.allowed) {
        console.log('🚫 Compression denied:', canCompress.reason)
        return NextResponse.json(
          { error: canCompress.reason },
          { status: 403 }
        )
      }

      const canUseFormat = permissions.canUseAdvancedFormats(user, file.type)
      if (!canUseFormat.allowed) {
        console.log('🚫 Advanced format denied:', canUseFormat.reason)
        return NextResponse.json(
          { error: canUseFormat.reason },
          { status: 403 }
        )
      }

      console.log('✅ All permissions passed for video compression')
    } else {
      // For anonymous users, video compression is not allowed
      return NextResponse.json(
        { error: 'Video compression requires Pro membership. Please sign in and upgrade.' },
        { status: 403 }
      )
    }

    // 创建临时文件使用 TempFileManager
    const fileId = uuidv4()
    const inputExt = file.name.split('.').pop() || 'mp4'
    const outputExt = options.outputFormat || 'mp4'
    const inputPath = tempManager.createTempPath(`.${inputExt}`, `${fileId}_input`)
    const outputPath = tempManager.createTempPath(`.${outputExt}`, `${fileId}_output`)

    // 保存上传的文件
    const buffer = Buffer.from(await file.arrayBuffer())
    await tempManager.writeFile(inputPath, buffer)

    // 获取原始文件大小
    const originalSize = buffer.length
    const startTime = Date.now()

    try {
      // 执行FFmpeg压缩 - 根据输出格式选择不同的处理方式
      if (options.outputFormat === 'gif') {
        // GIF转换的特殊处理
        console.log('Converting video to GIF')

        const fps = (options as any).fps || 10
        const scale = (options as any).gifScale || 'auto'

        // 创建调色板文件路径
        const paletteFile = tempManager.createTempPath('.png', 'palette')

        // 第一步：生成调色板
        console.log('Generating palette for GIF conversion')
        await new Promise<void>((resolve, reject) => {
          let paletteCommand = ffmpeg(inputPath)

          // 设置帧率和尺寸
          paletteCommand = paletteCommand.fps(fps)

          if (scale !== 'auto') {
            paletteCommand = paletteCommand.size(`${scale}x?`)
          } else if (options.maxWidth || options.maxHeight) {
            const width = options.maxWidth || -1
            const height = options.maxHeight || -1
            paletteCommand = paletteCommand.size(`${width}x${height}`)
          }

          paletteCommand
            .outputOptions(['-vf', 'palettegen'])
            .output(paletteFile)
            .on('end', () => {
              console.log('Palette generation finished')
              resolve()
            })
            .on('error', (err) => {
              console.error('Palette generation failed:', err)
              reject(err)
            })
            .run()
        })

        // 第二步：使用调色板生成GIF
        console.log('Generating GIF with palette')
        await new Promise<void>((resolve, reject) => {
          let command = ffmpeg()
            .input(inputPath)
            .input(paletteFile)
            .fps(fps)

          if (scale !== 'auto') {
            command = command.size(`${scale}x?`)
          } else if (options.maxWidth || options.maxHeight) {
            const width = options.maxWidth || -1
            const height = options.maxHeight || -1
            command = command.size(`${width}x${height}`)
          }

          command
            .outputOptions([
              '-filter_complex', `fps=${fps}[x];[x][1:v]paletteuse=dither=bayer:bayer_scale=3:diff_mode=rectangle`,
              '-y',
              '-loop', '0',
              '-map_metadata', '-1'
            ])
            .output(outputPath)
            .on('end', () => {
              console.log('GIF conversion finished')
              resolve()
            })
            .on('error', (err) => {
              console.error('GIF conversion failed:', err)
              reject(err)
            })
            .run()
        })

      } else {
        // 常规视频压缩
        await new Promise<void>((resolve, reject) => {
          let command = ffmpeg(inputPath)

          // 视频编解码器设置 - 使用更兼容的编解码器
          let codec = 'libx264'

          // 如果用户指定了编解码器，先尝试用户选择
          if ((options as any).codec && (options as any).codec !== 'h264') {
            codec = (options as any).codec
          }

          console.log(`Attempting to use video codec: ${codec}`)
          command = command.videoCodec(codec)

          // 音频编解码器 - 使用最兼容的设置
          command = command.audioCodec('aac').audioBitrate('128k')

          // 质量控制 - 使用CRF (Constant Rate Factor)
          let crf = 23 // 默认值，平衡质量和大小
          if (options.quality) {
            // quality: 0.1-1.0 -> CRF: 51-18 (数值越小质量越好)
            crf = Math.round(51 - (options.quality * 33))
            crf = Math.max(18, Math.min(51, crf)) // 限制在18-51范围内
          }
          command = command.outputOptions(['-crf', crf.toString()])

          // 预设 - 平衡编码速度和压缩效率
          command = command.outputOptions(['-preset', 'medium'])

          // 尺寸控制
          if (options.maxWidth || options.maxHeight) {
            const width = options.maxWidth || -1
            const height = options.maxHeight || -1
            command = command.size(`${width}x${height}`)
          }

          // 帧率控制
          if ((options as any).fps) {
            command = command.fps((options as any).fps)
          }

          // 比特率控制（可选，CRF优先）
          if ((options as any).bitrate) {
            command = command.videoBitrate((options as any).bitrate)
          }

          // 移除元数据
          if (options.removeMetadata) {
            command = command.outputOptions(['-map_metadata', '-1'])
          }

          // 输出文件
          command = command.output(outputPath)

          // 事件处理
          command
            .on('progress', (progress) => {
              console.log(`Processing: ${progress.percent}% done`)
            })
            .on('end', () => {
              console.log('Video compression finished')
              resolve()
            })
            .on('error', (err) => {
              console.error('FFmpeg video processing failed:', err)
              reject(err)
            })
            .run()
        })
      }

      // 读取压缩后的文件
      const compressedBuffer = await tempManager.readFile(outputPath)
      const compressedSize = compressedBuffer.length
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100
      const processingTime = Date.now() - startTime
      const compressedSizeMb = compressedSize / (1024 * 1024)

      // Initialize points variable
      let updatedPoints = user?.points || 0

      // Log compression and update usage if user is logged in
      if (user) {
        // Log the compression
        await compressionLogOperations.logCompression({
          user_id: user.id,
          file_name: file.name,
          file_type: file.type,
          original_size_mb: fileSizeMb,
          compressed_size_mb: compressedSizeMb,
          compression_ratio: compressionRatio,
          processing_time_ms: processingTime,
          ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
          user_agent: request.headers.get('user-agent') || undefined
        })

        // Update usage
        await usageOperations.incrementUserUsage(user.id, 'compression', fileSizeMb)

        // Deduct points for free users (though video compression requires Pro)
        if (user.membership_level === 'free' && user.points > 0) {
          updatedPoints = Math.max(0, user.points - 1)
          await userOperations.updateUser(user.id, {
            points: updatedPoints
          })
        }
      }

      // 清理临时文件 - 使用 TempFileManager
      await tempManager.cleanup()

      // 返回压缩结果
      const contentType = outputExt === 'gif' ? 'image/gif' : `video/${outputExt}`
      const headers: Record<string, string> = {
        'Content-Type': contentType,
        'Content-Length': compressedSize.toString(),
        'X-Original-Size': originalSize.toString(),
        'X-Compressed-Size': compressedSize.toString(),
        'X-Compression-Ratio': compressionRatio.toFixed(2),
        'X-Processing-Time': processingTime.toString(),
      }

      // Add user points info if user is authenticated
      if (user) {
        headers['X-User-Points'] = updatedPoints.toString()
        headers['X-User-Membership'] = user.membership_level
      }

      return new NextResponse(compressedBuffer, {
        status: 200,
        headers,
      })

    } catch (ffmpegError) {
      // 清理临时文件 - 使用 TempFileManager
      await tempManager.cleanup()

      console.error('FFmpeg processing failed:', ffmpegError)

      // 提供更详细的错误信息
      let errorMessage = 'Video processing failed'
      let errorDetails = ffmpegError

      if (ffmpegError instanceof Error) {
        if (ffmpegError.message.includes('codec') || ffmpegError.message.includes('h264') || ffmpegError.message.includes('libx264')) {
          errorMessage = 'FFmpeg codec not available'
          errorDetails = 'The server does not have the required video codecs installed. This is a server configuration issue. Please try using the client-side compression instead.'
        } else if (ffmpegError.message.includes('Invalid data found')) {
          errorMessage = 'Invalid video file'
          errorDetails = 'The uploaded file appears to be corrupted or in an unsupported format.'
        } else if (ffmpegError.message.includes('Permission denied')) {
          errorMessage = 'Server permission error'
          errorDetails = 'Server does not have permission to process the file. Please try again later.'
        } else if (ffmpegError.message.includes('No such file')) {
          errorMessage = 'File processing error'
          errorDetails = 'Temporary file handling failed. Please try again.'
        } else {
          errorDetails = ffmpegError.message
        }
      }

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          details: errorDetails,
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    )
  } finally {
    // 确保清理临时文件
    await tempManager.cleanup()
  }
}
