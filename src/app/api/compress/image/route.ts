import { NextRequest, NextResponse } from 'next/server'
import { auth, permissions } from '@/lib/auth'
import { usageOperations, compressionLogOperations, userOperations } from '@/lib/database'
import sharp from 'sharp'

export async function POST(request: NextRequest) {
  try {
    // Get current user (optional for free users)
    const user = await auth.getCurrentUser()
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    const quality = parseFloat(formData.get('quality') as string) || 0.8
    const format = formData.get('format') as string || 'original'
    const removeMetadata = formData.get('removeMetadata') === 'true'
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Only image files are supported' },
        { status: 400 }
      )
    }

    const fileSizeMb = file.size / (1024 * 1024)
    
    // Check permissions if user is logged in
    if (user) {
      const canCompress = permissions.canCompress(user, fileSizeMb)
      if (!canCompress.allowed) {
        return NextResponse.json(
          { error: canCompress.reason },
          { status: 403 }
        )
      }

      const canUseFormat = permissions.canUseAdvancedFormats(user, file.type)
      if (!canUseFormat.allowed) {
        return NextResponse.json(
          { error: canUseFormat.reason },
          { status: 403 }
        )
      }
    } else {
      // For anonymous users, apply basic limits
      if (fileSizeMb > 10) {
        return NextResponse.json(
          { error: 'File size exceeds 10MB limit. Please sign in for higher limits.' },
          { status: 403 }
        )
      }
    }

    const startTime = Date.now()
    
    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())
    
    // Process image with Sharp
    let sharpInstance = sharp(buffer)
    
    // Remove metadata if requested
    if (removeMetadata) {
      sharpInstance = sharpInstance.withMetadata({})
    }
    
    // Apply format conversion and quality
    let outputBuffer: Buffer
    let outputFormat = format === 'original' ? file.type.split('/')[1] : format

    // Normalize format names
    if (outputFormat === 'jpg') outputFormat = 'jpeg'

    switch (outputFormat) {
      case 'jpeg':
        outputBuffer = await sharpInstance
          .jpeg({
            quality: Math.round(quality * 100),
            progressive: true,
            mozjpeg: true // Use mozjpeg encoder for better compression
          })
          .toBuffer()
        break
      case 'png':
        outputBuffer = await sharpInstance
          .png({
            quality: Math.round(quality * 100),
            compressionLevel: 9, // Maximum compression
            progressive: true
          })
          .toBuffer()
        break
      case 'webp':
        outputBuffer = await sharpInstance
          .webp({
            quality: Math.round(quality * 100),
            effort: 6, // Higher effort for better compression
            lossless: quality >= 0.95 // Use lossless for very high quality
          })
          .toBuffer()
        break
      case 'avif':
        outputBuffer = await sharpInstance
          .avif({
            quality: Math.round(quality * 100),
            effort: 9, // Maximum effort for best compression
            lossless: quality >= 0.95 // Use lossless for very high quality
          })
          .toBuffer()
        break
      default:
        outputBuffer = await sharpInstance
          .jpeg({
            quality: Math.round(quality * 100),
            progressive: true,
            mozjpeg: true
          })
          .toBuffer()
        outputFormat = 'jpeg'
    }
    
    const processingTime = Date.now() - startTime
    const compressedSizeMb = outputBuffer.length / (1024 * 1024)
    const compressionRatio = ((fileSizeMb - compressedSizeMb) / fileSizeMb) * 100
    
    // Log compression and update usage if user is logged in
    if (user) {
      // Log the compression
      await compressionLogOperations.logCompression({
        user_id: user.id,
        file_name: file.name,
        file_type: file.type,
        original_size_mb: fileSizeMb,
        compressed_size_mb: compressedSizeMb,
        compression_ratio: compressionRatio,
        processing_time_ms: processingTime,
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        user_agent: request.headers.get('user-agent') || undefined
      })
      
      // Update usage
      await usageOperations.incrementUserUsage(user.id, 'compression', fileSizeMb)
      
      // Deduct points for free users
      let updatedPoints = user.points
      if (user.membership_level === 'free' && user.points > 0) {
        updatedPoints = Math.max(0, user.points - 1)
        await userOperations.updateUser(user.id, {
          points: updatedPoints
        })
      }
    } else {
      // Log anonymous compression
      await compressionLogOperations.logCompression({
        file_name: file.name,
        file_type: file.type,
        original_size_mb: fileSizeMb,
        compressed_size_mb: compressedSizeMb,
        compression_ratio: compressionRatio,
        processing_time_ms: processingTime,
        ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        user_agent: request.headers.get('user-agent') || undefined
      })
    }
    
    // Return compressed image
    const response = new NextResponse(outputBuffer)
    response.headers.set('Content-Type', `image/${outputFormat}`)
    response.headers.set('Content-Disposition', `attachment; filename="compressed_${file.name}"`)
    response.headers.set('X-Original-Size', fileSizeMb.toString())
    response.headers.set('X-Compressed-Size', compressedSizeMb.toString())
    response.headers.set('X-Compression-Ratio', compressionRatio.toString())
    response.headers.set('X-Processing-Time', processingTime.toString())

    // Add user points info if user is authenticated
    if (user) {
      response.headers.set('X-User-Points', updatedPoints.toString())
      response.headers.set('X-User-Membership', user.membership_level)
    }
    
    return response
  } catch (error) {
    console.error('Image compression error:', error)
    return NextResponse.json(
      { error: 'Failed to compress image' },
      { status: 500 }
    )
  }
}
