import { NextResponse } from 'next/server'
import { membershipOperations } from '@/lib/database'

export async function GET() {
  try {
    const plans = await membershipOperations.getAllMembershipLevels()
    
    return NextResponse.json({
      success: true,
      plans
    })
  } catch (error) {
    console.error('Get subscription plans error:', error)
    
    return NextResponse.json(
      { error: 'Failed to fetch subscription plans' },
      { status: 500 }
    )
  }
}
