import { NextRequest, NextResponse } from 'next/server'
import { Google } from 'arctic'
import { generateCodeVerifier, generateState } from 'arctic'

const CALLBACK_URL = process.env.NEXT_PUBLIC_GOOGLE_CALLBACK_URL || 'http://localhost:3000/api/oauth/google/callback'

export async function GET(request: NextRequest) {
  try {
    const googleAuth = new Google(
      process.env.GOOGLE_CLIENT_ID!,
      process.env.GOOGLE_CLIENT_SECRET!,
      CALLBACK_URL
    )

    const state = generateState()
    const codeVerifier = generateCodeVerifier()
    
    const url = googleAuth.createAuthorizationURL(state, codeVerifier, [
      'openid',
      'profile',
      'email'
    ])

    // Store state and code verifier in cookies for verification
    const response = NextResponse.redirect(url)
    response.cookies.set('google_oauth_state', state, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 10, // 10 minutes
      path: '/'
    })
    response.cookies.set('google_oauth_code_verifier', codeVerifier, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 10, // 10 minutes
      path: '/'
    })

    return response
  } catch (error) {
    console.error('Google OAuth initiation error:', error)
    return NextResponse.json(
      { error: 'Failed to initiate Google OAuth' },
      { status: 500 }
    )
  }
}
