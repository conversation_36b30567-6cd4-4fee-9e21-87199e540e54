import { supabase, supabaseAdmin } from './supabase'
import { User, MembershipLevel, Order, UserUsage, CompressionLog } from '@/types/database'

// User operations
export const userOperations = {
  async getUserByEmail(email: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .maybeSingle() // Use maybeSingle() instead of single() to handle no results gracefully

    if (error) {
      console.error('Error fetching user:', error)
      return null
    }

    return data
  },

  async createUser(userData: Partial<User>): Promise<User | null> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert(userData)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating user:', error)
      return null
    }
    
    return data
  },

  async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating user:', error)
      return null
    }
    
    return data
  },

  async getUserWithMembershipStatus(email: string) {
    const user = await this.getUserByEmail(email)
    if (!user) return null

    const now = new Date()
    const endDate = user.membership_end_date ? new Date(user.membership_end_date) : null
    const isMembershipExpired = endDate ? now > endDate : true

    return {
      ...user,
      is_membership_active: !isMembershipExpired &&
        (user.membership_status === 'active' || user.membership_status === 'trial'),
      membership_days_left: endDate
        ? Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)))
        : 0,
    }
  },

  async getUserById(userId: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .maybeSingle() // Use maybeSingle() instead of single()

    if (error) {
      console.error('Error fetching user by ID:', error)
      return null
    }

    return data
  },

  async updateUserMembership(userId: string, membershipData: {
    membership_level: string
    membership_status: string
    membership_start_date?: string
    membership_end_date?: string
    points?: number
  }): Promise<User | null> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .update({
        ...membershipData,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating user membership:', error)
      return null
    }

    return data
  },

  async decrementUserPoints(userId: string, amount: number = 1): Promise<boolean> {
    // First get current points
    const user = await this.getUserById(userId)
    if (!user || user.points < amount) {
      return false
    }

    const { error } = await supabaseAdmin
      .from('users')
      .update({
        points: user.points - amount,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)

    if (error) {
      console.error('Error decrementing user points:', error)
      return false
    }

    return true
  }
}

// Membership operations
export const membershipOperations = {
  async getAllMembershipLevels(): Promise<MembershipLevel[]> {
    const { data, error } = await supabase
      .from('membership_levels')
      .select('*')
      .eq('is_active', true)
      .order('price', { ascending: true })
    
    if (error) {
      console.error('Error fetching membership levels:', error)
      return []
    }
    
    return data || []
  },

  async getMembershipLevelById(id: number): Promise<MembershipLevel | null> {
    const { data, error } = await supabase
      .from('membership_levels')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      console.error('Error fetching membership level:', error)
      return null
    }
    
    return data
  }
}

// Order operations
export const orderOperations = {
  async createOrder(orderData: Partial<Order>): Promise<Order | null> {
    const { data, error } = await supabaseAdmin
      .from('orders')
      .insert(orderData)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating order:', error)
      return null
    }
    
    return data
  },

  async updateOrderStatus(orderId: string, status: Order['status'], updates?: Partial<Order>): Promise<Order | null> {
    const { data, error } = await supabaseAdmin
      .from('orders')
      .update({ status, ...updates })
      .eq('id', orderId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating order:', error)
      return null
    }
    
    return data
  },

  async getUserOrders(userId: string): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching user orders:', error)
      return []
    }

    return data || []
  },

  async getOrderByNumber(orderNumber: string): Promise<Order | null> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('order_number', orderNumber)
      .single()

    if (error) {
      console.error('Error fetching order by number:', error)
      return null
    }

    return data
  },

  async getOrderById(orderId: string): Promise<Order | null> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('id', orderId)
      .single()

    if (error) {
      console.error('Error fetching order by ID:', error)
      return null
    }

    return data
  },

  async generateOrderNumber(isMobile: boolean = false): Promise<string> {
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000)
    const platform = isMobile ? 'M' : 'P'
    return `ORD${timestamp}${random}-${platform}`
  }
}

// Usage tracking operations
export const usageOperations = {
  async getUserUsageToday(userId: string): Promise<UserUsage | null> {
    const today = new Date().toISOString().split('T')[0]
    
    const { data, error } = await supabase
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('date', today)
      .single()
    
    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error fetching user usage:', error)
      return null
    }
    
    return data
  },

  async incrementUserUsage(userId: string, type: 'compression' | 'api_call', fileSizeMb?: number): Promise<void> {
    const today = new Date().toISOString().split('T')[0]
    
    // First, try to get existing usage record
    const existingUsage = await this.getUserUsageToday(userId)
    
    if (existingUsage) {
      // Update existing record
      const updates: Partial<UserUsage> = {}
      if (type === 'compression') {
        updates.compressions_count = existingUsage.compressions_count + 1
        if (fileSizeMb) {
          updates.total_file_size_mb = existingUsage.total_file_size_mb + fileSizeMb
        }
      } else if (type === 'api_call') {
        updates.api_calls_count = existingUsage.api_calls_count + 1
      }
      
      await supabaseAdmin
        .from('user_usage')
        .update(updates)
        .eq('id', existingUsage.id)
    } else {
      // Create new record
      const newUsage: Partial<UserUsage> = {
        user_id: userId,
        date: today,
        compressions_count: type === 'compression' ? 1 : 0,
        api_calls_count: type === 'api_call' ? 1 : 0,
        total_file_size_mb: fileSizeMb || 0
      }
      
      await supabaseAdmin
        .from('user_usage')
        .insert(newUsage)
    }
  }
}

// Compression logging operations
export const compressionLogOperations = {
  async logCompression(logData: Partial<CompressionLog>): Promise<void> {
    const { error } = await supabaseAdmin
      .from('compression_logs')
      .insert(logData)
    
    if (error) {
      console.error('Error logging compression:', error)
    }
  },

  async getUserCompressionStats(userId: string, days: number = 30) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)
    
    const { data, error } = await supabase
      .from('compression_logs')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error fetching compression stats:', error)
      return []
    }
    
    return data || []
  },

  async getUserMonthlyUsage(userId: string, year: number, month: number): Promise<UserUsage[]> {
    const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]
    const endDate = new Date(year, month, 0).toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('user_usage')
      .select('*')
      .eq('user_id', userId)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: true })

    if (error) {
      console.error('Error fetching monthly usage:', error)
      return []
    }

    return data || []
  },

  async getTotalUserUsage(userId: string): Promise<{
    total_compressions: number
    total_api_calls: number
    total_file_size_mb: number
  }> {
    const { data, error } = await supabase
      .from('user_usage')
      .select('compressions_count, api_calls_count, total_file_size_mb')
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching total usage:', error)
      return { total_compressions: 0, total_api_calls: 0, total_file_size_mb: 0 }
    }

    const totals = data.reduce((acc, usage) => ({
      total_compressions: acc.total_compressions + usage.compressions_count,
      total_api_calls: acc.total_api_calls + usage.api_calls_count,
      total_file_size_mb: acc.total_file_size_mb + usage.total_file_size_mb
    }), { total_compressions: 0, total_api_calls: 0, total_file_size_mb: 0 })

    return totals
  }
}

// Admin and statistics operations
export const adminOperations = {
  async getAllUsers(limit: number = 50, offset: number = 0): Promise<User[]> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching all users:', error)
      return []
    }

    return data || []
  },

  async getUserStats(): Promise<{
    total_users: number
    active_pro_users: number
    free_users: number
    expired_users: number
  }> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('membership_level, membership_status')

    if (error) {
      console.error('Error fetching user stats:', error)
      return { total_users: 0, active_pro_users: 0, free_users: 0, expired_users: 0 }
    }

    const stats = data.reduce((acc, user) => {
      acc.total_users++
      if (user.membership_level === 'free') {
        acc.free_users++
      } else if (user.membership_level === 'pro' && user.membership_status === 'active') {
        acc.active_pro_users++
      } else if (user.membership_status === 'expired') {
        acc.expired_users++
      }
      return acc
    }, { total_users: 0, active_pro_users: 0, free_users: 0, expired_users: 0 })

    return stats
  },

  async getRevenueStats(days: number = 30): Promise<{
    total_revenue: number
    orders_count: number
    average_order_value: number
  }> {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const { data, error } = await supabaseAdmin
      .from('orders')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', startDate.toISOString())

    if (error) {
      console.error('Error fetching revenue stats:', error)
      return { total_revenue: 0, orders_count: 0, average_order_value: 0 }
    }

    const total_revenue = data.reduce((sum, order) => sum + order.amount, 0)
    const orders_count = data.length
    const average_order_value = orders_count > 0 ? total_revenue / orders_count : 0

    return { total_revenue, orders_count, average_order_value }
  },

  async getCompressionStats(days: number = 30): Promise<{
    total_compressions: number
    total_file_size_mb: number
    average_compression_ratio: number
  }> {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    const { data, error } = await supabaseAdmin
      .from('compression_logs')
      .select('original_size_mb, compressed_size_mb, compression_ratio')
      .gte('created_at', startDate.toISOString())

    if (error) {
      console.error('Error fetching compression stats:', error)
      return { total_compressions: 0, total_file_size_mb: 0, average_compression_ratio: 0 }
    }

    const total_compressions = data.length
    const total_file_size_mb = data.reduce((sum, log) => sum + log.original_size_mb, 0)
    const average_compression_ratio = data.length > 0
      ? data.reduce((sum, log) => sum + log.compression_ratio, 0) / data.length
      : 0

    return { total_compressions, total_file_size_mb, average_compression_ratio }
  }
}
