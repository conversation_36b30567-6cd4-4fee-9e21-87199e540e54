'use client'

import { QueueManagerProps } from '@/types'
import { Play, Pause, Square, RotateCcw, Download, Trash2, Clock, CheckCircle, AlertCircle } from 'lucide-react'
import { formatFileSize } from '@/lib/utils'

export function QueueManager({
  queue,
  onPause,
  onResume,
  onCancel,
  onCancelItem,
  onRetryItem
}: QueueManagerProps) {
  const { items, isProcessing, currentIndex, totalProgress } = queue

  const stats = {
    total: items.length,
    pending: items.filter(item => item.status === 'pending').length,
    processing: items.filter(item => item.status === 'processing').length,
    completed: items.filter(item => item.status === 'completed').length,
    failed: items.filter(item => item.status === 'error').length,
    cancelled: items.filter(item => item.status === 'cancelled').length
  }

  const totalOriginalSize = items.reduce((sum, item) => sum + item.originalSize, 0)
  const totalCompressedSize = items
    .filter(item => item.compressedSize)
    .reduce((sum, item) => sum + (item.compressedSize || 0), 0)
  
  const overallSavings = totalOriginalSize > 0 
    ? ((totalOriginalSize - totalCompressedSize) / totalOriginalSize * 100).toFixed(1)
    : '0'

  if (items.length === 0) {
    return null
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Processing Queue</h3>
          <p className="text-sm text-gray-600">
            {stats.completed} of {stats.total} files completed
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {isProcessing ? (
            <button
              onClick={onPause}
              className="inline-flex items-center px-3 py-2 bg-yellow-600 text-white font-medium rounded-lg hover:bg-yellow-700 transition-colors"
            >
              <Pause className="w-4 h-4 mr-2" />
              Pause
            </button>
          ) : stats.pending > 0 ? (
            <button
              onClick={onResume}
              className="inline-flex items-center px-3 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Play className="w-4 h-4 mr-2" />
              Resume
            </button>
          ) : null}
          
          {(isProcessing || stats.pending > 0) && (
            <button
              onClick={onCancel}
              className="inline-flex items-center px-3 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 transition-colors"
            >
              <Square className="w-4 h-4 mr-2" />
              Cancel All
            </button>
          )}
        </div>
      </div>

      {/* Overall Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Overall Progress</span>
          <span className="text-sm text-gray-600">{Math.round(totalProgress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300"
            style={{ width: `${totalProgress}%` }}
          />
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <Clock className="w-4 h-4 text-yellow-500 mr-1" />
            <span className="text-lg font-bold text-yellow-600">{stats.pending}</span>
          </div>
          <div className="text-xs text-gray-600">Pending</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-1" />
            <span className="text-lg font-bold text-blue-600">{stats.processing}</span>
          </div>
          <div className="text-xs text-gray-600">Processing</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-lg font-bold text-green-600">{stats.completed}</span>
          </div>
          <div className="text-xs text-gray-600">Completed</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <AlertCircle className="w-4 h-4 text-red-500 mr-1" />
            <span className="text-lg font-bold text-red-600">{stats.failed}</span>
          </div>
          <div className="text-xs text-gray-600">Failed</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <Download className="w-4 h-4 text-purple-500 mr-1" />
            <span className="text-lg font-bold text-purple-600">{overallSavings}%</span>
          </div>
          <div className="text-xs text-gray-600">Saved</div>
        </div>
      </div>

      {/* Size Summary */}
      {stats.completed > 0 && (
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-sm text-gray-600">Original Size</div>
              <div className="text-lg font-semibold text-gray-900">
                {formatFileSize(totalOriginalSize)}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Compressed Size</div>
              <div className="text-lg font-semibold text-gray-900">
                {formatFileSize(totalCompressedSize)}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Space Saved</div>
              <div className="text-lg font-semibold text-green-600">
                {formatFileSize(totalOriginalSize - totalCompressedSize)} ({overallSavings}%)
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Current Processing Item */}
      {isProcessing && currentIndex >= 0 && currentIndex < items.length && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-blue-900">Currently Processing</div>
              <div className="text-blue-700">{items[currentIndex].name}</div>
            </div>
            <div className="text-right">
              <div className="text-sm text-blue-600">
                {Math.round(items[currentIndex].progress)}%
              </div>
              <div className="w-24 bg-blue-200 rounded-full h-2 mt-1">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${items[currentIndex].progress}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-2">
        {stats.failed > 0 && (
          <button
            onClick={() => {
              items
                .filter(item => item.status === 'error')
                .forEach(item => onRetryItem(item.id))
            }}
            className="inline-flex items-center px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Retry Failed ({stats.failed})
          </button>
        )}
        
        {stats.completed > 0 && (
          <button
            className="inline-flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
          >
            <Download className="w-3 h-3 mr-1" />
            Download All ({stats.completed})
          </button>
        )}
        
        <button
          onClick={() => {
            items.forEach(item => {
              if (item.status === 'pending' || item.status === 'processing') {
                onCancelItem(item.id)
              }
            })
          }}
          className="inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
        >
          <Trash2 className="w-3 h-3 mr-1" />
          Clear Queue
        </button>
      </div>
    </div>
  )
}
