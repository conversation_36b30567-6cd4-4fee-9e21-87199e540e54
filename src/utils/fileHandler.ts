// File handling utilities for compress pages

export interface UploadedFileData {
  name: string
  size: number
  type: string
  data: string // base64 data
  lastModified: number
}

// Get uploaded file data from sessionStorage
export function getUploadedFile(): UploadedFileData | null {
  if (typeof window === 'undefined') return null
  
  try {
    const fileDataStr = sessionStorage.getItem('uploadedFile')
    if (!fileDataStr) return null
    
    const fileData = JSON.parse(fileDataStr) as UploadedFileData
    return fileData
  } catch (error) {
    console.error('Failed to get uploaded file:', error)
    return null
  }
}

// Clear uploaded file data from sessionStorage
export function clearUploadedFile(): void {
  if (typeof window === 'undefined') return
  
  try {
    sessionStorage.removeItem('uploadedFile')
  } catch (error) {
    console.error('Failed to clear uploaded file:', error)
  }
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Get file type category
export function getFileCategory(type: string): 'image' | 'video' | 'gif' | 'unknown' {
  const lowerType = type.toLowerCase()
  
  if (lowerType.includes('gif')) {
    return 'gif'
  } else if (lowerType.startsWith('video/')) {
    return 'video'
  } else if (lowerType.startsWith('image/')) {
    return 'image'
  }
  
  return 'unknown'
}

// Check if file type is supported
export function isSupportedFileType(type: string): boolean {
  const supportedTypes = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/avif', 'image/gif',
    'video/mp4', 'video/webm', 'video/avi', 'video/mov'
  ]

  return supportedTypes.includes(type.toLowerCase())
}

// Convert base64 data to File object
export function base64ToFile(base64Data: string, fileName: string, fileType: string, lastModified: number): File {
  // Remove data URL prefix if present
  const base64String = base64Data.split(',')[1] || base64Data

  // Convert base64 to binary
  const binaryString = atob(base64String)
  const bytes = new Uint8Array(binaryString.length)

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }

  // Create File object
  return new File([bytes], fileName, {
    type: fileType,
    lastModified: lastModified
  })
}
