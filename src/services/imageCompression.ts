import imageCompression from 'browser-image-compression'
import { CompressionResult, ImageCompressionOptions } from '@/types'
import { CompressionErrorHandler } from '@/utils/errorHandler'

// Check if browser supports AVIF format
function supportsAVIF(): Promise<boolean> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1

    canvas.toBlob((blob) => {
      resolve(blob !== null)
    }, 'image/avif', 0.5)
  })
}

export class ImageCompressionService {
  static async compressImage(
    file: File,
    options: ImageCompressionOptions,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    try {
      console.log('Starting image compression with options:', options)
      onProgress?.(5)

      // Try API compression first (server-side with Sharp)
      const apiResult = await this.compressWithAPI(file, options, (progress) => {
        onProgress?.(progress * 0.8) // 0-80% range for API compression
      })

      if (apiResult.success) {
        console.log('API compression successful')
        onProgress?.(100)
        return apiResult
      }

      console.log('API compression failed, falling back to client-side compression:', apiResult.error)
      onProgress?.(10)

      // Determine output format
      let outputFormat = options.outputFormat || file.type.replace('image/', '')

      // Handle special cases
      if (outputFormat === 'jpg') outputFormat = 'jpeg'

      // Check AVIF support if needed
      if (outputFormat === 'avif') {
        const avifSupported = await supportsAVIF()
        if (!avifSupported) {
          console.warn('AVIF not supported in this browser, falling back to WebP')
          outputFormat = 'webp'
        }
      }

      console.log('Target output format:', outputFormat)

      // If format conversion is needed or modern formats are requested, use Canvas API
      const needsFormatConversion = outputFormat !== file.type.replace('image/', '') ||
                                   outputFormat === 'webp' ||
                                   outputFormat === 'avif'

      if (needsFormatConversion) {
        console.log('Using Canvas API for format conversion')
        return await this.compressWithCanvas(file, options, outputFormat, (progress) => {
          onProgress?.(10 + progress * 0.9) // 10-100% range
        })
      }

      // Use browser-image-compression for same-format compression
      const compressionOptions = {
        maxSizeMB: 10,
        maxWidthOrHeight: Math.max(options.maxWidth || 1920, options.maxHeight || 1080),
        useWebWorker: true,
        fileType: file.type,
        initialQuality: options.quality,
        alwaysKeepResolution: !options.maintainAspectRatio,
        preserveExif: !options.removeMetadata,
        onProgress: (progress: number) => {
          onProgress?.(10 + progress * 0.9) // 10-100% range
        }
      }

      console.log('Using browser-image-compression with options:', compressionOptions)

      const compressedFile = await imageCompression(file, compressionOptions)
      const compressionRatio = ((file.size - compressedFile.size) / file.size) * 100

      onProgress?.(100)

      return {
        success: true,
        originalSize: file.size,
        compressedSize: compressedFile.size,
        compressionRatio,
        blob: compressedFile
      }
    } catch (error) {
      console.error('Image compression failed:', error)
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  private static async compressWithAPI(
    file: File,
    options: ImageCompressionOptions,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    try {
      onProgress?.(10)

      const formData = new FormData()
      formData.append('file', file)
      formData.append('quality', options.quality.toString())
      formData.append('format', options.outputFormat || 'original')
      formData.append('removeMetadata', options.removeMetadata ? 'true' : 'false')

      onProgress?.(30)

      const response = await fetch('/api/compress/image', {
        method: 'POST',
        body: formData,
      })

      onProgress?.(70)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown API error' }))
        throw new Error(errorData.error || `API error: ${response.status}`)
      }

      const blob = await response.blob()
      const originalSize = file.size
      const compressedSize = blob.size
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100

      onProgress?.(100)

      // Extract user points from response headers for real-time updates
      const userPoints = response.headers.get('X-User-Points')
      const userMembership = response.headers.get('X-User-Membership')

      return {
        success: true,
        originalSize,
        compressedSize,
        compressionRatio,
        blob,
        userPoints: userPoints ? parseInt(userPoints) : undefined,
        userMembership: userMembership || undefined
      }
    } catch (error) {
      return CompressionErrorHandler.createErrorResult(
        error,
        file.size,
        'image',
        { showToast: false, logError: true }
      )
    }
  }

  private static async compressWithCanvas(
    file: File,
    options: ImageCompressionOptions,
    outputFormat: string,
    onProgress?: (progress: number) => void
  ): Promise<CompressionResult> {
    return new Promise((resolve) => {
      const img = new Image()

      img.onload = () => {
        try {
          onProgress?.(60)

          // Calculate dimensions
          let { width, height } = img

          if (options.maxWidth && width > options.maxWidth) {
            height = (height * options.maxWidth) / width
            width = options.maxWidth
          }

          if (options.maxHeight && height > options.maxHeight) {
            width = (width * options.maxHeight) / height
            height = options.maxHeight
          }

          onProgress?.(70)

          // Create canvas
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          if (!ctx) {
            resolve({
              success: false,
              originalSize: file.size,
              compressedSize: 0,
              compressionRatio: 0,
              error: 'Could not get canvas context'
            })
            return
          }

          canvas.width = width
          canvas.height = height

          // Handle transparency for PNG/WebP/AVIF
          if (outputFormat === 'png' || outputFormat === 'webp' || outputFormat === 'avif') {
            ctx.clearRect(0, 0, canvas.width, canvas.height)
          } else {
            // Fill with white background for JPEG
            ctx.fillStyle = '#FFFFFF'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
          }

          onProgress?.(80)

          // Draw image
          ctx.drawImage(img, 0, 0, width, height)

          onProgress?.(90)

          // For AVIF, use a more aggressive quality setting to ensure compression
          let qualityToUse = options.quality
          if (outputFormat === 'avif') {
            // AVIF is very efficient, so we can use lower quality values
            qualityToUse = Math.min(options.quality, 0.6) // Cap at 60% for AVIF
          }

          // Convert to blob with specified format and quality
          canvas.toBlob(
            (blob) => {
              if (blob) {
                // Check if compression actually reduced file size
                if (blob.size >= file.size && outputFormat !== 'avif') {
                  // If file size didn't reduce, try with lower quality
                  const lowerQuality = Math.max(0.1, qualityToUse * 0.7)
                  canvas.toBlob(
                    (retryBlob) => {
                      if (retryBlob && retryBlob.size < file.size) {
                        const compressionRatio = ((file.size - retryBlob.size) / file.size) * 100
                        onProgress?.(100)
                        resolve({
                          success: true,
                          originalSize: file.size,
                          compressedSize: retryBlob.size,
                          compressionRatio,
                          blob: retryBlob
                        })
                      } else {
                        // If still no compression, return original result
                        const compressionRatio = ((file.size - blob.size) / file.size) * 100
                        onProgress?.(100)
                        resolve({
                          success: true,
                          originalSize: file.size,
                          compressedSize: blob.size,
                          compressionRatio,
                          blob
                        })
                      }
                    },
                    `image/${outputFormat}`,
                    lowerQuality
                  )
                } else {
                  const compressionRatio = ((file.size - blob.size) / file.size) * 100
                  onProgress?.(100)
                  resolve({
                    success: true,
                    originalSize: file.size,
                    compressedSize: blob.size,
                    compressionRatio,
                    blob
                  })
                }
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to create blob'
                })
              }
            },
            `image/${outputFormat}`,
            qualityToUse
          )
        } catch (error) {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: error instanceof Error ? error.message : 'Canvas compression failed'
          })
        }
      }

      img.onerror = () => {
        resolve({
          success: false,
          originalSize: file.size,
          compressedSize: 0,
          compressionRatio: 0,
          error: 'Failed to load image'
        })
      }

      img.src = URL.createObjectURL(file)
    })
  }

  static async convertFormat(
    file: File,
    targetFormat: string,
    quality: number = 0.8
  ): Promise<CompressionResult> {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          canvas.width = img.width
          canvas.height = img.height
          
          if (ctx) {
            // Handle transparency for PNG/WebP/AVIF
            if (targetFormat === 'png' || targetFormat === 'webp' || targetFormat === 'avif') {
              ctx.clearRect(0, 0, canvas.width, canvas.height)
            } else {
              // Fill with white background for JPEG
              ctx.fillStyle = '#FFFFFF'
              ctx.fillRect(0, 0, canvas.width, canvas.height)
            }
            
            ctx.drawImage(img, 0, 0)
            
            canvas.toBlob((blob) => {
              if (blob) {
                const compressionRatio = ((file.size - blob.size) / file.size) * 100
                resolve({
                  success: true,
                  originalSize: file.size,
                  compressedSize: blob.size,
                  compressionRatio,
                  blob
                })
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to convert image format'
                })
              }
            }, `image/${targetFormat}`, quality)
          }
        }

        img.onerror = () => {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: 'Failed to load image'
          })
        }

        img.src = URL.createObjectURL(file)
      })
    } catch (error) {
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  static async resizeImage(
    file: File,
    maxWidth: number,
    maxHeight: number,
    maintainAspectRatio: boolean = true
  ): Promise<CompressionResult> {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      return new Promise((resolve) => {
        img.onload = () => {
          let { width, height } = img

          if (maintainAspectRatio) {
            const aspectRatio = width / height
            if (width > maxWidth) {
              width = maxWidth
              height = width / aspectRatio
            }
            if (height > maxHeight) {
              height = maxHeight
              width = height * aspectRatio
            }
          } else {
            width = Math.min(width, maxWidth)
            height = Math.min(height, maxHeight)
          }

          canvas.width = width
          canvas.height = height

          if (ctx) {
            ctx.drawImage(img, 0, 0, width, height)
            
            canvas.toBlob((blob) => {
              if (blob) {
                const compressionRatio = ((file.size - blob.size) / file.size) * 100
                resolve({
                  success: true,
                  originalSize: file.size,
                  compressedSize: blob.size,
                  compressionRatio,
                  blob
                })
              } else {
                resolve({
                  success: false,
                  originalSize: file.size,
                  compressedSize: 0,
                  compressionRatio: 0,
                  error: 'Failed to resize image'
                })
              }
            }, file.type, 0.9)
          }
        }

        img.onerror = () => {
          resolve({
            success: false,
            originalSize: file.size,
            compressedSize: 0,
            compressionRatio: 0,
            error: 'Failed to load image'
          })
        }

        img.src = URL.createObjectURL(file)
      })
    } catch (error) {
      return {
        success: false,
        originalSize: file.size,
        compressedSize: 0,
        compressionRatio: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }
}
