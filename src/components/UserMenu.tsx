"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  User,
  Settings,
  LogOut,
  Crown,
  ChevronDown,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useUserStatus } from "@/hooks/useUserStatus";
import { AuthModal } from "./AuthModal";
import Link from "next/link";
import Image from "next/image";
import toast from "react-hot-toast";

// Avatar component with fallback
function UserAvatar({
  user,
  size = 32,
}: {
  user: { avatar_url?: string; username?: string; email: string };
  size?: number;
}) {
  const [imageError, setImageError] = useState(false);

  // Use fixed Tailwind classes instead of dynamic ones
  const sizeClasses =
    size === 32
      ? "w-8 h-8"
      : size === 40
      ? "w-10 h-10"
      : "w-12 h-12";

  if (user.avatar_url && !imageError) {
    return (
      <Image
        src={user.avatar_url}
        alt={user.username || user.email}
        width={size}
        height={size}
        className={`${sizeClasses} rounded-full object-cover border border-gray-200`}
        onError={() => setImageError(true)}
        unoptimized // For external URLs like Google avatars
      />
    );
  }

  return (
    <div className={`${sizeClasses} bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm`}>
      {user.username?.[0]?.toUpperCase() || user.email[0].toUpperCase()}
    </div>
  );
}

export function UserMenu() {
  const { loading, signOut } = useAuth();
  const {
    user,
    displayPoints,
    isProUser,
    isTrialUser,
    membershipDaysLeft,
    isMembershipActive
  } = useUserStatus();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle click outside and ESC key to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscKey);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        document.removeEventListener("keydown", handleEscKey);
      };
    }
  }, [isDropdownOpen]);

  const handleSignOut = async () => {
    try {
      await signOut();
      toast.success("Signed out successfully");
      setIsDropdownOpen(false);
    } catch {
      toast.error("Failed to sign out");
    }
  };

  if (loading) {
    return <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />;
  }

  if (!user) {
    return (
      <>
        <button
          onClick={() => setIsAuthModalOpen(true)}
          className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
        >
          Sign In
        </button>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
        />
      </>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* User Avatar/Button */}
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      >
        <UserAvatar user={user} size={32} />
        <div className="hidden md:block text-left">
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
            {user.username || user.email.split("@")[0]}
            {isProUser && (
              <Crown className="w-4 h-4 text-yellow-500 dark:text-yellow-400 ml-1" />
            )}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {isProUser ? (isTrialUser ? "Pro Trial" : "Pro Member") : "Free Plan"}
          </div>
        </div>
        <ChevronDown className="w-4 h-4 text-gray-400 dark:text-gray-500" />
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isDropdownOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-[100]"
          >
            {/* User Info */}
            <div className="px-4 py-3 border-b border-gray-100 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <UserAvatar user={user} size={40} />
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {user.username || user.email.split("@")[0]}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                </div>
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center mt-2">
                {isProUser ? (
                  <>
                    <Crown className="w-4 h-4 text-yellow-500 dark:text-yellow-400 mr-1" />
                    {isTrialUser ? "Pro Trial" : "Pro Member"}
                    {isMembershipActive ? (
                      <span className="ml-2 text-green-600 dark:text-green-400">
                        ({membershipDaysLeft} days left)
                      </span>
                    ) : (
                      <span className="ml-2 text-red-600 dark:text-red-400">(Expired)</span>
                    )}
                  </>
                ) : (
                  <>Free Plan ({displayPoints} credits left)</>
                )}
              </div>
            </div>

            {/* Menu Items */}
            <div className="py-2">
              <Link
                href="/profile"
                className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                onClick={() => setIsDropdownOpen(false)}
              >
                <User className="w-4 h-4 mr-3" />
                Profile
              </Link>

              {/* <Link
                href="/settings"
                className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                onClick={() => setIsDropdownOpen(false)}
              >
                <Settings className="w-4 h-4 mr-3" />
                Settings
              </Link> */}
            </div>

            {/* Upgrade CTA for free users */}


            {/* Sign Out */}
            <div className="border-t border-gray-100 dark:border-gray-700 pt-2">
              <button
                onClick={handleSignOut}
                className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
              >
                <LogOut className="w-4 h-4 mr-3" />
                Sign Out
              </button>
            </div>

            {!isProUser && (
              <div className="px-4 py-3 border-t border-gray-100 dark:border-gray-700">
                <Link
                  href="/pricing"
                  className="block w-full bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg text-sm font-medium transition-all duration-200"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Upgrade to Pro
                </Link>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
