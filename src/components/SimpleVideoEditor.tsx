'use client'

import { useState, useRef, useEffect } from 'react'
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Volume2,
  VolumeX,
  Save
} from 'lucide-react'
import toast from 'react-hot-toast'

interface SimpleVideoEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

export function SimpleVideoEditor({ file, onEditComplete }: SimpleVideoEditorProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)

  useEffect(() => {
    console.log('SimpleVideoEditor: Loading file', file.name)
    
    // Use a timeout to ensure the video ref is available after render
    const timer = setTimeout(() => {
      if (!videoRef.current) {
        console.log('Video ref still not available after timeout')
        setIsLoading(false)
        toast.error('Failed to initialize video player')
        return
      }
      
      initializeVideo()
    }, 100)

    return () => clearTimeout(timer)
  }, [file])

  const initializeVideo = () => {
    if (!videoRef.current) {
      console.log('Video ref not available in initializeVideo')
      return
    }

    const video = videoRef.current
    const url = URL.createObjectURL(file)
    console.log('Created video URL:', url)

    const handleLoadedMetadata = () => {
      try {
        console.log('Video metadata loaded, duration:', video.duration)
        if (video.duration && !isNaN(video.duration)) {
          setDuration(video.duration)
          setEndTime(video.duration)
          setIsLoading(false)
          console.log('Video editor ready')
        } else {
          throw new Error('Invalid video duration')
        }
      } catch (error) {
        console.error('Error loading video metadata:', error)
        setIsLoading(false)
        toast.error('Failed to load video metadata')
      }
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    
    const handleError = () => {
      console.error('Video loading error')
      setIsLoading(false)
      toast.error('Failed to load video file')
    }

    const handleCanPlay = () => {
      console.log('Video can play')
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('error', handleError)
    video.addEventListener('canplay', handleCanPlay)

    video.src = url
    video.load()

    // Cleanup function
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('error', handleError)
      video.removeEventListener('canplay', handleCanPlay)
      URL.revokeObjectURL(url)
    }
  }

  const togglePlay = () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }

  const seekTo = (time: number) => {
    if (!videoRef.current) return
    videoRef.current.currentTime = time
    setCurrentTime(time)
  }

  const handleVolumeChange = (newVolume: number) => {
    if (!videoRef.current) return
    
    setVolume(newVolume)
    videoRef.current.volume = newVolume
    
    if (newVolume === 0) {
      setIsMuted(true)
    } else {
      setIsMuted(false)
    }
  }

  const toggleMute = () => {
    if (!videoRef.current) return
    
    if (isMuted) {
      videoRef.current.volume = volume
      setIsMuted(false)
    } else {
      videoRef.current.volume = 0
      setIsMuted(true)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const handleExport = async () => {
    if (!videoRef.current) return
    
    try {
      // For now, we'll just return the original file
      // In a real implementation, you would process the video based on trim settings
      const response = await fetch(videoRef.current.src)
      const blob = await response.blob()
      
      console.log('Video exported successfully')
      onEditComplete(blob)
      toast.success('Video exported successfully!')
      
    } catch (error) {
      console.error('Export failed:', error)
      toast.error('Failed to export video')
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <span className="ml-4 text-gray-600 dark:text-gray-400">Loading video...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* Video Player */}
      <div className="relative bg-black">
        <video
          ref={videoRef}
          className="w-full h-auto max-h-96 object-contain"
          onClick={togglePlay}
        />
        
        {/* Play/Pause Overlay */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <button
            onClick={togglePlay}
            className="w-16 h-16 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors pointer-events-auto"
          >
            {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8 ml-1" />}
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        {/* Timeline */}
        <div className="mb-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600 dark:text-gray-400 w-12">
              {formatTime(currentTime)}
            </span>
            <div className="flex-1 relative">
              <input
                type="range"
                min={0}
                max={duration}
                value={currentTime}
                onChange={(e) => seekTo(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              {/* Trim markers */}
              <div
                className="absolute top-0 h-2 bg-blue-500 rounded-lg pointer-events-none"
                style={{
                  left: `${(startTime / duration) * 100}%`,
                  width: `${((endTime - startTime) / duration) * 100}%`
                }}
              />
            </div>
            <span className="text-sm text-gray-600 dark:text-gray-400 w-12">
              {formatTime(duration)}
            </span>
          </div>
        </div>

        {/* Player Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => seekTo(Math.max(0, currentTime - 10))}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <SkipBack className="w-4 h-4" />
            </button>
            <button
              onClick={togglePlay}
              className="p-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </button>
            <button
              onClick={() => seekTo(Math.min(duration, currentTime + 10))}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <SkipForward className="w-4 h-4" />
            </button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleMute}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </button>
            <input
              type="range"
              min={0}
              max={1}
              step={0.1}
              value={isMuted ? 0 : volume}
              onChange={(e) => handleVolumeChange(Number(e.target.value))}
              className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>

          {/* Export Button */}
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <Save className="w-4 h-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Trim Controls */}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Trim Video</h3>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Start Time: {formatTime(startTime)}
            </label>
            <input
              type="range"
              min={0}
              max={duration}
              value={startTime}
              onChange={(e) => {
                const value = Number(e.target.value)
                setStartTime(Math.min(value, endTime - 1))
              }}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              End Time: {formatTime(endTime)}
            </label>
            <input
              type="range"
              min={0}
              max={duration}
              value={endTime}
              onChange={(e) => {
                const value = Number(e.target.value)
                setEndTime(Math.max(value, startTime + 1))
              }}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>
        </div>
        
        <div className="flex space-x-2 mt-4">
          <button
            onClick={() => seekTo(startTime)}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Go to Start
          </button>
          <button
            onClick={() => seekTo(endTime)}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Go to End
          </button>
        </div>
      </div>
    </div>
  )
}
