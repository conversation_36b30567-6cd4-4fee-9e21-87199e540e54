/**
 * Debug utilities for user status and permissions
 */

import { AuthUser } from '@/lib/auth'

export function debugUserStatus(user: AuthUser | null) {
  if (!user) {
    console.log('🔍 User Debug: No user logged in')
    return
  }

  const now = new Date()
  const endDate = user.membership_end_date ? new Date(user.membership_end_date) : null
  const isMembershipExpired = endDate ? now > endDate : true
  const is_membership_active = !isMembershipExpired && 
    (user.membership_status === 'active' || user.membership_status === 'trial')

  console.log('🔍 User Debug Status:', {
    email: user.email,
    membership_level: user.membership_level,
    membership_status: user.membership_status,
    membership_start_date: user.membership_start_date,
    membership_end_date: user.membership_end_date,
    trial_used: user.trial_used,
    trial_start_date: user.trial_start_date,
    trial_end_date: user.trial_end_date,
    points: user.points,
    // Calculated values
    now: now.toISOString(),
    endDate: endDate?.toISOString(),
    isMembershipExpired,
    is_membership_active,
    // From user object
    user_is_membership_active: user.is_membership_active,
    user_membership_days_left: user.membership_days_left
  })
}

export function debugPermissions(user: AuthUser | null, fileType: string, fileSizeMb: number) {
  if (!user) {
    console.log('🔍 Permission Debug: No user logged in')
    return
  }

  console.log('🔍 Permission Debug:', {
    fileType,
    fileSizeMb,
    user: {
      membership_level: user.membership_level,
      membership_status: user.membership_status,
      is_membership_active: user.is_membership_active,
      points: user.points
    }
  })

  // Test compression permission
  const maxFileSize = user.membership_level === 'pro' ? 200 : 10
  const fileSizeOk = fileSizeMb <= maxFileSize
  const hasPoints = user.membership_level === 'free' ? user.points > 0 : true
  const membershipActive = user.membership_level === 'pro' ? user.is_membership_active : true

  console.log('🔍 Compression Check:', {
    maxFileSize,
    fileSizeOk,
    hasPoints,
    membershipActive,
    canCompress: fileSizeOk && hasPoints && membershipActive
  })

  // Test advanced format permission
  const advancedFormats = ['gif', 'video', 'mp4', 'avi', 'mov', 'webm']
  const isAdvancedFormat = advancedFormats.some(format => 
    fileType.toLowerCase().includes(format)
  )
  const hasProLevel = user.membership_level === 'pro'
  const proMembershipActive = user.membership_level === 'pro' ? user.is_membership_active : true

  console.log('🔍 Advanced Format Check:', {
    isAdvancedFormat,
    hasProLevel,
    proMembershipActive,
    canUseAdvancedFormat: !isAdvancedFormat || (hasProLevel && proMembershipActive)
  })
}

// Add to window for browser console access
if (typeof window !== 'undefined') {
  (window as any).debugUser = {
    debugUserStatus,
    debugPermissions
  }
}