export interface FileItem {
  id: string
  file: File
  name: string
  size: number
  type: 'image' | 'video' | 'gif'
  format: string
  preview?: string
  status: 'pending' | 'processing' | 'completed' | 'error' | 'cancelled'
  progress: number
  originalSize: number
  compressedSize?: number
  compressionRatio?: number
  error?: string
  downloadUrl?: string
  compressedFile?: Blob
}

export interface CompressionOptions {
  quality: number // 0-1 for images, 0-51 for videos (lower is better quality)
  maxWidth?: number
  maxHeight?: number
  outputFormat?: string
  maintainAspectRatio: boolean
  removeMetadata: boolean
  frameRate?: number
  colors?: number
  dithering?: boolean
}

export interface VideoCompressionOptions extends CompressionOptions {
  bitrate?: string
  fps?: number
  codec?: 'h264' | 'h265' | 'vp8' | 'vp9'
  audioCodec?: 'aac' | 'mp3' | 'opus'
  audioBitrate?: string
  // Video to GIF specific options
  gifScale?: string | number
}

export interface ImageCompressionOptions extends CompressionOptions {
  preserveTransparency: boolean
  progressive?: boolean
}

export interface GifCompressionOptions extends CompressionOptions {
  frameRate?: number
  colors?: number
  dithering?: boolean
}

export interface ProcessingQueue {
  items: FileItem[]
  isProcessing: boolean
  currentIndex: number
  totalProgress: number
  canPause: boolean
  canCancel: boolean
}

export interface CompressionResult {
  success: boolean
  originalSize: number
  compressedSize: number
  compressionRatio: number
  blob?: Blob
  error?: string
  userPoints?: number // Updated user points after compression
  userMembership?: string // User membership level
}

export interface UploadAreaProps {
  onFilesSelected: (files: File[]) => void
  acceptedTypes: string[]
  maxFiles?: number
  maxFileSize?: number
  disabled?: boolean
}

export interface ProgressBarProps {
  progress: number
  status: FileItem['status']
  showPercentage?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export interface FileCardProps {
  item: FileItem
  onRemove: (id: string) => void
  onRetry: (id: string) => void
  onDownload: (id: string) => void
  onPreview: (id: string) => void
  onReprocess?: (id: string) => void
}

export interface CompressionSettingsProps {
  type: 'image' | 'video' | 'gif'
  options: CompressionOptions
  onChange: (options: CompressionOptions) => void
  files?: FileItem[] // Add files to get original dimensions
}

export interface QueueManagerProps {
  queue: ProcessingQueue
  onPause: () => void
  onResume: () => void
  onCancel: () => void
  onCancelItem: (id: string) => void
  onRetryItem: (id: string) => void
}

// Supported formats
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif',
  'image/bmp',
  'image/tiff'
] as const

export const SUPPORTED_VIDEO_FORMATS = [
  'video/mp4',
  'video/avi',
  'video/mov',
  'video/wmv',
  'video/flv',
  'video/webm',
  'video/mkv',
  'video/3gp'
] as const

export const OUTPUT_IMAGE_FORMATS = [
  { value: 'jpeg', label: 'JPEG' },
  { value: 'png', label: 'PNG' },
  { value: 'webp', label: 'WebP' }
] as const

export const OUTPUT_VIDEO_FORMATS = [
  { value: 'mp4', label: 'MP4' },
  { value: 'webm', label: 'WebM' },
  { value: 'avi', label: 'AVI' }
] as const

export const VIDEO_CODECS = [
  { value: 'h264', label: 'H.264' },
  { value: 'h265', label: 'H.265' },
  { value: 'vp8', label: 'VP8' },
  { value: 'vp9', label: 'VP9' }
] as const

export const AUDIO_CODECS = [
  { value: 'aac', label: 'AAC' },
  { value: 'mp3', label: 'MP3' },
  { value: 'opus', label: 'Opus' }
] as const

// Google One Tap types
declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          initialize: (config: any) => void
          prompt: (callback?: (notification: any) => void) => void
          cancel: () => void
          disableAutoSelect: () => void
        }
      }
    }
  }
}
