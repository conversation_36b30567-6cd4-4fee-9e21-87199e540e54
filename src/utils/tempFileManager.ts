import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

/**
 * 临时文件管理器 - 专为 Vercel 等无服务器环境优化
 */
export class TempFileManager {
  private tempDir: string
  private createdFiles: Set<string> = new Set()

  constructor(tempDir: string = '/tmp') {
    // 在 Vercel 上，只有 /tmp 目录是可写的
    this.tempDir = tempDir
  }

  /**
   * 创建临时文件路径
   */
  createTempPath(extension: string = '', prefix: string = ''): string {
    const fileId = uuidv4()
    const fileName = prefix ? `${prefix}${extension}` : `${fileId}${extension}`
    const filePath = path.join(this.tempDir, fileName)
    this.createdFiles.add(filePath)
    return filePath
  }

  /**
   * 写入临时文件
   */
  async writeFile(filePath: string, data: Buffer | Uint8Array): Promise<void> {
    try {
      await fs.writeFile(filePath, data)
      this.createdFiles.add(filePath)
    } catch (error) {
      console.error('Failed to write temp file:', error)
      throw error
    }
  }

  /**
   * 读取临时文件
   */
  async readFile(filePath: string): Promise<Buffer> {
    try {
      return await fs.readFile(filePath)
    } catch (error) {
      console.error('Failed to read temp file:', error)
      throw error
    }
  }

  /**
   * 安全删除单个文件
   */
  async deleteFile(filePath: string): Promise<boolean> {
    try {
      await fs.unlink(filePath)
      this.createdFiles.delete(filePath)
      return true
    } catch (error) {
      // 文件不存在或其他错误，静默处理
      this.createdFiles.delete(filePath)
      return false
    }
  }

  /**
   * 清理所有创建的临时文件
   */
  async cleanup(): Promise<void> {
    const deletePromises = Array.from(this.createdFiles).map(filePath =>
      this.deleteFile(filePath)
    )
    
    await Promise.allSettled(deletePromises)
    this.createdFiles.clear()
  }

  /**
   * 检查文件是否存在
   */
  async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取文件大小
   */
  async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath)
      return stats.size
    } catch (error) {
      console.error('Failed to get file size:', error)
      return 0
    }
  }

  /**
   * 创建目录（如果不存在）
   */
  async ensureDir(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true })
    } catch (error) {
      // 目录可能已存在，忽略错误
    }
  }
}

/**
 * 全局临时文件管理器实例
 */
export const globalTempManager = new TempFileManager()

/**
 * 用于 API 路由的临时文件管理器工厂
 */
export function createTempManager(): TempFileManager {
  return new TempFileManager()
}

/**
 * Vercel 兼容的临时文件操作
 */
export const VercelTempUtils = {
  /**
   * 在 Vercel 上创建临时文件
   */
  async createTempFile(data: Buffer, extension: string = ''): Promise<string> {
    const tempPath = path.join('/tmp', `${uuidv4()}${extension}`)
    await fs.writeFile(tempPath, data)
    return tempPath
  },

  /**
   * 安全删除文件（Vercel 兼容）
   */
  async safeDelete(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath)
    } catch (error) {
      // 在 Vercel 上，文件可能已经被自动清理，忽略错误
    }
  },

  /**
   * 批量安全删除文件
   */
  async safeDeleteMultiple(filePaths: string[]): Promise<void> {
    await Promise.allSettled(
      filePaths.map(filePath => this.safeDelete(filePath))
    )
  },

  /**
   * 检查是否在 Vercel 环境中
   */
  isVercelEnvironment(): boolean {
    return process.env.VERCEL === '1' || process.env.VERCEL_ENV !== undefined
  },

  /**
   * 获取适合的临时目录
   */
  getTempDir(): string {
    if (this.isVercelEnvironment()) {
      return '/tmp'
    }
    return process.env.TEMP || process.env.TMP || '/tmp'
  }
}

/**
 * 自动清理装饰器 - 用于 API 路由
 */
export function withTempFileCleanup<T extends any[], R>(
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const tempManager = createTempManager()
    try {
      return await fn(...args)
    } finally {
      await tempManager.cleanup()
    }
  }
}
