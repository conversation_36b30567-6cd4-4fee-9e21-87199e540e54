'use client'

import { useEffect, useState, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import {
  initializeGoogleOneTapOnce,
  getGoogleOneTapConfig,
  cancelGoogleOneTap,
  resetGoogleOneTapState
} from '@/utils/google-one-tap'
import toast from 'react-hot-toast'

interface GoogleOneTapProviderProps {
  children: React.ReactNode
}

interface GoogleOneTapResponse {
  credential: string
  select_by?: string
}

export function GoogleOneTapProvider({ children }: GoogleOneTapProviderProps) {
  const [isClient, setIsClient] = useState(false)
  const { user, loading, refreshUser } = useAuth()

  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle One Tap login success
  const handleOneTapSuccess = useCallback(async (response: GoogleOneTapResponse) => {
    
    console.log('Google One Tap response:', response)

    try {
      // Call backend API to verify JWT token
      const verifyResponse = await fetch('/api/oauth/google/one-tap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          credential: response.credential,
        }),
      })

      const result = await verifyResponse.json()

      if (result.success) {
        console.log('Google One Tap login successful:', result.user)
        toast.success('Successfully signed in with Google!')
        
        // Cancel One Tap immediately and reset state
        resetGoogleOneTapState()

        // Refresh user data
        await refreshUser()
      } else {
        console.error('Google One Tap verification failed:', result.error)
        toast.error('Google sign in failed')
      }
    } catch (error) {
      console.error('Error during Google One Tap verification:', error)
      toast.error('Google sign in failed')
    }
  }, [refreshUser])

  // Effect to cancel One Tap when user becomes authenticated
  useEffect(() => {
    if (user) {
      console.log('User authenticated, resetting Google One Tap state')
      resetGoogleOneTapState()
    }
  }, [user])

  // Effect to handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && user) {
        // Cancel One Tap when page becomes visible and user is logged in
        cancelGoogleOneTap()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [user])

  useEffect(() => {
    if (!isClient || typeof window === 'undefined') return

    // Wait for auth loading to complete
    if (loading) {
      console.log('Auth still loading, waiting...')
      return
    }

    // Don't show One Tap if user is already logged in
    if (user) {
      console.log('User already logged in, skipping Google One Tap:', user.email)
      cancelGoogleOneTap()
      return
    }

    // Get configuration
    const config = getGoogleOneTapConfig()
    if (!config) {
      console.log('Google One Tap config not available')
      return
    }

    console.log('Initializing Google One Tap for unauthenticated user')

    // Use IIFE (Immediately Invoked Function Expression) to handle async operation
    ;(async () => {
      try {
        await initializeGoogleOneTapOnce(config, handleOneTapSuccess)
      } catch (error) {
        console.error('GoogleOneTapProvider: Initialization failed:', error)
      }
    })()
  }, [isClient, user, loading, handleOneTapSuccess])

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Reset One Tap state when component unmounts
      resetGoogleOneTapState()
    }
  }, [])

  return <>{children}</>
}
