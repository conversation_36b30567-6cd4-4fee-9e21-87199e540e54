'use client'

import { useState, useEffect } from 'react'
import { CompressionSettingsProps, CompressionOptions } from '@/types'
import { Settings, Image as ImageIcon, Video, Sparkles } from 'lucide-react'

export function CompressionSettings({ type, options, onChange, files }: CompressionSettingsProps) {
  const [isExpanded, setIsExpanded] = useState(true) // Default to expanded for better visibility
  const [originalDimensions, setOriginalDimensions] = useState<{width: number, height: number} | null>(null)

  // Get original dimensions from the first image file
  useEffect(() => {
    if (files && files.length > 0 && (type === 'image' || type === 'gif')) {
      const firstImageFile = files.find(f => f.type === 'image' || f.type === 'gif')
      if (firstImageFile) {
        const img = new Image()
        img.onload = () => {
          setOriginalDimensions({ width: img.naturalWidth, height: img.naturalHeight })
        }
        img.src = URL.createObjectURL(firstImageFile.file)
      }
    }
  }, [files, type])

  const handleQualityChange = (quality: number) => {
    onChange({ ...options, quality })
  }

  const handleFormatChange = (format: string) => {
    console.log('Format changed to:', format)
    onChange({ ...options, outputFormat: format === 'auto' ? undefined : format })
  }

  const handleDimensionChange = (dimension: 'width' | 'height', value: string) => {
    const numValue = value ? parseInt(value) : undefined
    if (dimension === 'width') {
      onChange({ ...options, maxWidth: numValue })
    } else {
      onChange({ ...options, maxHeight: numValue })
    }
  }

  const handleToggleChange = (key: keyof CompressionOptions, value: boolean) => {
    onChange({ ...options, [key]: value })
  }

  const getQualityLabel = (quality: number) => {
    if (quality >= 0.8) return 'High Quality'
    if (quality >= 0.6) return 'Medium Quality'
    if (quality >= 0.4) return 'Low Quality'
    return 'Maximum Compression'
  }

  const getFormatOptions = () => {
    switch (type) {
      case 'image':
        return [
          { value: 'auto', label: 'Keep Original' },
          { value: 'jpeg', label: 'JPEG' },
          { value: 'png', label: 'PNG' },
          { value: 'webp', label: 'WebP' },
          { value: 'avif', label: 'AVIF (Best Compression)' }
        ]
      case 'video':
        return [
          { value: 'auto', label: 'Keep Original' },
          { value: 'mp4', label: 'MP4' },
          { value: 'webm', label: 'WebM' },
          { value: 'avi', label: 'AVI' },
          { value: 'gif', label: 'Convert to GIF(may become larger)' }
        ]
      case 'gif':
        return [
          { value: 'auto', label: 'Keep as GIF' },
          { value: 'gif', label: 'Optimized GIF' },
          { value: 'mp4', label: 'Convert to MP4' },
          { value: 'webm', label: 'Convert to WebM' }
        ]
      default:
        return [{ value: 'auto', label: 'Keep Original' }]
    }
  }

  const getTypeIcon = () => {
    switch (type) {
      case 'image':
        return <ImageIcon className="w-5 h-5 text-blue-500 dark:text-blue-400" />
      case 'video':
        return <Video className="w-5 h-5 text-purple-500 dark:text-purple-400" />
      case 'gif':
        return <Sparkles className="w-5 h-5 text-pink-500 dark:text-pink-400" />
      default:
        return <Settings className="w-5 h-5 text-gray-500 dark:text-gray-400" />
    }
  }

  return (
    <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl shadow-lg border-2 border-blue-200 dark:border-blue-700">
      {/* Header - Always visible and prominent */}
      <div className="p-6 bg-white dark:bg-gray-800 rounded-t-xl border-b-2 border-blue-100 dark:border-blue-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getTypeIcon()}
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 capitalize">
                {type} Compression Settings
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Current: {getQualityLabel(options.quality)}
              </p>
            </div>
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/40 rounded-lg transition-colors"
          >
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              {isExpanded ? 'Hide' : 'Show'} Settings
            </span>
            <Settings className={`w-4 h-4 text-blue-600 dark:text-blue-400 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
          </button>
        </div>
      </div>

      {/* Quick Quality Presets - Always visible */}
      <div className="p-4 bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Quick Presets:</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">Click to apply</span>
        </div>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => handleQualityChange(0.1)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality <= 0.2
                ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border-2 border-red-300 dark:border-red-600'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-red-50 dark:hover:bg-red-900/20'
            }`}
          >
            🔥 Maximum Compression
          </button>
          <button
            onClick={() => handleQualityChange(0.4)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality > 0.2 && options.quality <= 0.5
                ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 border-2 border-orange-300 dark:border-orange-600'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-orange-50 dark:hover:bg-orange-900/20'
            }`}
          >
            ⚡ Balanced
          </button>
          <button
            onClick={() => handleQualityChange(0.7)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality > 0.5 && options.quality <= 0.8
                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-2 border-blue-300 dark:border-blue-600'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20'
            }`}
          >
            💎 High Quality
          </button>
          <button
            onClick={() => handleQualityChange(0.9)}
            className={`px-3 py-2 text-sm rounded-lg transition-colors ${
              options.quality > 0.8
                ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-2 border-green-300 dark:border-green-600'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/20'
            }`}
          >
            🎯 Best Quality
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-6 bg-white dark:bg-gray-800 rounded-b-xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Quality Slider */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <label className="flex items-center text-sm font-bold text-gray-800 dark:text-gray-200 mb-4">
                <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mr-2"></span>
                Fine-tune Quality Level
              </label>
              <div className="space-y-4">
                <div className="relative">
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.05"
                    value={options.quality}
                    onChange={(e) => handleQualityChange(parseFloat(e.target.value))}
                    className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Max Compression</span>
                  <span className="font-bold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
                    {Math.round(options.quality * 100)}% Quality
                  </span>
                  <span className="font-medium">Best Quality</span>
                </div>
                <div className="text-center">
                  <span className="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full text-sm font-medium">
                    {getQualityLabel(options.quality)}
                  </span>
                </div>
              </div>
            </div>

            {/* Output Format */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <label className="flex items-center text-sm font-bold text-gray-800 dark:text-gray-200 mb-4">
                <span className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full mr-2"></span>
                Output Format
              </label>
              <select
                value={options.outputFormat || 'auto'}
                onChange={(e) => handleFormatChange(e.target.value)}
                className="w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-medium"
              >
                {getFormatOptions().map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                Choose the output format for your compressed files
                {options.outputFormat === 'avif' && (
                  <span className="block mt-1 text-indigo-600 dark:text-indigo-400 font-medium">
                    ✨ AVIF provides up to 50% better compression than JPEG with superior quality
                  </span>
                )}
                {options.outputFormat === 'gif' && type === 'video' && (
                  <span className="block mt-1 text-green-600 dark:text-green-400 font-medium">
                    🎬 Converting video to animated GIF - perfect for social media and web
                  </span>
                )}
              </p>
            </div>

            {/* Dimensions */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <label className="flex items-center text-sm font-bold text-gray-800 dark:text-gray-200 mb-4">
                <span className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></span>
                Maximum Dimensions
              </label>
              <div className="flex space-x-3 items-center">
                <input
                  type="number"
                  placeholder={originalDimensions ? `${originalDimensions.width}` : "Width"}
                  value={options.maxWidth || ''}
                  onChange={(e) => handleDimensionChange('width', e.target.value)}
                  className="flex-1 px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-medium text-center placeholder-gray-500 dark:placeholder-gray-400"
                />
                <span className="flex items-center text-gray-400 dark:text-gray-500 font-bold text-lg">×</span>
                <input
                  type="number"
                  placeholder={originalDimensions ? `${originalDimensions.height}` : "Height"}
                  value={options.maxHeight || ''}
                  onChange={(e) => handleDimensionChange('height', e.target.value)}
                  className="flex-1 px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 font-medium text-center placeholder-gray-500 dark:placeholder-gray-400"
                />
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                💡 Original: {originalDimensions ? `${originalDimensions.width} × ${originalDimensions.height}` : 'Loading...'} | Leave empty to keep original
              </p>
            </div>

            {/* Video to GIF Settings */}
            {type === 'video' && options.outputFormat === 'gif' && (
              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4 lg:col-span-2 border-2 border-green-200 dark:border-green-700">
                <label className="flex items-center text-sm font-bold text-gray-800 dark:text-gray-200 mb-4">
                  <span className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></span>
                  🎬 Video to GIF Settings
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* GIF Frame Rate */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Frame Rate (FPS)
                    </label>
                    <select
                      value={(options as any).fps || 10}
                      onChange={(e) => onChange({ ...options, fps: parseInt(e.target.value) } as any)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    >
                      <option value={5}>5 FPS (Smaller file)</option>
                      <option value={10}>10 FPS (Balanced)</option>
                      <option value={15}>15 FPS (Smooth)</option>
                      <option value={20}>20 FPS (High quality)</option>
                    </select>
                  </div>

                  {/* GIF Scale */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Scale
                    </label>
                    <select
                      value={(options as any).gifScale || 'auto'}
                      onChange={(e) => onChange({ ...options, gifScale: e.target.value } as any)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                    >
                      <option value="auto">Auto (Keep aspect ratio)</option>
                      <option value="320">320px width</option>
                      <option value="480">480px width</option>
                      <option value="640">640px width</option>
                      <option value="800">800px width</option>
                    </select>
                  </div>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-3">
                  💡 Lower FPS and smaller scale = smaller file size. Perfect for social media sharing!
                </p>
              </div>
            )}

            {/* Additional Options */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 lg:col-span-2">
              <label className="flex items-center text-sm font-bold text-gray-800 dark:text-gray-200 mb-4">
                <span className="w-2 h-2 bg-orange-500 dark:bg-orange-400 rounded-full mr-2"></span>
                Additional Options
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border-2 border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 cursor-pointer transition-colors">
                  <input
                    type="checkbox"
                    checked={options.maintainAspectRatio}
                    onChange={(e) => handleToggleChange('maintainAspectRatio', e.target.checked)}
                    className="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 w-4 h-4"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">📐 Maintain aspect ratio</span>
                </label>

                <label className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border-2 border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 cursor-pointer transition-colors">
                  <input
                    type="checkbox"
                    checked={options.removeMetadata}
                    onChange={(e) => handleToggleChange('removeMetadata', e.target.checked)}
                    className="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 w-4 h-4"
                  />
                  <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">🗑️ Remove metadata</span>
                </label>

                {type === 'image' && (
                  <label className="flex items-center p-3 bg-white dark:bg-gray-800 rounded-lg border-2 border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 cursor-pointer transition-colors">
                    <input
                      type="checkbox"
                      checked={(options as any).preserveTransparency !== false}
                      onChange={(e) => handleToggleChange('preserveTransparency' as any, e.target.checked)}
                      className="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 w-4 h-4"
                    />
                    <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">✨ Preserve transparency</span>
                  </label>
                )}
              </div>
            </div>
          </div>

          {/* Presets */}
          <div className="mt-6 pt-6 border-t border-gray-100 dark:border-gray-700">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Quick Presets
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.9,
                  maxWidth: undefined,
                  maxHeight: undefined,
                  maintainAspectRatio: true,
                  removeMetadata: false
                })}
                className="px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800/40 transition-colors"
              >
                High Quality
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.7,
                  maxWidth: 1920,
                  maxHeight: 1080,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full hover:bg-purple-200 dark:hover:bg-purple-800/40 transition-colors"
              >
                Balanced
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.5,
                  maxWidth: 1280,
                  maxHeight: 720,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full hover:bg-green-200 dark:hover:bg-green-800/40 transition-colors"
              >
                Small Size
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.3,
                  maxWidth: 800,
                  maxHeight: 600,
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors"
              >
                Maximum Compression
              </button>
              <button
                onClick={() => onChange({
                  ...options,
                  quality: 0.6,
                  outputFormat: 'avif',
                  maintainAspectRatio: true,
                  removeMetadata: true
                })}
                className="px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded-full hover:bg-indigo-200 transition-colors"
              >
                🚀 AVIF Ultra
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
