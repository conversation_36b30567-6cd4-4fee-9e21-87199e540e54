import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

// This would typically require admin authentication
// For demo purposes, we'll skip auth but in production you'd need:
// const user = await auth.getCurrentUser()
// if (!user || user.role !== 'admin') return 401

export async function GET() {
  try {
    // Get total users
    const { count: totalUsers } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true })

    // Get active subscriptions
    const { count: activeSubscriptions } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('membership_status', 'active')

    // Get total compressions
    const { count: totalCompressions } = await supabaseAdmin
      .from('compression_logs')
      .select('*', { count: 'exact', head: true })

    // Get new users this month
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const { count: newUsersThisMonth } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startOfMonth.toISOString())

    // Get compressions today
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const { count: compressionsTodayCount } = await supabaseAdmin
      .from('compression_logs')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', today.toISOString())

    // Calculate monthly revenue (simplified - would need actual payment data)
    const { data: completedOrders } = await supabaseAdmin
      .from('orders')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', startOfMonth.toISOString())

    const monthlyRevenue = completedOrders?.reduce((sum, order) => sum + order.amount, 0) || 0

    return NextResponse.json({
      success: true,
      stats: {
        totalUsers: totalUsers || 0,
        activeSubscriptions: activeSubscriptions || 0,
        totalCompressions: totalCompressions || 0,
        monthlyRevenue: Math.round(monthlyRevenue * 100) / 100,
        newUsersThisMonth: newUsersThisMonth || 0,
        compressionsTodayCount: compressionsTodayCount || 0
      }
    })
  } catch (error) {
    console.error('Admin stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch admin stats' },
      { status: 500 }
    )
  }
}
