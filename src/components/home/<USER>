'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, HelpCircle } from 'lucide-react'

const faqs = [
  {
    question: "How does the free image compressor work while ensuring privacy?",
    answer: "Our image compressor processes all files locally in your browser, ensuring complete privacy. No files are uploaded to servers, and all compression happens on your device for maximum security. Your images never leave your computer."
  },
  {
    question: "Can I compress images without losing quality?",
    answer: "Yes, our advanced image compressor algorithm uses smart compression techniques to reduce file size while maintaining visual quality. You can achieve up to 90% size reduction with minimal quality impact, and you have full control over the quality settings."
  },
  {
    question: "What file formats does your compressor support?",
    answer: "Our tool supports all major formats: compress JPEG, compress PNG, compress GIF, MP4 video compression, and many more including WebP, BMP, TIFF, AVI, MOV, and WebM. You can also convert between formats during compression."
  },
  {
    question: "How much can I reduce my file sizes?",
    answer: "Typically, you can reduce image file size by 70-90% and video file size by 60-80% depending on the original format and quality settings. Our compressor is optimized for maximum efficiency while preserving visual quality."
  },
  {
    question: "Is there a limit on file size or number of files?",
    answer: "There are no artificial limits on the number of files you can compress. For individual files, we support up to 100MB per file to ensure optimal performance. You can process multiple files simultaneously using our batch compression feature."
  },
  {
    question: "Do I need to create an account to use the compressor?",
    answer: "No account required! Our compression tool is completely free to use without any registration. Simply visit our website, upload your files, and start compressing immediately. No hidden fees or subscription required."
  },
  {
    question: "Can I use this tool for commercial purposes?",
    answer: "Absolutely! Our compression tool is free for both personal and commercial use. Whether you're optimizing images for your business website, e-commerce store, or client projects, you can use our tool without any restrictions."
  },
  {
    question: "How fast is the compression process?",
    answer: "Compression speed depends on file size and your device's performance, but typically takes just a few seconds per image and under a minute for most videos. Since processing happens locally, there's no upload/download time, making it much faster than online alternatives."
  }
]

function FAQItem({ faq, index, isOpen, onToggle }: {
  faq: typeof faqs[0]
  index: number
  isOpen: boolean
  onToggle: () => void
}) {
  return (
    <motion.div
      className="border border-gray-200 dark:border-gray-700 rounded-2xl overflow-hidden bg-white dark:bg-gray-800 hover:shadow-lg transition-shadow duration-300"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
    >
      <button
        onClick={onToggle}
        className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 cursor-pointer"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 pr-4">
          {faq.question}
        </h3>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          className="flex-shrink-0"
        >
          <ChevronDown className="w-6 h-6 text-gray-500 dark:text-gray-400" />
        </motion.div>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="px-8 pb-6">
              <div className="h-px bg-gray-200 dark:bg-gray-600 mb-4" />
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                {faq.answer}
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(0) // First item open by default

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-5">
        <svg className="absolute top-0 left-0 w-full h-full" viewBox="0 0 1200 800">
          <defs>
            <pattern id="faq-dots" width="60" height="60" patternUnits="userSpaceOnUse">
              <circle cx="30" cy="30" r="2" fill="#3B82F6" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#faq-dots)" />
        </svg>
      </div>

      <div className="max-w-4xl mx-auto relative">
        {/* Section header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-3 px-6 py-3 bg-blue-50 dark:bg-blue-900/20 rounded-full border border-blue-100 dark:border-blue-700 mb-6"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <HelpCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Frequently Asked Questions</span>
          </motion.div>

          <motion.h2 
            className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Got <span className="text-blue-600 dark:text-blue-400">Questions</span>?
          </motion.h2>
          
          <motion.p 
            className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Find answers to the most common questions about our image and video compression tools.
          </motion.p>
        </motion.div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <FAQItem
              key={index}
              faq={faq}
              index={index}
              isOpen={openIndex === index}
              onToggle={() => toggleFAQ(index)}
            />
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Can't find the answer you're looking for? Our compression tool is designed to be intuitive and user-friendly.
            </p>
            <motion.button
              className="inline-flex items-center gap-3 px-8 py-4 bg-blue-600 dark:bg-blue-500 text-white font-bold rounded-full hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl cursor-pointer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <HelpCircle className="w-5 h-5" />
              <span>Try It Now - It's Free!</span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
