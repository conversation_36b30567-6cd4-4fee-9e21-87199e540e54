# CompressHub 部署和配置指南

## 🚀 项目概述

CompressHub 现已升级为完整的 SaaS 产品，包含以下核心功能：

### 核心服务
- **用户认证系统**: 基于邮箱的用户注册和登录
- **订阅管理**: 免费版和Pro版订阅计划
- **支付集成**: Stripe 支付网关集成
- **权限控制**: 基于订阅级别的功能限制
- **使用统计**: 用户使用情况跟踪和分析
- **管理后台**: 用户管理和数据统计

### 订阅方案

#### 免费版 (Free Plan)
- 每月50次压缩
- 最大文件大小: 10MB
- 支持基础图片格式 (PNG, JPEG, WebP)
- 基础压缩速度

#### Pro版 (Pro Plan)
- 无限制压缩
- 最大文件大小: 200MB
- 支持所有格式 (图片、GIF、视频)
- 高级压缩算法
- 批量处理 (最多100个文件)
- API访问 (每月10,000次调用)
- 优先邮件支持
- 格式转换功能

### 定价
- **Pro月付**: $9/月
- **Pro年付**: $84/年 (节省22%)

## 🛠 技术架构

### 前端技术栈
- **Next.js 15**: React框架
- **React 19**: 用户界面库
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **Framer Motion**: 动画库
- **React Hook Form**: 表单处理
- **React Hot Toast**: 通知系统

### 后端技术栈
- **Next.js API Routes**: 服务端API
- **Supabase**: 数据库和认证
- **Stripe**: 支付处理
- **Sharp**: 图片处理
- **Node.js**: 运行时环境

### 数据库设计
- **users**: 用户账户信息
- **membership_levels**: 订阅计划定义
- **orders**: 订单和支付记录
- **user_usage**: 用户使用统计
- **compression_logs**: 文件处理日志

## 📋 部署前准备

### 必需账户和服务
1. **Supabase账户**: 数据库和后端服务
2. **Stripe账户**: 支付处理
3. **Vercel账户**: 应用部署 (推荐)
4. **域名**: 自定义域名 (可选)

### 环境变量配置
创建 `.env.local` 文件并配置以下变量：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe 配置
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# 应用配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_random_secret_key
```

## 🗄 数据库设置

### 1. 创建Supabase项目
1. 访问 [Supabase](https://supabase.com)
2. 创建新项目
3. 等待项目初始化完成
4. 获取项目URL和API密钥

### 2. 执行数据库迁移
在Supabase SQL编辑器中执行 `database/schema.sql` 文件内容：

```sql
-- 创建所有必需的表和索引
-- 设置行级安全策略 (RLS)
-- 插入默认订阅计划数据
```

### 3. 配置行级安全 (RLS)
数据库已配置完整的行级安全策略，确保用户只能访问自己的数据。

## 💳 Stripe配置

### 1. 创建产品和价格
在Stripe控制台中创建以下产品：

1. **Pro Monthly**
   - 价格: $9.00 USD
   - 计费周期: 月度
   - 复制价格ID到代码中

2. **Pro Yearly**
   - 价格: $84.00 USD
   - 计费周期: 年度
   - 复制价格ID到代码中

### 2. 配置Webhook
1. 在Stripe控制台，进入 开发者 > Webhooks
2. 添加端点: `https://yourdomain.com/api/webhooks/stripe`
3. 选择以下事件：
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

## 🚀 部署步骤

### Vercel部署 (推荐)

1. **连接仓库**
   ```bash
   # 安装Vercel CLI
   npm i -g vercel
   
   # 部署
   vercel
   ```

2. **配置环境变量**
   在Vercel控制台的 Settings > Environment Variables 中添加所有环境变量

3. **域名配置**
   - 在Vercel中设置自定义域名
   - 更新Stripe webhook URL为生产域名
   - 更新NEXTAUTH_URL为生产URL

### 手动部署

1. **构建应用**
   ```bash
   npm run build
   ```

2. **启动生产服务器**
   ```bash
   npm start
   ```

## 🔐 安全配置

### 数据库安全
- 启用行级安全 (RLS)
- 配置适当的数据库角色和权限
- 定期备份数据库

### API安全
- 实施速率限制
- 验证文件类型和大小
- 使用HTTPS加密传输

### 支付安全
- 通过Stripe确保PCI合规
- 验证webhook签名
- 安全存储敏感信息

## 📊 监控和维护

### 性能监控
- 监控API响应时间
- 跟踪文件处理性能
- 监控数据库查询性能

### 使用统计
- 用户注册和活跃度
- 压缩使用情况
- 收入和订阅指标

### 定期维护
- 更新依赖包
- 监控Supabase使用量
- 检查Stripe交易状态
- 备份重要数据

## 🆘 故障排除

### 常见问题
1. **Stripe webhook失败**: 检查webhook URL和签名密钥
2. **数据库连接错误**: 验证Supabase配置
3. **文件上传失败**: 检查文件大小和类型限制
4. **认证问题**: 验证JWT配置和cookie设置

### 日志和调试
- 检查Vercel函数日志
- 监控Supabase日志
- 查看Stripe事件日志

## 📈 扩展建议

### 性能优化
- 实施CDN加速
- 添加Redis缓存
- 优化数据库查询

### 功能扩展
- 添加更多文件格式支持
- 实施API速率限制
- 添加团队协作功能
- 集成更多支付方式

### 监控和分析
- 集成Google Analytics
- 添加错误追踪 (如Sentry)
- 实施用户行为分析

## 📞 支持和联系

如需技术支持或有任何问题，请：
- 查看项目文档
- 提交GitHub Issue
- 联系技术支持团队

---

**注意**: 这是一个完整的SaaS产品，包含用户管理、订阅计费、权限控制等企业级功能。请确保在生产环境中正确配置所有安全设置。
