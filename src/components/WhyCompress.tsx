'use client'

import { Rocket, Smartphone, Target, BarChart3, Wifi, HardDrive } from 'lucide-react'

const features = [
  {
    icon: Rocket,
    title: 'Lightning Fast Loading',
    description: 'Compressed images load significantly faster, creating a smoother user experience and reducing bounce rates on your website.'
  },
  {
    icon: Smartphone,
    title: 'Mobile-First Performance',
    description: 'Optimized images are essential for mobile users who often have limited bandwidth and slower network connections.'
  },
  {
    icon: Target,
    title: 'Better SEO Rankings',
    description: 'Search engines like Google prioritize fast-loading websites, giving you a competitive advantage in search results.'
  },
  {
    icon: BarChart3,
    title: 'Higher Conversion Rates',
    description: 'Faster websites keep visitors engaged longer, leading to improved user satisfaction and higher conversion rates.'
  },
  {
    icon: Wifi,
    title: 'Reduced Bandwidth Costs',
    description: 'Smaller file sizes mean lower hosting costs and reduced data usage, benefiting both you and your users.'
  },
  {
    icon: HardDrive,
    title: 'Storage Optimization',
    description: 'Compressed files take up less server space and cloud storage, reducing infrastructure costs while maintaining quality.'
  }
]

export function WhyCompress() {
  return (
    <section className="py-20 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Why Should You Compress Your Images?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Optimizing your images to the smallest possible size is crucial for modern web performance and user experience.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 hover:bg-white dark:hover:bg-gray-600 hover:shadow-lg transition-all duration-300 group border border-gray-100 dark:border-gray-600"
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-600 dark:bg-blue-500 rounded-lg flex items-center justify-center group-hover:bg-blue-700 dark:group-hover:bg-blue-600 transition-colors">
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <div className="inline-flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/30 px-6 py-3 rounded-full">
            <Target className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="text-blue-800 dark:text-blue-300 font-medium">
              Start optimizing your images today for better performance
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}
