'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { X, Eye, RotateCcw } from 'lucide-react'
import { FileItem } from '@/types'

interface InteractivePreviewProps {
  item: FileItem
  originalUrl: string
  compressedUrl: string
  onClose: () => void
}

interface ImageDimensions {
  width: number
  height: number
  aspectRatio: number
}

export function InteractivePreview({
  item,
  originalUrl,
  compressedUrl,
  onClose
}: InteractivePreviewProps) {
  const [isComparing, setIsComparing] = useState(true) // Always show comparison
  const [scanPosition, setScanPosition] = useState(50) // Start in middle
  const [isScanning, setIsScanning] = useState(false)
  const [imageDimensions, setImageDimensions] = useState<ImageDimensions | null>(null)
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 })
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number | undefined>(undefined)

  // Load image and get dimensions
  useEffect(() => {
    setIsLoading(true)
    setHasError(false)

    const img = new Image()
    img.onload = () => {
      setImageDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
        aspectRatio: img.naturalWidth / img.naturalHeight
      })
      setIsLoading(false)
    }
    img.onerror = () => {
      setHasError(true)
      setIsLoading(false)
    }
    img.src = originalUrl
  }, [originalUrl])

  // Update container dimensions on resize
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        setContainerDimensions({ width: rect.width, height: rect.height })
      }
    }

    updateDimensions()
    window.addEventListener('resize', updateDimensions)
    return () => window.removeEventListener('resize', updateDimensions)
  }, [])

  // Calculate optimal display dimensions
  const getOptimalDimensions = useCallback(() => {
    if (!imageDimensions) return { width: '100%', height: '400px' }

    const maxWidth = Math.min(window.innerWidth - 64, 1200) // 64px for padding, max 1200px
    const maxHeight = Math.min(window.innerHeight - 200, 800) // 200px for header/footer, max 800px

    const { aspectRatio } = imageDimensions

    let width = maxWidth
    let height = width / aspectRatio

    // If height exceeds max, constrain by height
    if (height > maxHeight) {
      height = maxHeight
      width = height * aspectRatio
    }

    return {
      width: `${width}px`,
      height: `${height}px`
    }
  }, [imageDimensions])

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current) return
    
    setIsComparing(true)
    setIsScanning(true)
    
    const rect = containerRef.current.getBoundingClientRect()
    const startX = (e.clientX - rect.left) / rect.width * 100
    setScanPosition(startX)

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return
      const rect = containerRef.current.getBoundingClientRect()
      const x = Math.max(0, Math.min(100, (e.clientX - rect.left) / rect.width * 100))
      setScanPosition(x)
    }

    const handleMouseUp = () => {
      setIsScanning(false)
      // Animate scan line back to start
      animateToStart()
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [])

  const animateToStart = useCallback(() => {
    const startPosition = scanPosition
    const targetPosition = 50 // Return to middle instead of 0
    const startTime = Date.now()
    const duration = 800 // 800ms animation

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      // Easing function for smooth animation
      const easeOut = 1 - Math.pow(1 - progress, 3)
      const currentPosition = startPosition + (targetPosition - startPosition) * easeOut

      setScanPosition(currentPosition)
      
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate)
      } else {
        setIsComparing(true) // Keep comparison active
      }
    }

    animationRef.current = requestAnimationFrame(animate)
  }, [scanPosition])

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  const handleReset = () => {
    setIsComparing(true) // Keep comparison active
    setScanPosition(50) // Reset to middle
    setIsScanning(false)
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-90 dark:bg-black dark:bg-opacity-95 flex items-center justify-center z-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div className="w-full h-full flex items-center justify-center">
        <div className="relative bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-2xl max-w-full max-h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{item.name}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Original: {(item.originalSize / 1024).toFixed(1)}KB →
                Compressed: {item.compressedSize ? (item.compressedSize / 1024).toFixed(1) : '0'}KB
                {item.compressionRatio && (
                  <span className="ml-2 text-green-600 dark:text-green-400 font-medium">
                    ({item.compressionRatio.toFixed(1)}% reduction)
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleReset}
                className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                title="Reset comparison"
              >
                <RotateCcw className="w-5 h-5" />
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Image Container */}
          <div className="relative bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
            {isLoading && (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
                <span className="ml-3 text-gray-600 dark:text-gray-300">Loading image...</span>
              </div>
            )}

            {hasError && (
              <div className="flex items-center justify-center p-8 text-red-600 dark:text-red-400">
                <span>Failed to load image</span>
              </div>
            )}

            {!isLoading && !hasError && (
              <div
                ref={containerRef}
                className="relative overflow-hidden cursor-crosshair select-none"
                onMouseDown={handleMouseDown}
                style={getOptimalDimensions()}
              >
                {/* Compressed Image (Background) */}
                <img
                  src={compressedUrl}
                  alt={`${item.name} - Compressed`}
                  className="absolute inset-0 w-full h-full object-contain"
                  draggable={false}
                />

                {/* Original Image (Clipped) */}
                <div
                  className="absolute inset-0 overflow-hidden"
                  style={{
                    clipPath: isComparing
                      ? `polygon(0 0, ${scanPosition}% 0, ${scanPosition}% 100%, 0 100%)`
                      : 'polygon(0 0, 0 0, 0 100%, 0 100%)'
                  }}
                >
                  <img
                    src={originalUrl}
                    alt={`${item.name} - Original`}
                    className="w-full h-full object-contain"
                    draggable={false}
                  />
                </div>

                {/* Scan Line - Always visible */}
                <div
                  className="absolute top-0 bottom-0 w-0.5 bg-white dark:bg-gray-300 shadow-lg z-10 transition-all duration-75"
                  style={{ left: `${scanPosition}%` }}
                >
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white dark:bg-gray-700 rounded-full shadow-lg flex items-center justify-center">
                    <Eye className="w-4 h-4 text-gray-700 dark:text-gray-300" />
                  </div>
                </div>

                {/* Subtle instruction hint */}
                {!isComparing && (
                  <div className="absolute top-4 left-4 bg-black bg-opacity-60 dark:bg-black dark:bg-opacity-80 text-white px-3 py-2 rounded-lg text-sm">
                    拖拽对比原图与压缩图
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
              <div>
                Drag across the image to see the quality difference
              </div>
              <div className="flex items-center space-x-4">
                <span>Original Quality</span>
                <div className="w-16 h-2 bg-gradient-to-r from-blue-500 to-green-500 dark:from-blue-400 dark:to-green-400 rounded"></div>
                <span>Compressed</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
