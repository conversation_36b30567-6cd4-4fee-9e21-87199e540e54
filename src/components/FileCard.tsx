'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileCardProps } from '@/types'
import { formatFileSize, createImagePreview, createVideoPreview } from '@/lib/utils'
import { InteractivePreview } from './InteractivePreview'
import {
  X,
  Download,
  RotateCcw,
  Eye,
  Image as ImageIcon,
  Video,
  FileImage,
  CheckCircle,
  AlertCircle,
  Clock,
  Loader2,
  RefreshCw
} from 'lucide-react'

export function FileCard({ item, onRemove, onRetry, onDownload, onPreview, onReprocess }: FileCardProps) {
  const [preview, setPreview] = useState<string>('')
  const [showPreview, setShowPreview] = useState(false)
  const [showInteractivePreview, setShowInteractivePreview] = useState(false)
  const [compressedPreview, setCompressedPreview] = useState<string>('')

  useEffect(() => {
    const generatePreview = async () => {
      try {
        if (item.type === 'image' || item.type === 'gif') {
          const previewUrl = await createImagePreview(item.file)
          setPreview(previewUrl)
        } else if (item.type === 'video') {
          const previewUrl = await createVideoPreview(item.file)
          setPreview(previewUrl)
        }
      } catch (error) {
        console.error('Failed to generate preview:', error)
      }
    }

    const generateCompressedPreview = async () => {
      if (item.compressedFile && (item.type === 'image' || item.type === 'gif')) {
        try {
          const compressedUrl = await createImagePreview(item.compressedFile)
          setCompressedPreview(compressedUrl)
        } catch (error) {
          console.error('Failed to generate compressed preview:', error)
        }
      }
    }

    if (!item.preview) {
      generatePreview()
    } else {
      setPreview(item.preview)
    }

    // Generate compressed preview if available
    if (item.status === 'completed' && item.compressedFile) {
      generateCompressedPreview()
    }
  }, [item])

  const getFileIcon = () => {
    switch (item.type) {
      case 'image':
      case 'gif':
        return <ImageIcon className="w-6 h-6 text-blue-500" />
      case 'video':
        return <Video className="w-6 h-6 text-purple-500" />
      default:
        return <FileImage className="w-6 h-6 text-gray-500" />
    }
  }

  const getStatusIcon = () => {
    switch (item.status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />
      case 'cancelled':
        return <X className="w-4 h-4 text-gray-500" />
      default:
        return null
    }
  }

  const getStatusColor = () => {
    switch (item.status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const compressionSavings = item.compressedSize 
    ? ((item.originalSize - item.compressedSize) / item.originalSize * 100).toFixed(1)
    : null

  return (
    <>
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-shadow duration-200"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        <div className="flex items-start space-x-4">
          {/* Preview/Icon */}
          <div className="flex-shrink-0">
            {preview ? (
              <div 
                className="w-16 h-16 rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => setShowPreview(true)}
              >
                <img 
                  src={preview} 
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                {getFileIcon()}
              </div>
            )}
          </div>

          {/* File Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 truncate" title={item.name}>
                  {item.name}
                </h3>
                <div className="flex items-center space-x-3 mt-1 text-sm text-gray-500 dark:text-gray-400">
                  <span>{formatFileSize(item.originalSize)}</span>
                  <span className="capitalize">{item.type}</span>
                  <span className="uppercase">{item.format}</span>
                </div>

                {/* Compression Info */}
                {item.status === 'completed' && item.compressedSize && (
                  <div className="mt-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-600 dark:text-gray-300">
                        Compressed: {formatFileSize(item.compressedSize)}
                      </span>
                      {compressionSavings && (
                        <span className="text-green-600 dark:text-green-400 font-medium">
                          (-{compressionSavings}%)
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {item.status === 'error' && item.error && (
                  <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                    {item.error}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2 ml-4">
                {preview && (
                  <>
                    {item.status === 'completed' && compressedPreview && (item.type === 'image' || item.type === 'gif') ? (
                      <button
                        onClick={() => setShowInteractivePreview(true)}
                        className="p-2 text-gray-400 dark:text-gray-500 hover:text-purple-500 dark:hover:text-purple-400 transition-colors"
                        title="Interactive Comparison"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    ) : (
                      <button
                        onClick={() => setShowPreview(true)}
                        className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                        title="Preview"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    )}
                  </>
                )}

                {item.status === 'completed' && (
                  <>
                    {onReprocess && (
                      <button
                        onClick={() => onReprocess(item.id)}
                        className="p-2 text-gray-400 dark:text-gray-500 hover:text-orange-500 dark:hover:text-orange-400 transition-colors"
                        title="Reprocess with current settings"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      onClick={() => onDownload(item.id)}
                      className="p-2 text-gray-400 dark:text-gray-500 hover:text-green-500 dark:hover:text-green-400 transition-colors"
                      title="Download"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                  </>
                )}

                {item.status === 'error' && (
                  <button
                    onClick={() => onRetry(item.id)}
                    className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                    title="Retry"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                )}

                <button
                  onClick={() => onRemove(item.id)}
                  className="p-2 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title="Remove"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Progress Bar */}
            {item.status === 'processing' && (
              <motion.div
                className="mt-3"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                  <span>Processing...</span>
                  <span>{Math.round(item.progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                  <motion.div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${item.progress}%` }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                  />
                </div>
              </motion.div>
            )}

            {/* Status Badge */}
            <div className="mt-3 flex items-center">
              <div className={`
                inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border
                ${getStatusColor()}
              `}>
                {getStatusIcon()}
                <span className="capitalize">{item.status}</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Preview Modal */}
      {showPreview && preview && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setShowPreview(false)}
        >
          <div className="max-w-4xl max-h-full">
            <div className="relative">
              <button
                onClick={() => setShowPreview(false)}
                className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-colors z-10"
              >
                <X className="w-6 h-6" />
              </button>
              {item.type === 'video' ? (
                <video 
                  src={URL.createObjectURL(item.file)}
                  controls
                  className="max-w-full max-h-full rounded-lg"
                />
              ) : (
                <img 
                  src={preview}
                  alt={item.name}
                  className="max-w-full max-h-full rounded-lg"
                />
              )}
            </div>
          </div>
        </div>
      )}

      {/* Interactive Preview Modal */}
      {showInteractivePreview && preview && compressedPreview && (
        <InteractivePreview
          item={item}
          originalUrl={preview}
          compressedUrl={compressedPreview}
          onClose={() => setShowInteractivePreview(false)}
        />
      )}
    </>
  )
}
