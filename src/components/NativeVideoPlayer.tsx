'use client'

import { useState, useRef, useEffect } from 'react'
import { Save } from 'lucide-react'
import toast from 'react-hot-toast'

interface NativeVideoPlayerProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

export function NativeVideoPlayer({ file, onEditComplete }: NativeVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [duration, setDuration] = useState(0)
  const [videoUrl, setVideoUrl] = useState<string>('')

  useEffect(() => {
    console.log('NativeVideoPlayer: Creating URL for file:', file.name)
    const url = URL.createObjectURL(file)
    setVideoUrl(url)
    console.log('Video URL created:', url)

    return () => {
      console.log('Cleaning up video URL')
      URL.revokeObjectURL(url)
    }
  }, [file])

  const handleLoadedMetadata = () => {
    console.log('✅ Native video metadata loaded!')
    const video = videoRef.current
    if (video && video.duration && !isNaN(video.duration)) {
      console.log('Duration:', video.duration)
      setDuration(video.duration)
      setIsLoading(false)
      console.log('✅ Native video is ready!')
    } else {
      console.error('❌ Invalid video duration:', video?.duration)
      setIsLoading(false)
      toast.error('Invalid video file')
    }
  }

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('❌ Native video error:', e)
    const video = e.currentTarget
    console.error('Video error details:', video.error)
    setIsLoading(false)
    toast.error('Failed to load video: ' + (video.error?.message || 'Unknown error'))
  }

  const handleLoadStart = () => {
    console.log('📥 Native video load started')
  }

  const handleCanPlay = () => {
    console.log('✅ Native video can play')
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleExport = async () => {
    try {
      const blob = new Blob([file], { type: file.type })
      onEditComplete(blob)
      toast.success('Video exported successfully!')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export video')
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-gray-100">
        Native Video Player - {file.name}
      </h2>
      
      {isLoading && (
        <div className="flex items-center justify-center h-64 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading video...</p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              File: {file.name} ({Math.round(file.size / 1024 / 1024)}MB)
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Type: {file.type}
            </p>
          </div>
        </div>
      )}

      {/* Always show video element, even when loading */}
      <div className="relative bg-black rounded-lg overflow-hidden mb-6">
        <video
          ref={videoRef}
          src={videoUrl}
          className="w-full h-auto max-h-96 object-contain"
          controls={true}  // Use native controls for simplicity
          playsInline
          preload="metadata"
          onLoadedMetadata={handleLoadedMetadata}
          onError={handleError}
          onLoadStart={handleLoadStart}
          onCanPlay={handleCanPlay}
          style={{ display: isLoading ? 'none' : 'block' }}
        />
        
        {!isLoading && (
          <div className="absolute top-4 right-4">
            <button
              onClick={handleExport}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2 shadow-lg"
            >
              <Save className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        )}
      </div>

      {!isLoading && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Video Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Duration:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(duration)}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">File Size:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{Math.round(file.size / 1024 / 1024)}MB</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Format:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{file.type}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Status:</span>
              <span className="ml-2 text-green-600 dark:text-green-400">Ready</span>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Native Video Player:</strong> This player uses the browser's native video controls for maximum compatibility. 
              You can play, pause, seek, and adjust volume using the built-in controls.
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
