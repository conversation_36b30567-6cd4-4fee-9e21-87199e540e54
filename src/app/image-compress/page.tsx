'use client'

import { useState, useCallback, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileUpload } from '@/components/FileUpload'
import { FileCard } from '@/components/FileCard'
import { CompressionSettings } from '@/components/CompressionSettings'
import { ProcessingStats } from '@/components/ProcessingStats'
import { useCompressionManager } from '@/hooks/useCompressionManager'
import { useAuth, usePermissions } from '@/hooks/useAuth'
import { AuthModal } from '@/components/AuthModal'
import { ImageCompressionOptions } from '@/types'
import { getUploadedFile, clearUploadedFile, getFileCategory, base64ToFile } from '@/utils/fileHandler'
import toast from 'react-hot-toast'
import { 
  ImageIcon, 
  Sparkles, 
  Zap, 
  Download,
  Settings,
  FileImage,
  Layers
} from 'lucide-react'

export default function ImageCompressPage() {
  const { user } = useAuth()
  const { canCompress, canUseAdvancedFormats } = usePermissions()
  const [showAuthModal, setShowAuthModal] = useState(false)

  const [compressionOptions, setCompressionOptions] = useState<ImageCompressionOptions>({
    quality: 0.1, // Maximum Compression for images
    maintainAspectRatio: true,
    removeMetadata: true,
    preserveTransparency: true
  })

  const {
    files,
    isProcessing,
    currentProcessingIndex,
    addFiles,
    removeFile,
    compressAllFiles,
    retryFile,
    reprocessFile,
    downloadFile: downloadSingleFile,
    downloadAllFiles,
    clearAllFiles,
    getStats
  } = useCompressionManager()

  const stats = getStats()

  // Check for uploaded file from hero section
  useEffect(() => {
    const uploadedFileData = getUploadedFile()
    if (uploadedFileData) {
      // Check if it's an image file (not GIF)
      const fileCategory = getFileCategory(uploadedFileData.type)
      if (fileCategory === 'image' && !uploadedFileData.type.includes('gif')) {
        try {
          // Create a File object from the base64 data
          const file = base64ToFile(
            uploadedFileData.data,
            uploadedFileData.name,
            uploadedFileData.type,
            uploadedFileData.lastModified
          )

          // Add the file to the compression manager
          addFiles([file])

          // Clear the uploaded file data
          clearUploadedFile()

          // Show success message
          toast.success(`Image "${uploadedFileData.name}" loaded successfully!`)
        } catch (error) {
          console.error('Failed to load uploaded file:', error)
          toast.error('Failed to load the uploaded file. Please try uploading again.')
          clearUploadedFile()
        }
      } else {
        // Wrong file type, clear and show error
        clearUploadedFile()
        if (fileCategory === 'gif') {
          toast.error('GIF files should be processed on the GIF compression page.')
        } else {
          toast.error('This file type is not supported on the image compression page.')
        }
      }
    }
  }, [addFiles])

  const handleFileUpload = useCallback((uploadedFiles: File[]) => {
    // Filter only image files
    const imageFiles = uploadedFiles.filter(file =>
      file.type.startsWith('image/') && !file.type.includes('gif')
    )

    if (imageFiles.length !== uploadedFiles.length) {
      toast.error('Only image files (PNG, JPEG, WebP) are allowed on this page. GIF files should be processed on the GIF compression page.')
      return
    }

    // Check permissions for each file
    for (const file of imageFiles) {
      const fileSizeMb = file.size / (1024 * 1024)

      // Check file size permission
      const compressionPermission = canCompress(fileSizeMb)
      if (!compressionPermission.allowed) {
        if (!user) {
          setShowAuthModal(true)
        } else {
          toast.error(compressionPermission.reason || 'Permission denied')
        }
        return
      }

      // Check format permission
      const formatPermission = canUseAdvancedFormats(file.type)
      if (!formatPermission.allowed) {
        if (!user) {
          setShowAuthModal(true)
        } else {
          toast.error(formatPermission.reason || 'Permission denied')
        }
        return
      }
    }

    if (imageFiles.length > 0) {
      addFiles(imageFiles)
    }
  }, [addFiles, canCompress, canUseAdvancedFormats, user, setShowAuthModal])

  const handleCompress = useCallback(() => {
    compressAllFiles(compressionOptions)
  }, [compressAllFiles, compressionOptions])

  const handleRetry = (id: string) => {
    retryFile(id, compressionOptions)
  }

  const handleReprocess = (id: string) => {
    reprocessFile(id, compressionOptions)
  }

  const handleOptionsChange = (options: any) => {
    setCompressionOptions(options as ImageCompressionOptions)
  }

  const handlePreview = (id: string) => {
    console.log('Preview image:', id)
  }

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 1, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                <ImageIcon className="w-10 h-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-yellow-800" />
              </div>
            </div>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Image <span className="text-blue-600 dark:text-blue-400">Compression</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            Compress PNG, JPEG, and WebP images while preserving quality and transparency. 
            Reduce file sizes by up to 90% with our advanced compression algorithms.
          </p>
          
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center">
              <FileImage className="w-4 h-4 mr-2 text-blue-500" />
              PNG, JPEG, WebP Support
            </div>
            <div className="flex items-center">
              <Layers className="w-4 h-4 mr-2 text-green-500" />
              Transparency Preserved
            </div>
            <div className="flex items-center">
              <Zap className="w-4 h-4 mr-2 text-yellow-500" />
              Batch Processing
            </div>
          </div>
        </motion.div>

        {/* Upload Section */}
        <motion.div
          initial={{ opacity: 1, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <FileUpload
            onFileUpload={handleFileUpload}
            acceptedTypes={{
              'image/png': ['.png'],
              'image/jpeg': ['.jpg', '.jpeg'],
              'image/webp': ['.webp'],
              'image/avif': ['.avif']
            }}
            maxFileSize={50 * 1024 * 1024} // 50MB for images
            title="Drop your images here"
            subtitle="Support for PNG, JPEG, WebP, and AVIF formats"
          />
        </motion.div>

        {/* Settings and Stats */}
        {files.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
            <div className="lg:col-span-2">
              <CompressionSettings
                type="image"
                options={compressionOptions}
                onChange={handleOptionsChange}
                files={files}
              />
            </div>
            <div>
              <ProcessingStats
                stats={stats}
                isProcessing={isProcessing}
                currentProcessingIndex={currentProcessingIndex}
                onCompress={handleCompress}
                onDownloadAll={downloadAllFiles}
                onClearAll={clearAllFiles}
              />
            </div>
          </div>
        )}

        {/* Files List */}
        {files.length > 0 && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                <Settings className="w-6 h-6 mr-3 text-blue-600" />
                Image Files ({files.length})
              </h2>
              {stats.completed > 0 && (
                <button
                  onClick={downloadAllFiles}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download All ({stats.completed})
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {files.map((file, index) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 1, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <FileCard
                    item={file}
                    onRemove={removeFile}
                    onRetry={handleRetry}
                    onReprocess={handleReprocess}
                    onDownload={downloadSingleFile}
                    onPreview={handlePreview}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Features Section */}
        {files.length === 0 && (
          <motion.div
            className="mt-16"
            initial={{ opacity: 1, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Why Choose Our Image Compression?
              </h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Advanced algorithms ensure maximum compression while maintaining visual quality
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">Lightning Fast</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Process multiple images simultaneously with our optimized compression engine
                </p>
              </div>

              <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Layers className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">Quality Preserved</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Smart algorithms maintain image quality while significantly reducing file size
                </p>
              </div>

              <div className="text-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <FileImage className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">Format Support</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Support for PNG, JPEG, WebP with transparency preservation
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </main>


      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        title="Sign In Required"
        description="Please sign in to compress files larger than 10MB or use advanced features."
      />
    </div>
  )
}
