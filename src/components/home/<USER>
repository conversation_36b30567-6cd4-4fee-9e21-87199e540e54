'use client'

import { motion } from 'framer-motion'
import { Upload, Settings, Download, CheckCircle } from 'lucide-react'

const steps = [
  {
    icon: Upload,
    title: 'Upload Files',
    description: 'Drag and drop your images or videos, or click to browse. Support for multiple file formats and batch uploads.',
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-700'
  },
  {
    icon: Settings,
    title: 'Smart Processing',
    description: 'Our AI-powered algorithms automatically optimize compression settings for the best quality-to-size ratio.',
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-700'
  },
  {
    icon: Download,
    title: 'Download Results',
    description: 'Get your compressed files instantly. Compare before/after results and download individually or as a zip.',
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-700'
  }
]

function StepCard({ step, index }: { step: typeof steps[0]; index: number }) {
  const Icon = step.icon

  return (
    <motion.div
      className="relative group"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.2 }}
      viewport={{ once: true }}
    >
      {/* Connection line to next step */}
      {index < steps.length - 1 && (
        <div className="hidden lg:block absolute top-1/2 -right-8 xl:-right-12 w-16 xl:w-24 h-0.5 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-600 dark:to-gray-700 z-10">
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
            initial={{ width: 0 }}
            whileInView={{ width: '100%' }}
            transition={{ duration: 0.8, delay: index * 0.2 + 0.5 }}
            viewport={{ once: true }}
          />
          {/* Arrow */}
          <div className="absolute -right-2 -top-1 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rotate-45 rounded-sm" />
        </div>
      )}

      <div className={`
        relative p-8 rounded-2xl border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1
        ${step.bgColor} ${step.borderColor} group-hover:border-opacity-60
      `}>
        {/* Step number */}
        <div className="absolute -top-4 -left-4 w-8 h-8 bg-white dark:bg-gray-800 rounded-full border-2 border-gray-200 dark:border-gray-600 flex items-center justify-center shadow-lg">
          <span className="text-sm font-bold text-gray-700 dark:text-gray-300">{index + 1}</span>
        </div>

        {/* Icon */}
        <div className={`
          w-16 h-16 rounded-xl bg-gradient-to-br ${step.color} 
          flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300
        `}>
          <Icon className="w-8 h-8 text-white" />
        </div>

        {/* Content */}
        <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
          {step.title}
        </h3>
        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
          {step.description}
        </p>

        {/* Decorative corner */}
        <div className={`
          absolute top-4 right-4 w-3 h-3 rounded-full bg-gradient-to-br ${step.color} 
          opacity-20 group-hover:opacity-40 transition-opacity duration-300
        `} />
      </div>
    </motion.div>
  )
}

export function HowItWorks() {
  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-10">
        <svg className="absolute top-0 left-0 w-full h-full" viewBox="0 0 1200 800">
          <defs>
            <pattern id="dots" width="40" height="40" patternUnits="userSpaceOnUse">
              <circle cx="20" cy="20" r="1" fill="#3B82F6" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#dots)" />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto relative">
        {/* Section header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2 
            className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            How It <span className="text-blue-600 dark:text-blue-400">Works</span>
          </motion.h2>
          <motion.p 
            className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Get started in seconds with our simple three-step process. No technical knowledge required.
          </motion.p>
        </motion.div>

        {/* Steps */}
        <div className="grid lg:grid-cols-3 gap-8 lg:gap-4 xl:gap-8 mb-16">
          {steps.map((step, index) => (
            <StepCard key={index} step={step} index={index} />
          ))}
        </div>

        {/* Features highlight */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-700"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { icon: CheckCircle, text: 'No registration required' },
              { icon: CheckCircle, text: 'Files processed locally' },
              { icon: CheckCircle, text: 'Unlimited file size' },
              { icon: CheckCircle, text: 'Batch processing' }
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                viewport={{ once: true }}
              >
                <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 font-medium">{feature.text}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="inline-flex items-center gap-3 px-8 py-4 bg-blue-600 dark:bg-blue-500 text-white font-bold rounded-full hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl cursor-pointer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Upload className="w-5 h-5" />
            <span>Try It Now - It's Free!</span>
          </motion.button>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
            No credit card required • Start compressing in seconds
          </p>
        </motion.div>
      </div>
    </section>
  )
}
