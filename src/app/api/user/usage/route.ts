import { NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { usageOperations, compressionLogOperations } from '@/lib/database'

export async function GET() {
  try {
    const user = await auth.getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get today's usage
    const todayUsage = await usageOperations.getUserUsageToday(user.id)
    
    // Get compression stats for the last 30 days
    const compressionStats = await compressionLogOperations.getUserCompressionStats(user.id, 30)
    
    // Calculate summary statistics
    const totalCompressions = compressionStats.length
    const totalOriginalSize = compressionStats.reduce((sum, log) => sum + log.original_size_mb, 0)
    const totalCompressedSize = compressionStats.reduce((sum, log) => sum + log.compressed_size_mb, 0)
    const totalSavings = totalOriginalSize - totalCompressedSize
    const averageCompressionRatio = totalCompressions > 0 
      ? compressionStats.reduce((sum, log) => sum + log.compression_ratio, 0) / totalCompressions 
      : 0

    // Group compressions by date for chart data
    const compressionsByDate = compressionStats.reduce((acc, log) => {
      const date = log.created_at.split('T')[0]
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      success: true,
      usage: {
        today: todayUsage,
        summary: {
          totalCompressions,
          totalOriginalSizeMb: Math.round(totalOriginalSize * 100) / 100,
          totalCompressedSizeMb: Math.round(totalCompressedSize * 100) / 100,
          totalSavingsMb: Math.round(totalSavings * 100) / 100,
          averageCompressionRatio: Math.round(averageCompressionRatio * 100) / 100
        },
        chartData: Object.entries(compressionsByDate).map(([date, count]) => ({
          date,
          compressions: count
        })).sort((a, b) => a.date.localeCompare(b.date))
      }
    })
  } catch (error) {
    console.error('Get usage error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch usage data' },
      { status: 500 }
    )
  }
}
