import { FAQ } from "@/components/home/<USER>";
import { ImageComparison } from "@/components/ImageComparison";
import "@/styles/hero-animations.css";

export default function Home() {
  return (
    <div className="bg-white">
      <main className="w-full min-h-screen text-gray-900">
        <FAQ />
        <ImageComparison
          beforeImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=800&fit=crop&q=100"
          afterImage="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=1200&h=800&fit=crop&q=75"
          beforeLabel="ORIGINAL"
          afterLabel="COMPRESSED"
          beforeSize="1.1 MB"
          afterSize="188 KB"
        />
      </main>

    </div>
  );
}
