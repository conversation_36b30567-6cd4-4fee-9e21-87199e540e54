import { NextRequest, NextResponse } from 'next/server'
import { auth, permissions } from '@/lib/auth'
import { usageOperations, compressionLogOperations, userOperations } from '@/lib/database'
import sharp from 'sharp'
import { z } from 'zod'

const batchCompressSchema = z.object({
  quality: z.number().min(0.1).max(1).optional().default(0.8),
  format: z.enum(['original', 'jpeg', 'png', 'webp']).optional().default('original'),
  removeMetadata: z.boolean().optional().default(false),
  maxFiles: z.number().min(1).max(100).optional().default(10)
})

export async function POST(request: NextRequest) {
  try {
    const user = await auth.getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user can use batch processing
    const canUseBatch = permissions.canUseBatchProcessing(user)
    if (!canUseBatch.allowed) {
      return NextResponse.json(
        { error: canUseBatch.reason },
        { status: 403 }
      )
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const optionsStr = formData.get('options') as string

    let options
    try {
      options = batchCompressSchema.parse(JSON.parse(optionsStr || '{}'))
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid options format' },
        { status: 400 }
      )
    }

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      )
    }

    if (files.length > options.maxFiles) {
      return NextResponse.json(
        { error: `Maximum ${options.maxFiles} files allowed for batch processing` },
        { status: 400 }
      )
    }

    const results = []
    const startTime = Date.now()
    let totalOriginalSize = 0
    let totalCompressedSize = 0

    for (const file of files) {
      try {
        // Check file type
        if (!file.type.startsWith('image/')) {
          results.push({
            filename: file.name,
            success: false,
            error: 'Only image files are supported'
          })
          continue
        }

        const fileSizeMb = file.size / (1024 * 1024)
        totalOriginalSize += fileSizeMb

        // Check file size limits
        const canCompress = permissions.canCompress(user, fileSizeMb)
        if (!canCompress.allowed) {
          results.push({
            filename: file.name,
            success: false,
            error: canCompress.reason
          })
          continue
        }

        // Process the image
        const buffer = Buffer.from(await file.arrayBuffer())
        let sharpInstance = sharp(buffer)

        if (options.removeMetadata) {
          sharpInstance = sharpInstance.withMetadata({})
        }

        let outputBuffer: Buffer
        let outputFormat = options.format === 'original' ? file.type.split('/')[1] : options.format

        switch (outputFormat) {
          case 'jpeg':
          case 'jpg':
            outputBuffer = await sharpInstance
              .jpeg({ quality: Math.round(options.quality * 100) })
              .toBuffer()
            break
          case 'png':
            outputBuffer = await sharpInstance
              .png({ quality: Math.round(options.quality * 100) })
              .toBuffer()
            break
          case 'webp':
            outputBuffer = await sharpInstance
              .webp({ quality: Math.round(options.quality * 100) })
              .toBuffer()
            break
          default:
            outputBuffer = await sharpInstance
              .jpeg({ quality: Math.round(options.quality * 100) })
              .toBuffer()
            outputFormat = 'jpeg'
        }

        const compressedSizeMb = outputBuffer.length / (1024 * 1024)
        totalCompressedSize += compressedSizeMb
        const compressionRatio = ((fileSizeMb - compressedSizeMb) / fileSizeMb) * 100

        // Convert to base64 for response
        const base64Data = outputBuffer.toString('base64')

        results.push({
          filename: file.name,
          success: true,
          originalSize: file.size,
          compressedSize: outputBuffer.length,
          compressionRatio: Math.round(compressionRatio * 100) / 100,
          format: outputFormat,
          data: `data:image/${outputFormat};base64,${base64Data}`
        })

        // Log individual compression
        await compressionLogOperations.logCompression({
          user_id: user.id,
          file_name: file.name,
          file_type: file.type,
          original_size_mb: fileSizeMb,
          compressed_size_mb: compressedSizeMb,
          compression_ratio: compressionRatio,
          processing_time_ms: Date.now() - startTime,
          ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
          user_agent: request.headers.get('user-agent') || undefined
        })

      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error)
        results.push({
          filename: file.name,
          success: false,
          error: 'Failed to process image'
        })
      }
    }

    const totalProcessingTime = Date.now() - startTime
    const successfulCompressions = results.filter(r => r.success).length

    // Update usage statistics
    if (successfulCompressions > 0) {
      await usageOperations.incrementUserUsage(user.id, 'compression', totalOriginalSize)
    }

    return NextResponse.json({
      success: true,
      results,
      summary: {
        totalFiles: files.length,
        successfulCompressions,
        failedCompressions: files.length - successfulCompressions,
        totalOriginalSizeMb: Math.round(totalOriginalSize * 100) / 100,
        totalCompressedSizeMb: Math.round(totalCompressedSize * 100) / 100,
        totalSpaceSavedMb: Math.round((totalOriginalSize - totalCompressedSize) * 100) / 100,
        averageCompressionRatio: successfulCompressions > 0
          ? Math.round((results.filter(r => r.success).reduce((sum, r) => sum + (r.compressionRatio || 0), 0) / successfulCompressions) * 100) / 100
          : 0,
        totalProcessingTimeMs: totalProcessingTime
      }
    })
  } catch (error) {
    console.error('Batch compression error:', error)
    return NextResponse.json(
      { error: 'Failed to process batch compression' },
      { status: 500 }
    )
  }
}