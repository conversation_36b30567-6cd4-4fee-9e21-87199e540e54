'use client'

import { usePathname } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@/components/Header'

interface ClientLayoutProps {
  children: React.ReactNode
}

export function ClientLayout({ children }: ClientLayoutProps) {
  const pathname = usePathname()

  return (
    <>
      <Header currentPath={pathname} />
      <main className="min-h-screen">
        {children}
      </main>
      <Footer />
    </>
  )
}
