'use client'

import { useState, useRef, useEffect } from 'react'

interface ImageComparisonProps {
  beforeImage: string
  afterImage: string
  beforeLabel?: string
  afterLabel?: string
  beforeSize?: string
  afterSize?: string
}

export function ImageComparison({
  beforeImage,
  afterImage,
  beforeLabel = 'ORIGINAL',
  afterLabel = 'COMPRESSED',
  beforeSize = '2.1 MB',
  afterSize = '312 KB'
}: ImageComparisonProps) {
  const [sliderPosition, setSliderPosition] = useState(50)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleMouseDown = () => {
    setIsDragging(true)
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
    setSliderPosition(percentage)
  }

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging || !containerRef.current) return

    const rect = containerRef.current.getBoundingClientRect()
    const x = e.touches[0].clientX - rect.left
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
    setSliderPosition(percentage)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleTouchMove)
      document.addEventListener('touchend', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleMouseUp)
    }
  }, [isDragging])

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h3 className="text-sm font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wide mb-2">
            LOSSLESS COMPRESSION TECHNOLOGY
          </h3>
          <h2 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            Can You Tell the Difference?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Move the slider to compare the compressed image with the original.
            <br />
            The file size is reduced by more than 85% with no visible quality loss!
          </p>
        </div>

        <div className="max-w-4xl mx-auto select-none">
          <div
            ref={containerRef}
            className="relative overflow-hidden rounded-2xl shadow-2xl bg-white dark:bg-gray-800"
            style={{ aspectRatio: '16/10' }}
          >
            {/* After Image (Right side - compressed) */}
            <img
              src={afterImage}
              alt="Compressed"
              className="absolute inset-0 w-full h-full object-cover select-none"
              style={{
                clipPath: `inset(0 0 0 ${sliderPosition}%)`
              }}
            />
            
            {/* Before Image (Left side - original) */}
            <img
              src={beforeImage}
              alt="Original"
              className="absolute inset-0 w-full h-full object-cover"
              style={{
                clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`
              }}
            />

            {/* Slider */}
            <div
              className="absolute top-0 bottom-0 w-1 bg-white dark:bg-gray-300 shadow-2xl cursor-ew-resize flex items-center justify-center z-10"
              style={{ left: `${sliderPosition}%`, transform: 'translateX(-50%)' }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleMouseDown}
            >
              {/* Vertical line */}
              <div className="absolute top-0 bottom-0 w-1 bg-white dark:bg-gray-300 shadow-lg"></div>

              {/* Handle */}
              <div className="w-10 h-10 bg-white dark:bg-gray-700 rounded-full shadow-2xl flex items-center justify-center border-2 border-gray-200 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500 transition-colors">
                {/* Left arrow */}
                <div className="absolute left-1 w-0 h-0 border-t-2 border-b-2 border-r-4 border-transparent border-r-gray-400 dark:border-r-gray-300"></div>
                {/* Right arrow */}
                <div className="absolute right-1 w-0 h-0 border-t-2 border-b-2 border-l-4 border-transparent border-l-gray-400 dark:border-l-gray-300"></div>
              </div>
            </div>

            {/* Labels */}
            <div className="absolute bottom-6 left-6 bg-black/80 text-white px-4 py-3 rounded-lg backdrop-blur-sm">
              <div className="text-sm font-bold tracking-wide">{beforeLabel}</div>
              <div className="text-xs opacity-90 mt-1">{beforeSize}</div>
            </div>

            <div className="absolute top-6 right-6 bg-blue-600/90 text-white px-4 py-3 rounded-lg backdrop-blur-sm">
              <div className="text-sm font-bold tracking-wide">{afterLabel}</div>
              <div className="text-xs opacity-90 mt-1">{afterSize}</div>
            </div>
          </div>

          {/* Curved arrows and annotations */}
          <div className="relative mt-12">
            <div className="absolute left-1/4 -top-8 transform -translate-x-1/2">
              <div className="flex flex-col items-center">
                <svg width="60" height="40" viewBox="0 0 60 40" className="text-blue-500 mb-2">
                  <path
                    d="M10 35 Q30 5 50 35"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                    markerEnd="url(#arrowhead-blue)"
                  />
                  <defs>
                    <marker id="arrowhead-blue" markerWidth="10" markerHeight="7"
                     refX="9" refY="3.5" orient="auto">
                      <polygon points="0 0, 10 3.5, 0 7" fill="currentColor" />
                    </marker>
                  </defs>
                </svg>
                <div className="bg-blue-500 dark:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg">
                  Darker places stay intact
                </div>
              </div>
            </div>

            <div className="absolute right-1/4 -top-8 transform translate-x-1/2">
              <div className="flex flex-col items-center">
                <svg width="60" height="40" viewBox="0 0 60 40" className="text-green-500 dark:text-green-400 mb-2">
                  <path
                    d="M50 35 Q30 5 10 35"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                    markerEnd="url(#arrowhead-green)"
                  />
                  <defs>
                    <marker id="arrowhead-green" markerWidth="10" markerHeight="7"
                     refX="9" refY="3.5" orient="auto">
                      <polygon points="0 0, 10 3.5, 0 7" fill="currentColor" />
                    </marker>
                  </defs>
                </svg>
                <div className="bg-green-500 dark:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg">
                  Tiniest details are still there
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

// Default export with sample images
export default function DefaultImageComparison() {
  return (
    <ImageComparison
      beforeImage="/images/sample-original.jpg"
      afterImage="/images/sample-compressed.jpg"
      beforeLabel="ORIGINAL"
      afterLabel="COMPRESSED"
      beforeSize="2.1 MB"
      afterSize="312 KB"
    />
  )
}
