# CompressHub API 文档

## 概述

CompressHub 提供完整的 RESTful API，支持用户认证、文件压缩、订阅管理等功能。

## 认证

所有API请求都需要通过会话cookie或API密钥进行认证。

### 会话认证
通过登录获得的会话cookie自动包含在请求中。

### API密钥认证 (Pro用户)
在请求头中包含API密钥：
```
Authorization: Bearer your_api_key
```

## 基础URL
```
https://yourdomain.com/api
```

## 认证端点

### 用户登录
```http
POST /auth/signin
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**响应:**
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "membership_level": "free",
    "points": 50
  }
}
```

### 用户登出
```http
POST /auth/signout
```

### 获取当前用户
```http
GET /auth/me
```

## 压缩端点

### 图片压缩
```http
POST /compress/image
Content-Type: multipart/form-data

file: [图片文件]
quality: 0.8
format: "jpeg"
removeMetadata: true
```

**响应:** 压缩后的图片文件

**响应头:**
- `X-Original-Size`: 原始文件大小(MB)
- `X-Compressed-Size`: 压缩后大小(MB)
- `X-Compression-Ratio`: 压缩比例(%)
- `X-Processing-Time`: 处理时间(ms)

### 视频压缩
```http
POST /compress/video
Content-Type: multipart/form-data

file: [视频文件]
quality: 23
format: "mp4"
maxWidth: 1920
maxHeight: 1080
```

### GIF压缩
```http
POST /compress/gif
Content-Type: multipart/form-data

file: [GIF文件]
quality: 0.8
colors: 256
frameRate: 15
```

## 用户管理端点

### 获取用户资料
```http
GET /user/profile
```

**响应:**
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "username": "username",
    "membership_level": "pro",
    "membership_status": "active",
    "points": 0,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 更新用户资料
```http
PUT /user/profile
Content-Type: application/json

{
  "username": "new_username",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

### 获取使用统计
```http
GET /user/usage
```

**响应:**
```json
{
  "success": true,
  "usage": {
    "today": {
      "compressions_count": 5,
      "api_calls_count": 10,
      "total_file_size_mb": 25.5
    },
    "summary": {
      "totalCompressions": 150,
      "totalOriginalSizeMb": 1024.5,
      "totalCompressedSizeMb": 512.3,
      "totalSavingsMb": 512.2,
      "averageCompressionRatio": 50.1
    }
  }
}
```

## 订阅管理端点

### 获取订阅计划
```http
GET /subscription/plans
```

**响应:**
```json
{
  "success": true,
  "plans": [
    {
      "id": 1,
      "name": "Free",
      "price": 0,
      "currency": "USD",
      "billing_period": "monthly",
      "features": ["50 compressions per month", "10MB max file size"],
      "max_compressions_per_month": 50,
      "max_file_size_mb": 10
    },
    {
      "id": 2,
      "name": "Pro Monthly",
      "price": 9.00,
      "currency": "USD",
      "billing_period": "monthly",
      "features": ["Unlimited compressions", "200MB max file size"],
      "max_compressions_per_month": -1,
      "max_file_size_mb": 200
    }
  ]
}
```

### 创建订阅
```http
POST /subscription/subscribe
Content-Type: application/json

{
  "planId": 2,
  "successUrl": "https://yourdomain.com/success",
  "cancelUrl": "https://yourdomain.com/cancel"
}
```

**响应:**
```json
{
  "success": true,
  "checkoutUrl": "https://checkout.stripe.com/...",
  "sessionId": "cs_..."
}
```

### 获取订阅状态
```http
GET /subscription/status
```

**响应:**
```json
{
  "success": true,
  "user": {
    "membershipLevel": "pro",
    "membershipStatus": "active",
    "membershipEndDate": "2024-12-31T23:59:59Z",
    "isMembershipActive": true,
    "membershipDaysLeft": 30
  },
  "usage": {
    "monthly": {
      "compressions": 150,
      "apiCalls": 500
    }
  }
}
```

## 错误处理

所有API端点使用标准HTTP状态码：

- `200` - 成功
- `400` - 请求错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求过于频繁
- `500` - 服务器错误

**错误响应格式:**
```json
{
  "error": "错误描述信息"
}
```

## 速率限制

### 免费用户
- 压缩API: 每分钟10次请求
- 其他API: 每分钟30次请求

### Pro用户
- 压缩API: 每分钟100次请求
- 其他API: 每分钟300次请求

## 文件大小限制

### 免费用户
- 最大文件大小: 10MB
- 支持格式: PNG, JPEG, WebP

### Pro用户
- 最大文件大小: 200MB
- 支持格式: 所有图片和视频格式

## SDK和示例

### JavaScript/Node.js 示例
```javascript
// 压缩图片
const formData = new FormData();
formData.append('file', imageFile);
formData.append('quality', '0.8');
formData.append('format', 'jpeg');

const response = await fetch('/api/compress/image', {
  method: 'POST',
  body: formData
});

const compressedBlob = await response.blob();
```

### Python 示例
```python
import requests

# 压缩图片
files = {'file': open('image.jpg', 'rb')}
data = {'quality': '0.8', 'format': 'jpeg'}

response = requests.post(
    'https://yourdomain.com/api/compress/image',
    files=files,
    data=data,
    headers={'Authorization': 'Bearer your_api_key'}
)

with open('compressed.jpg', 'wb') as f:
    f.write(response.content)
```

### cURL 示例
```bash
# 压缩图片
curl -X POST \
  -H "Authorization: Bearer your_api_key" \
  -F "file=@image.jpg" \
  -F "quality=0.8" \
  -F "format=jpeg" \
  https://yourdomain.com/api/compress/image \
  --output compressed.jpg
```

## Webhook事件

### 订阅事件
当用户订阅状态发生变化时，系统会发送webhook事件：

```json
{
  "event": "subscription.updated",
  "user_id": "uuid",
  "subscription": {
    "status": "active",
    "plan": "pro",
    "end_date": "2024-12-31T23:59:59Z"
  }
}
```

## 支持

如需API支持，请：
- 查看API文档
- 提交技术支持请求
- 联系开发团队
