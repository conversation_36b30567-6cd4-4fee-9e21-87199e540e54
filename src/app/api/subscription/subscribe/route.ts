import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { membershipOperations, orderOperations } from '@/lib/database'
import { z } from 'zod'
import { createCheckoutSession } from '@/lib/stripe'

const subscribeSchema = z.object({
  planId: z.number(),
  successUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional()
})

export async function POST(request: NextRequest) {
  try {
    const user = await auth.getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { planId, successUrl, cancelUrl } = subscribeSchema.parse(body)

    // Get the membership plan
    const plan = await membershipOperations.getMembershipLevelById(planId)
    if (!plan) {
      return NextResponse.json(
        { error: 'Invalid subscription plan' },
        { status: 400 }
      )
    }

    // Skip payment for free plan
    if (plan.price === 0) {
      return NextResponse.json({
        success: true,
        message: 'Free plan activated'
      })
    }

    // Create order record
    const orderNumber = await orderOperations.generateOrderNumber()
    const order = await orderOperations.createOrder({
      user_id: user.id,
      order_number: orderNumber,
      membership_level_id: plan.id,
      amount: plan.price,
      currency: plan.currency,
      status: 'pending',
      payment_method: 'stripe'
    })

    if (!order) {
      return NextResponse.json(
        { error: 'Failed to create order' },
        { status: 500 }
      )
    }

    // Determine the correct Stripe price ID based on plan
    let stripePriceId: string
    if (plan.billing_period === 'yearly') {
      stripePriceId = process.env.STRIPE_PRICE_ID_PRO_YEARLY || 'price_pro_yearly'
    } else {
      stripePriceId = process.env.STRIPE_PRICE_ID_PRO_MONTHLY || 'price_pro_monthly'
    }

    // Create Stripe checkout session using our helper function
    const session = await createCheckoutSession({
      priceId: stripePriceId,
      userId: user.id,
      userEmail: user.email,
      orderId: order.id,
      successUrl: successUrl || `${request.nextUrl.origin}/dashboard?success=true`,
      cancelUrl: cancelUrl || `${request.nextUrl.origin}/pricing?cancelled=true`
    })

    // Update order with Stripe session ID
    await orderOperations.updateOrderStatus(order.id, 'pending', {
      stripe_payment_intent_id: session.id
    })

    return NextResponse.json({
      success: true,
      checkoutUrl: session.url,
      sessionId: session.id
    })
  } catch (error) {
    console.error('Subscribe API error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    )
  }
}
