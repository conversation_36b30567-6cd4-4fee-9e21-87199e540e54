'use client'

import { useState } from 'react'
import { Type, Move, RotateCw, <PERSON><PERSON>, Eye, EyeOff } from 'lucide-react'

export interface WatermarkConfig {
  enabled: boolean
  text: string
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
  opacity: number
  fontSize: number
  color: string
  rotation: number
  offsetX: number
  offsetY: number
}

interface WatermarkSettingsProps {
  config: WatermarkConfig
  onChange: (config: WatermarkConfig) => void
  className?: string
}

export function WatermarkSettings({ config, onChange, className = '' }: WatermarkSettingsProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const updateConfig = (updates: Partial<WatermarkConfig>) => {
    onChange({ ...config, ...updates })
  }

  const positions = [
    { value: 'top-left', label: 'Top Left' },
    { value: 'top-right', label: 'Top Right' },
    { value: 'bottom-left', label: 'Bottom Left' },
    { value: 'bottom-right', label: 'Bottom Right' },
    { value: 'center', label: 'Center' }
  ]

  const presetColors = [
    '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
    '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080'
  ]

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Type className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Watermark Settings
            </h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => updateConfig({ enabled: !config.enabled })}
              className={`flex items-center space-x-2 px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                config.enabled
                  ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              }`}
            >
              {config.enabled ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              <span>{config.enabled ? 'Enabled' : 'Disabled'}</span>
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
            >
              <RotateCw className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>

        {config.enabled && (
          <div className="space-y-4">
            {/* Watermark Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Watermark Text
              </label>
              <input
                type="text"
                value={config.text}
                onChange={(e) => updateConfig({ text: e.target.value })}
                placeholder="Enter watermark text..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {isExpanded && (
              <>
                {/* Position */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Position
                  </label>
                  <select
                    value={config.position}
                    onChange={(e) => updateConfig({ position: e.target.value as WatermarkConfig['position'] })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {positions.map((pos) => (
                      <option key={pos.value} value={pos.value}>
                        {pos.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Opacity */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Opacity: {Math.round(config.opacity * 100)}%
                  </label>
                  <input
                    type="range"
                    min={0}
                    max={1}
                    step={0.1}
                    value={config.opacity}
                    onChange={(e) => updateConfig({ opacity: Number(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                </div>

                {/* Font Size */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Font Size: {config.fontSize}px
                  </label>
                  <input
                    type="range"
                    min={12}
                    max={72}
                    value={config.fontSize}
                    onChange={(e) => updateConfig({ fontSize: Number(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                </div>

                {/* Color */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Color
                  </label>
                  <div className="flex items-center space-x-2 mb-2">
                    <input
                      type="color"
                      value={config.color}
                      onChange={(e) => updateConfig({ color: e.target.value })}
                      className="w-8 h-8 rounded border border-gray-300 dark:border-gray-600 cursor-pointer"
                    />
                    <input
                      type="text"
                      value={config.color}
                      onChange={(e) => updateConfig({ color: e.target.value })}
                      className="flex-1 px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
                    />
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {presetColors.map((color) => (
                      <button
                        key={color}
                        onClick={() => updateConfig({ color })}
                        className={`w-6 h-6 rounded border-2 ${
                          config.color === color
                            ? 'border-blue-500'
                            : 'border-gray-300 dark:border-gray-600'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>

                {/* Rotation */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Rotation: {config.rotation}°
                  </label>
                  <input
                    type="range"
                    min={-45}
                    max={45}
                    value={config.rotation}
                    onChange={(e) => updateConfig({ rotation: Number(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  />
                </div>

                {/* Offset */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Offset X: {config.offsetX}px
                    </label>
                    <input
                      type="range"
                      min={-100}
                      max={100}
                      value={config.offsetX}
                      onChange={(e) => updateConfig({ offsetX: Number(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Offset Y: {config.offsetY}px
                    </label>
                    <input
                      type="range"
                      min={-100}
                      max={100}
                      value={config.offsetY}
                      onChange={(e) => updateConfig({ offsetY: Number(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  </div>
                </div>

                {/* Reset Button */}
                <button
                  onClick={() => updateConfig({
                    text: 'Image Video Compress',
                    position: 'bottom-right',
                    opacity: 0.7,
                    fontSize: 24,
                    color: '#FFFFFF',
                    rotation: 0,
                    offsetX: 0,
                    offsetY: 0
                  })}
                  className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Reset to Default
                </button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Utility function to apply watermark to canvas
export function applyWatermarkToCanvas(
  canvas: HTMLCanvasElement,
  config: WatermarkConfig
): void {
  if (!config.enabled || !config.text.trim()) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // Save current context state
  ctx.save()

  // Set font and style
  ctx.font = `${config.fontSize}px Arial`
  ctx.fillStyle = config.color
  ctx.globalAlpha = config.opacity

  // Measure text
  const textMetrics = ctx.measureText(config.text)
  const textWidth = textMetrics.width
  const textHeight = config.fontSize

  // Calculate position
  let x: number, y: number

  switch (config.position) {
    case 'top-left':
      x = 20 + config.offsetX
      y = 20 + textHeight + config.offsetY
      break
    case 'top-right':
      x = canvas.width - textWidth - 20 + config.offsetX
      y = 20 + textHeight + config.offsetY
      break
    case 'bottom-left':
      x = 20 + config.offsetX
      y = canvas.height - 20 + config.offsetY
      break
    case 'bottom-right':
      x = canvas.width - textWidth - 20 + config.offsetX
      y = canvas.height - 20 + config.offsetY
      break
    case 'center':
      x = (canvas.width - textWidth) / 2 + config.offsetX
      y = (canvas.height + textHeight) / 2 + config.offsetY
      break
    default:
      x = canvas.width - textWidth - 20 + config.offsetX
      y = canvas.height - 20 + config.offsetY
  }

  // Apply rotation if needed
  if (config.rotation !== 0) {
    ctx.translate(x + textWidth / 2, y - textHeight / 2)
    ctx.rotate((config.rotation * Math.PI) / 180)
    ctx.translate(-(textWidth / 2), textHeight / 2)
    ctx.fillText(config.text, 0, 0)
  } else {
    ctx.fillText(config.text, x, y)
  }

  // Restore context state
  ctx.restore()
}

// Default watermark configuration
export const defaultWatermarkConfig: WatermarkConfig = {
  enabled: false,
  text: 'Image Video Compress',
  position: 'bottom-right',
  opacity: 0.7,
  fontSize: 24,
  color: '#FFFFFF',
  rotation: 0,
  offsetX: 0,
  offsetY: 0
}
