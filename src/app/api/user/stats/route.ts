import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { usageOperations, compressionLogOperations } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const user = await auth.getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')

    // Get total usage statistics
    const totalUsage = await usageOperations.getTotalUserUsage(user.id)

    // Get compression logs for the specified period
    const compressionStats = await compressionLogOperations.getUserCompressionStats(user.id, days)

    // Calculate additional statistics
    const totalCompressions = compressionStats.length
    const totalOriginalSize = compressionStats.reduce((sum, log) => sum + log.original_size_mb, 0)
    const totalCompressedSize = compressionStats.reduce((sum, log) => sum + log.compressed_size_mb, 0)
    const averageCompressionRatio = totalCompressions > 0
      ? compressionStats.reduce((sum, log) => sum + log.compression_ratio, 0) / totalCompressions
      : 0
    const totalSpaceSaved = totalOriginalSize - totalCompressedSize
    const averageProcessingTime = totalCompressions > 0
      ? compressionStats.reduce((sum, log) => sum + log.processing_time_ms, 0) / totalCompressions
      : 0

    // Group by file type
    const fileTypeStats = compressionStats.reduce((acc, log) => {
      const type = log.file_type.split('/')[0] // 'image', 'video', etc.
      if (!acc[type]) {
        acc[type] = {
          count: 0,
          originalSize: 0,
          compressedSize: 0,
          totalProcessingTime: 0
        }
      }
      acc[type].count++
      acc[type].originalSize += log.original_size_mb
      acc[type].compressedSize += log.compressed_size_mb
      acc[type].totalProcessingTime += log.processing_time_ms
      return acc
    }, {} as Record<string, any>)

    // Get current month usage
    const now = new Date()
    const currentMonthUsage = await usageOperations.getUserMonthlyUsage(
      user.id,
      now.getFullYear(),
      now.getMonth() + 1
    )

    const currentMonthCompressions = currentMonthUsage.reduce(
      (sum, usage) => sum + usage.compressions_count, 0
    )

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          membership_level: user.membership_level,
          membership_status: user.membership_status,
          points: user.points,
          is_membership_active: user.is_membership_active,
          membership_days_left: user.membership_days_left
        },
        period: {
          days,
          totalCompressions,
          currentMonthCompressions
        },
        usage: {
          totalCompressions: totalUsage.total_compressions,
          totalApiCalls: totalUsage.total_api_calls,
          totalFileSizeMb: totalUsage.total_file_size_mb
        },
        compression: {
          totalOriginalSizeMb: totalOriginalSize,
          totalCompressedSizeMb: totalCompressedSize,
          totalSpaceSavedMb: totalSpaceSaved,
          averageCompressionRatio: Math.round(averageCompressionRatio * 100) / 100,
          averageProcessingTimeMs: Math.round(averageProcessingTime)
        },
        fileTypes: fileTypeStats,
        limits: {
          maxFileSize: user.membership_level === 'pro' ? 200 : 10,
          maxCompressions: user.membership_level === 'pro' ? -1 : 50,
          remainingCompressions: user.membership_level === 'pro' ? -1 : user.points,
          canUseAdvancedFormats: user.membership_level === 'pro',
          canUseBatchProcessing: user.membership_level === 'pro',
          canUseAPI: user.membership_level === 'pro'
        }
      }
    })
  } catch (error) {
    console.error('Get user stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user statistics' },
      { status: 500 }
    )
  }
}