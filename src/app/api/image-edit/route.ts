import { NextRequest, NextResponse } from 'next/server'
import sharp from 'sharp'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const operations = formData.get('operations') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!operations) {
      return NextResponse.json({ error: 'No operations provided' }, { status: 400 })
    }

    const buffer = Buffer.from(await file.arrayBuffer())
    const parsedOperations = JSON.parse(operations)

    let image = sharp(buffer)

    // Apply operations
    for (const operation of parsedOperations) {
      switch (operation.type) {
        case 'resize':
          if (operation.width || operation.height) {
            image = image.resize(operation.width, operation.height, {
              fit: operation.fit || 'inside',
              withoutEnlargement: operation.withoutEnlargement !== false
            })
          }
          break

        case 'rotate':
          if (operation.angle) {
            image = image.rotate(operation.angle)
          }
          break

        case 'flip':
          if (operation.horizontal) {
            image = image.flop()
          }
          if (operation.vertical) {
            image = image.flip()
          }
          break

        case 'crop':
          if (operation.left !== undefined && operation.top !== undefined && 
              operation.width && operation.height) {
            image = image.extract({
              left: operation.left,
              top: operation.top,
              width: operation.width,
              height: operation.height
            })
          }
          break

        case 'modulate':
          const modulation: any = {}
          if (operation.brightness !== undefined) {
            modulation.brightness = 1 + (operation.brightness / 100)
          }
          if (operation.saturation !== undefined) {
            modulation.saturation = 1 + (operation.saturation / 100)
          }
          if (operation.hue !== undefined) {
            modulation.hue = operation.hue
          }
          if (Object.keys(modulation).length > 0) {
            image = image.modulate(modulation)
          }
          break

        case 'blur':
          if (operation.sigma > 0) {
            image = image.blur(operation.sigma)
          }
          break

        case 'sharpen':
          if (operation.sigma) {
            image = image.sharpen(operation.sigma, operation.flat, operation.jagged)
          }
          break

        case 'grayscale':
          if (operation.enabled) {
            image = image.grayscale()
          }
          break

        case 'negate':
          if (operation.enabled) {
            image = image.negate()
          }
          break

        case 'normalize':
          if (operation.enabled) {
            image = image.normalize()
          }
          break

        case 'gamma':
          if (operation.gamma) {
            image = image.gamma(operation.gamma)
          }
          break

        case 'tint':
          if (operation.color) {
            image = image.tint(operation.color)
          }
          break

        case 'watermark':
          if (operation.enabled && operation.text) {
            // Create text overlay using SVG
            const textSvg = `
              <svg width="${operation.width || 200}" height="${operation.height || 50}">
                <text 
                  x="${operation.x || 10}" 
                  y="${operation.y || 30}" 
                  font-family="Arial" 
                  font-size="${operation.fontSize || 24}" 
                  fill="${operation.color || '#ffffff'}"
                  opacity="${operation.opacity || 0.7}"
                  transform="rotate(${operation.rotation || 0} ${operation.x || 10} ${operation.y || 30})"
                >
                  ${operation.text}
                </text>
              </svg>
            `
            
            const textBuffer = Buffer.from(textSvg)
            image = image.composite([{
              input: textBuffer,
              top: operation.top || 0,
              left: operation.left || 0,
              blend: 'over'
            }])
          }
          break

        default:
          console.warn(`Unknown operation type: ${operation.type}`)
      }
    }

    // Get output format from request or default to original format
    const outputFormat = formData.get('format') as string || 'png'
    const quality = parseInt(formData.get('quality') as string) || 90

    // Convert to requested format
    let outputBuffer: Buffer
    switch (outputFormat.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        outputBuffer = await image.jpeg({ quality }).toBuffer()
        break
      case 'png':
        outputBuffer = await image.png({ quality }).toBuffer()
        break
      case 'webp':
        outputBuffer = await image.webp({ quality }).toBuffer()
        break
      case 'avif':
        outputBuffer = await image.avif({ quality }).toBuffer()
        break
      case 'tiff':
        outputBuffer = await image.tiff({ quality }).toBuffer()
        break
      default:
        outputBuffer = await image.png({ quality }).toBuffer()
    }

    // Get metadata
    const metadata = await sharp(outputBuffer).metadata()

    return new NextResponse(outputBuffer, {
      headers: {
        'Content-Type': `image/${outputFormat}`,
        'Content-Length': outputBuffer.length.toString(),
        'X-Image-Width': metadata.width?.toString() || '',
        'X-Image-Height': metadata.height?.toString() || '',
        'X-Image-Format': metadata.format || '',
        'X-Original-Size': buffer.length.toString(),
        'X-Compressed-Size': outputBuffer.length.toString(),
        'X-Compression-Ratio': ((1 - outputBuffer.length / buffer.length) * 100).toFixed(2)
      }
    })

  } catch (error) {
    console.error('Image editing error:', error)
    return NextResponse.json(
      { error: 'Failed to process image', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Image editing API',
    supportedFormats: ['jpeg', 'jpg', 'png', 'webp', 'avif', 'tiff'],
    supportedOperations: [
      'resize', 'rotate', 'flip', 'crop', 'modulate', 'blur', 'sharpen',
      'grayscale', 'negate', 'normalize', 'gamma', 'tint', 'watermark'
    ]
  })
}
