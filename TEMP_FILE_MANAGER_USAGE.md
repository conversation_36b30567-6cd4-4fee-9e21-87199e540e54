# TempFileManager 使用指南

## 概述

`TempFileManager` 是一个专为无服务器环境（如 Vercel）设计的临时文件管理工具，提供了安全、可靠的临时文件操作功能。

## 主要特性

- ✅ **自动清理**：确保所有创建的临时文件都能被正确清理
- ✅ **Vercel 兼容**：专门优化了 Vercel 无服务器环境
- ✅ **错误处理**：优雅处理文件不存在等常见错误
- ✅ **唯一文件名**：使用 UUID 避免文件名冲突
- ✅ **批量操作**：支持批量创建和清理文件

## 基本使用

### 1. 创建 TempFileManager 实例

```typescript
import { createTempManager } from '@/utils/tempFileManager'

export async function POST(request: NextRequest) {
  const tempManager = createTempManager()
  
  try {
    // 你的处理逻辑
  } finally {
    // 确保清理临时文件
    await tempManager.cleanup()
  }
}
```

### 2. 创建临时文件路径

```typescript
// 创建带扩展名的临时文件
const inputPath = tempManager.createTempPath('.mp4', 'input_video')
const outputPath = tempManager.createTempPath('.mp4', 'output_video')

// 创建调色板文件
const palettePath = tempManager.createTempPath('.png', 'palette')
```

### 3. 写入和读取文件

```typescript
// 写入文件
const buffer = Buffer.from(await file.arrayBuffer())
await tempManager.writeFile(inputPath, buffer)

// 读取文件
const compressedBuffer = await tempManager.readFile(outputPath)
```

### 4. 文件操作

```typescript
// 检查文件是否存在
const exists = await tempManager.exists(filePath)

// 获取文件大小
const size = await tempManager.getFileSize(filePath)

// 删除单个文件
await tempManager.deleteFile(filePath)

// 清理所有文件
await tempManager.cleanup()
```

## 在 API 路由中的完整示例

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { createTempManager } from '@/utils/tempFileManager'
import ffmpeg from 'fluent-ffmpeg'

export async function POST(request: NextRequest) {
  const tempManager = createTempManager()
  
  try {
    // 获取上传的文件
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    // 创建临时文件路径
    const inputPath = tempManager.createTempPath('.mp4', 'input')
    const outputPath = tempManager.createTempPath('.mp4', 'output')
    
    // 保存上传的文件
    const buffer = Buffer.from(await file.arrayBuffer())
    await tempManager.writeFile(inputPath, buffer)
    
    // 使用 FFmpeg 处理文件
    await new Promise<void>((resolve, reject) => {
      ffmpeg(inputPath)
        .output(outputPath)
        .on('end', resolve)
        .on('error', reject)
        .run()
    })
    
    // 读取处理后的文件
    const compressedBuffer = await tempManager.readFile(outputPath)
    
    // 返回结果
    return new NextResponse(compressedBuffer, {
      headers: {
        'Content-Type': 'video/mp4',
        'Content-Length': compressedBuffer.length.toString()
      }
    })
    
  } catch (error) {
    console.error('Processing error:', error)
    return NextResponse.json(
      { error: 'Processing failed' },
      { status: 500 }
    )
  } finally {
    // 确保清理所有临时文件
    await tempManager.cleanup()
  }
}
```

## Vercel 工具函数

### VercelTempUtils

```typescript
import { VercelTempUtils } from '@/utils/tempFileManager'

// 检查是否在 Vercel 环境中
const isVercel = VercelTempUtils.isVercelEnvironment()

// 获取适合的临时目录
const tempDir = VercelTempUtils.getTempDir() // 在 Vercel 上返回 '/tmp'

// 创建临时文件
const tempPath = await VercelTempUtils.createTempFile(buffer, '.mp4')

// 安全删除文件
await VercelTempUtils.safeDelete(tempPath)

// 批量删除文件
await VercelTempUtils.safeDeleteMultiple([path1, path2, path3])
```

## 最佳实践

### 1. 总是使用 finally 块清理

```typescript
export async function POST(request: NextRequest) {
  const tempManager = createTempManager()
  
  try {
    // 处理逻辑
  } finally {
    await tempManager.cleanup() // 确保清理
  }
}
```

### 2. 使用有意义的前缀

```typescript
// 好的做法
const inputPath = tempManager.createTempPath('.mp4', 'user_video_input')
const outputPath = tempManager.createTempPath('.mp4', 'compressed_output')
const palettePath = tempManager.createTempPath('.png', 'gif_palette')

// 避免
const path1 = tempManager.createTempPath('.mp4')
const path2 = tempManager.createTempPath('.mp4')
```

### 3. 错误处理

```typescript
try {
  await tempManager.writeFile(path, buffer)
} catch (error) {
  console.error('Failed to write temp file:', error)
  // 处理错误，但不需要手动清理，finally 块会处理
}
```

### 4. 大文件处理

```typescript
// 对于大文件，检查文件大小
const fileSize = await tempManager.getFileSize(inputPath)
if (fileSize > 100 * 1024 * 1024) { // 100MB
  throw new Error('File too large for processing')
}
```

## 迁移指南

### 从原始 fs 操作迁移

**之前：**
```typescript
import { promises as fs } from 'fs'
import path from 'path'

const tempDir = path.join(process.cwd(), 'temp')
await fs.mkdir(tempDir, { recursive: true })
const inputPath = path.join(tempDir, `${uuid()}_input.mp4`)
await fs.writeFile(inputPath, buffer)

// 清理
try {
  await fs.unlink(inputPath)
} catch (error) {
  console.warn('Cleanup failed:', error)
}
```

**现在：**
```typescript
import { createTempManager } from '@/utils/tempFileManager'

const tempManager = createTempManager()
const inputPath = tempManager.createTempPath('.mp4', 'input')
await tempManager.writeFile(inputPath, buffer)

// 清理
await tempManager.cleanup() // 自动处理所有错误
```

## 故障排除

### 常见问题

1. **文件未被清理**
   - 确保在 `finally` 块中调用 `cleanup()`
   - 检查是否有未捕获的异常

2. **权限错误**
   - 在 Vercel 上确保使用 `/tmp` 目录
   - 检查 `VercelTempUtils.getTempDir()` 返回值

3. **文件不存在错误**
   - 使用 `exists()` 方法检查文件是否存在
   - `cleanup()` 方法会自动处理文件不存在的情况

### 调试

```typescript
// 启用调试日志
const tempManager = createTempManager()
console.log('Temp directory:', tempManager.getTempDir())

// 检查创建的文件
const filePath = tempManager.createTempPath('.txt', 'debug')
console.log('Created temp file path:', filePath)

// 验证文件操作
await tempManager.writeFile(filePath, Buffer.from('test'))
const exists = await tempManager.exists(filePath)
console.log('File exists:', exists)
```

## 性能考虑

- **内存使用**：大文件会占用内存，考虑流式处理
- **并发限制**：Vercel 有并发函数限制，避免创建过多临时文件
- **清理时机**：及时清理不再需要的文件，不要等到函数结束
