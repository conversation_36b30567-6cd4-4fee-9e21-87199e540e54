'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Zap, 
  Shield, 
  Cloud, 
  Star, 
  CheckCircle, 
  ArrowRight,
  Image,
  Video,
  Download,
  Gauge
} from 'lucide-react'

const features = [
  {
    icon: Zap,
    title: 'Lightning Fast',
    description: 'Advanced algorithms compress files in seconds, not minutes. Optimized for speed without compromising quality.',
    color: 'from-yellow-400 to-orange-500',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    borderColor: 'border-yellow-200 dark:border-yellow-700',
    textColor: 'text-yellow-600 dark:text-yellow-400'
  },
  {
    icon: Shield,
    title: 'Privacy First',
    description: 'All processing happens locally in your browser. Your files never leave your device, ensuring complete privacy.',
    color: 'from-green-400 to-emerald-500',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    borderColor: 'border-green-200 dark:border-green-700',
    textColor: 'text-green-600 dark:text-green-400'
  },
  {
    icon: Gauge,
    title: 'Smart Compression',
    description: 'AI-powered algorithms automatically optimize compression settings for the best size-to-quality ratio.',
    color: 'from-blue-400 to-cyan-500',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    borderColor: 'border-blue-200 dark:border-blue-700',
    textColor: 'text-blue-600 dark:text-blue-400'
  },
  {
    icon: Image,
    title: 'Multiple Formats',
    description: 'Support for JPEG, PNG, WebP images and MP4, WebM videos. Convert between formats while compressing.',
    color: 'from-purple-400 to-pink-500',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    borderColor: 'border-purple-200 dark:border-purple-700',
    textColor: 'text-purple-600 dark:text-purple-400'
  },
  {
    icon: Download,
    title: 'Batch Processing',
    description: 'Upload and compress multiple files at once. Save time with bulk operations and batch downloads.',
    color: 'from-indigo-400 to-blue-500',
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
    borderColor: 'border-indigo-200 dark:border-indigo-700',
    textColor: 'text-indigo-600 dark:text-indigo-400'
  },
  {
    icon: Star,
    title: 'Premium Quality',
    description: 'Maintain visual quality while achieving up to 90% size reduction. Perfect balance of compression and clarity.',
    color: 'from-rose-400 to-red-500',
    bgColor: 'bg-rose-50 dark:bg-rose-900/20',
    borderColor: 'border-rose-200 dark:border-rose-700',
    textColor: 'text-rose-600 dark:text-rose-400'
  }
]

// Floating wireframe decorations
const FloatingWireframes = ({ cardIndex, isActive }: { cardIndex: number; isActive: boolean }) => {
  return (
    <>
      {cardIndex === 1 && (
        <div className="absolute -left-12 top-1/4 hidden lg:block">
          <svg
            width="40"
            height="80"
            viewBox="0 0 40 80"
            className={`transition-opacity duration-500 ${isActive ? 'opacity-60' : 'opacity-30'}`}
          >
            <path
              d="M10 20 Q20 40 10 60"
              stroke="#3B82F6"
              strokeWidth="1.5"
              fill="none"
              strokeDasharray="4 2"
            />
          </svg>
        </div>
      )}

      {cardIndex === 2 && (
        <div className="absolute -right-12 top-1/3 hidden lg:block">
          <svg
            width="40"
            height="60"
            viewBox="0 0 40 60"
            className={`transition-opacity duration-500 ${isActive ? 'opacity-60' : 'opacity-30'}`}
          >
            <path
              d="M30 15 Q20 30 30 45"
              stroke="#8B5CF6"
              strokeWidth="1.5"
              fill="none"
              strokeDasharray="3 2"
            />
          </svg>
        </div>
      )}
    </>
  )
}

export function Features() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-slate-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-20">
        <svg className="absolute top-0 left-0 w-full h-full" viewBox="0 0 1200 800">
          <defs>
            <pattern id="grid" width="80" height="80" patternUnits="userSpaceOnUse">
              <path
                d="M 80 0 L 0 0 0 80"
                fill="none"
                stroke="#E2E8F0"
                strokeWidth="0.5"
                opacity="0.3"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto relative">
        {/* Section header */}
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.h2 
            className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Why Choose <span className="text-blue-600 dark:text-blue-400">CompressHub</span>?
          </motion.h2>
          <motion.p 
            className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Professional-grade compression tools designed for developers, designers, and content creators who demand the best quality and performance.
          </motion.p>
        </motion.div>

        {/* Features grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={index}
                className="relative group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                <FloatingWireframes cardIndex={index} isActive={hoveredCard === index} />
                
                <div className={`
                  relative p-8 rounded-2xl border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-1
                  ${feature.bgColor} ${feature.borderColor}
                  ${hoveredCard === index ? 'shadow-xl -translate-y-1' : 'shadow-lg'}
                `}>
                  {/* Icon */}
                  <div className={`
                    w-16 h-16 rounded-xl bg-gradient-to-br ${feature.color} 
                    flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300
                  `}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                    {feature.description}
                  </p>

                  {/* Hover indicator */}
                  <div className={`
                    flex items-center ${feature.textColor} opacity-0 group-hover:opacity-100 
                    transition-opacity duration-300 font-medium
                  `}>
                    <span className="text-sm">Learn more</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>

                  {/* Decorative corner */}
                  <div className={`
                    absolute top-4 right-4 w-3 h-3 rounded-full bg-gradient-to-br ${feature.color} 
                    opacity-20 group-hover:opacity-40 transition-opacity duration-300
                  `} />
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div 
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.button
            className="inline-flex items-center gap-3 px-8 py-4 bg-blue-600 text-white font-bold rounded-full hover:bg-blue-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl cursor-pointer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Zap className="w-5 h-5" />
            <span>Start Compressing Now</span>
            <ArrowRight className="w-5 h-5" />
          </motion.button>
          <p className="text-sm text-gray-500 mt-4">
            No registration required • Free to use • Privacy guaranteed
          </p>
        </motion.div>
      </div>
    </section>
  )
}
